import React from 'react';
import { handleRequestFailed, translation } from '../../common/util';
import { useDispatch, useSelector } from 'react-redux';
import { useLazyGetAllInspectionsQuery } from '../../services/session';
import { Segmented, Switch } from 'antd';
import _ from 'lodash';
import { setStayAtLeatestEnabled, setViewInspectionViewMode } from '../../actions/setting';


const ViewInspectionLayout = (props) => {
  const dispatch = useDispatch();

  const {
    goldenProdName,
    handleRedirect,
    hideRealTimeToggle,
    sessionId,
    step,
    setSelectedFeatureId,
    setSelectedLineItem,
  } = props;

  const [getInspections] = useLazyGetAllInspectionsQuery();

  const isStayAtLeatestEnabled = useSelector((state) => state.setting.isStayAtLeatestEnabled);
  const viewInspectionViewMode = useSelector((state) => state.setting.viewInspectionViewMode);
  const curRunningIpcSessionIds =  useSelector((state) => state.setting.curRunningIpcSessionIds);

  return (
    <div className='flex h-full w-full'>
    <div
      className={`flex flex-col items-start gap-4 flex-1 self-stretch
        rounded-[2px] bg-[#131313]
      `}
    >
      <div className='flex h-12 items-center py-2 self-stretch justify-between pt-2 pr-8 pb-4 pl-8 '>
        <div className='flex h-12 items-center gap-6 self-stretch'>
          <div className='flex h-12 items-center gap-3 self-stretch'>
            <div
              className='flex w-6 h-6 cursor-pointer justify-center items-center gap-2.5'
              onClick={() => {
                window.location.href = !hideRealTimeToggle ? '/aoi/live-dashboard' : `/aoi/worklist?selectedSessionIdFromHome=${sessionId}`;
              }}
            >
              <img src='/img/icn/icn_arrowLeft_white.svg' alt='back' className='w-[8px] h-[16px] shrink' />
            </div>
            <div className='flex w-5 h-5 p-0.5 justify-center items-center gap-2.5'>
              <img src='/img/icn/icn_meter_white.svg' alt='meter' className='w-5 h-[15px] shrink' />
            </div>
            <span className='font-source text-[20px] font-semibold'>
              {`${translation(`viewInspection.${hideRealTimeToggle ? 'inspectionView' : 'liveInspectionView'}`)}: ${goldenProdName}`}
            </span>
          </div>
          <Segmented
            value={viewInspectionViewMode === 'single' ? translation('viewInspection.single') : translation('viewInspection.inspectionSummary')}
            options={[
              translation('viewInspection.single'),
              translation('viewInspection.inspectionSummary'),
            ]}
            onChange={(value) => {
              setSelectedFeatureId(null);
              setSelectedLineItem(null);
              dispatch(setViewInspectionViewMode(value === translation('viewInspection.single') ? 'single' : 'review'));
            }}
          />
        </div>
        { !hideRealTimeToggle &&
          <div className='flex gap-2 h-12 self-stretch items-center'>
            <Switch
              checked={isStayAtLeatestEnabled}
              onChange={async (checked) => {
                dispatch(setStayAtLeatestEnabled(checked));
                if (checked) {
                  // redirect to the latest inspection
                  // const res = await getInspections({ is_golden: false, limit: 1, ipc_session_id: _.first(curRunningIpcSessionIds) });
                  const res = await getInspections({ limit: 1 });
                  if (_.get(res, 'error')) {
                    handleRequestFailed('getInspections', _.get(res, 'error'));
                    return;
                  }
                  // window.location.href = `/aoi/view-inference/${_.get(res, 'data.0.golden_product_id')}/${_.get(res, 'data.0.product_id')}`;
                  handleRedirect(`/aoi/view-inference/${_.get(res, 'data.data.0.golden_product_id')}/${_.get(res, 'data.data.0.product_id')}${_.isInteger(Number(step)) ? `?step=${step}` : ''}`);
                }
              }}
            />
            <span className='font-source text-[12px] font-normal'>
              {translation('viewInspection.alwaysDisplayLatestScan')}
            </span>
          </div>
        }
      </div>
      {/* <div className='w-full h-[1px] bg-[#333]' /> */}
      {props.children}
    </div>
    </div>
  );
};

export default ViewInspectionLayout;