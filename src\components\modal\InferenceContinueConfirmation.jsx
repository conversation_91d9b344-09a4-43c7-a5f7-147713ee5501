import React, { useEffect, useRef, useState } from 'react';
import { activeGreen, DarkButton, DarkModal, defaultGreen } from '../../common/darkModeComponents';
import { handleRequestFailed, translation } from '../../common/util';
import _ from 'lodash';
import { usePostInferenceActionMutation, useStopInferenceMutation } from '../../services/product';
import { useLazyGetAllInspectionsQuery } from '../../services/session';
import { aoiAlert } from '../../common/alert';
import { useDispatch } from 'react-redux';
import { setCurRunningIpcSessionIds, setIsInferenceRunning } from '../../actions/setting';


const InferenceContinueConfirmation = (props) => {
  const dispatch = useDispatch();

  const {
    isOpened,
    setIsOpened,
    handleRunInference,
    handleRedirect,
  } = props;

  const pollNewInspection = useRef();
  const prevLatestProductId = useRef(null);

  const [stopInference] = useStopInferenceMutation();
  const [getSession] = useLazyGetAllInspectionsQuery();
  const [inferenceTrigger] = usePostInferenceActionMutation();

  const [isLoading, setIsLoading] = useState(false);

  const initPollingForNewInspection = async () => {
    if (pollNewInspection.current) {
      clearInterval(pollNewInspection.current);
      pollNewInspection.current = null;
    }

    const res = await getSession({ is_golden: false, limit: 1 });

    if (res.error) {
      handleRequestFailed('getInitInspection', res.error);
      return;
    }

    prevLatestProductId.current = _.get(res, 'data.data[0].product_id', null);

    pollNewInspection.current = setInterval(async () => {
      const latestInspcRes = await getSession({ is_golden: false, limit: 1 });
      if (latestInspcRes.error || !_.get(latestInspcRes, 'data.data[0].product_id', null)) {
        handleRequestFailed('getLatestInspection', latestInspcRes.error);
        clearInterval(pollNewInspection.current);
        pollNewInspection.current = null;
        return;
      }
      if (prevLatestProductId.current !== _.get(latestInspcRes, 'data.data[0].product_id', null)) {
        // new inspection done, whether this is triggered by plc or the web client side we redirect to the view-inference page either way
        const { golden_product_id, product_id } = _.get(latestInspcRes, 'data.data[0]', {});
        clearInterval(pollNewInspection.current);
        pollNewInspection.current = null;
        window.location.href = `/aoi/view-inference/${golden_product_id}/${product_id}?step-0`;
      }
      // wait for new inspection
    }, 500);
  };

  useEffect(() => {
    // start polling session for new inspection
    if (pollNewInspection.current) {
      clearInterval(pollNewInspection.current);
      pollNewInspection.current = null;
    }

    if (isOpened) {
      initPollingForNewInspection();
    }

    return () => {
      if (pollNewInspection.current) {
        clearInterval(pollNewInspection.current);
        pollNewInspection.current = null;
      }
    };
  }, [isOpened]);

  return (
    <DarkModal
      primaryColor={'#57F2C4'}
      open={isOpened}
      onCancel={() => setIsOpened(false)}
      title={
        <span className='font-source text-[16px] font-semibold'>
          {translation('imageCaptureRequired.userActionRequired')}
        </span>
      }
      footer={
        <div className='flex flex-col items-start self-stretch gap-2'>
          <DarkButton
            bgColor={defaultGreen}
            hoverBgColor={activeGreen}
            borderColor={defaultGreen}
            hoverTextColor={'#333'}
            hoverBorderColor={activeGreen}
            textColor={'#333'}
            style={{ width: '100%' }}
            loading={isLoading}
            onClick={async () => {
              setIsLoading(true);
              // inference trigger in web client side then we don't need polling
              // in inference trigger in web clinet's case, the new product write and inference result write are not atomic
              // hence the getSessionInfo call after new ipc is found may not contain the correct defect count
              // we have to wait until inference trigger returns
              if (pollNewInspection.current) {
                clearInterval(pollNewInspection.current);
                pollNewInspection.current = null;
              }
              // await handleRunInference();
              const res = await inferenceTrigger();
              setIsLoading(false);
              if (res.error) {
                handleRequestFailed('inferenceTrigger', res.error);
                return;
              }
              
              const latestInspcRes = await getSession({ is_golden: false, limit: 1 });
              if (latestInspcRes.error || !_.get(latestInspcRes, 'data.data[0].product_id', null)) {
                handleRequestFailed('getLatestInspection', latestInspcRes.error);
                return;
              }
              const { golden_product_id, product_id } = _.get(latestInspcRes, 'data.data[0]', {});
              window.location.href = `/aoi/view-inference/${golden_product_id}/${product_id}?step=0`;
            }}
          >
            {translation('common.continue')}
          </DarkButton>
          <DarkButton
            bgColor={defaultGreen}
            hoverBgColor={activeGreen}
            borderColor={defaultGreen}
            hoverBorderColor={activeGreen}
            hoverTextColor={'#333'}
            textColor={'#333'}
            style={{ width: '100%' }}
            loading={isLoading}
            onClick={async () => {
              setIsLoading(true);
              await stopInference();
              setIsLoading(false);
              setIsOpened(false);
              dispatch(setIsInferenceRunning(false));
              dispatch(setCurRunningIpcSessionIds([]));
              handleRedirect('/aoi/live-dashboard');
            }}
          >
            {translation('common.stop')}
          </DarkButton>
        </div>
      }
    >
      <div className='flex py-6 px-4 flex-col items-start gap-8 self-stretch'>
        <span className='text-source text-[14px] font-normal'>
          {translation('imageCaptureRequired.inferencePositionTheNextPart')}
        </span>
      </div>
    </DarkModal>
  );
};

export default InferenceContinueConfirmation;