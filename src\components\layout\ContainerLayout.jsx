import { Spin } from 'antd';
import React from 'react';
import { useSelector } from 'react-redux';
import NotificationCol from '../common/NotificationCol';


const ContainerLayout = (props) => {
  const isContainerWindowLoadingLocked = useSelector((state) => state.setting.isContainerWindowLoadingLocked);
  const isContainerTransparentLockEnabled = useSelector((state) => state.setting.isContainerTransparentLockEnabled);

  return (
    <div
      className='flex h-[100vh] w-[100vw]'
      // style={{ background: 'var(--nav-background, linear-gradient(0deg, rgba(0, 0, 0, 0.70) 0%, rgba(0, 0, 0, 0.70) 100%), #073B4C)' }}
      style={{ background: '#131313' }}
    >
      <div className='relative w-full h-full'>
        <div className={`absolute w-full h-full ${(isContainerWindowLoadingLocked || isContainerTransparentLockEnabled) ? 'z-[10]' : 'z-[20]'}`}>
          {props.children}
        </div>
        <div className={`absolute w-full h-full ${isContainerWindowLoadingLocked ? 'z-[10001]' : 'z-[10] hidden'} bg-gray-2 bg-opacity-50 flex items-center justify-center`}>
          <Spin size='large' />
        </div>
        <div className={`absolute w-full h-full ${isContainerTransparentLockEnabled ? 'z-[10001]' : 'z-[10] hidden'}`} />
        <NotificationCol />
      </div>
    </div>
  );
};

export default ContainerLayout;