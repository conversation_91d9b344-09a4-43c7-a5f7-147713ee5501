import { <PERSON><PERSON>, ConfigProvider, Select, Spin } from 'antd';
import _ from 'lodash';
import React, { Fragment, useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { CustomCollapse, DarkButton, lightGray, primaryColor, RoundLabel } from '../../common/darkModeComponents';
import { convertBackendTimestampToMoment, getColorByStr, getInspectionCountByInspectionViewLayout, getQueryParams, handleRequestFailed, sleep, translation } from '../../common/util';
import { useGetProductByIdQuery, useRunDetectionPipelineMutation } from '../../services/product';
import { systemApi } from '../../services/system';
import MultiViewGridSelection from '../common/MultiViewGridSelection';
import ObjectContainImage from '../common/ObjectContainImage';
import MainMenuLayout from '../layout/MainMenuLayout';
import ManageBoardsLayout from '../layout/ManageBoardsLayout';
import GoldenProductInferenceStep from './GoldenProductsAnnotation';
import DetectionContinueConfirmation from '../modal/DetectionContinueConfirmation';
import { ALERT_TYPES, aoiAlert } from '../../common/alert';
import { setContainerWindowLoadingLocked } from '../../actions/setting';
import { useLazyGetAllFeaturesByProductIdAndStepQuery } from '../../services/feature';
import ProductVariationDefineDescription from '../modal/ProductVariationDefineDescription';
import RetrainConfirmation from '../modal/RetrainConfirmation';
import { retrainModelTaskPhaseType, retrainModelType, serverEndpoint } from '../../common/const';
import { useModelUpdateTriggerMutation } from '../../services/model';
import DeleteVariantComfirmation from '../modal/DeleteVariantComfirmation';
import DefineProductRegister from '../modal/DefineProductReregister';
import ProductVariationCapture from '../modal/ProductVariationCapture';
import StaticStepFeatureListViewer from '../common/viewer/StaticStepFeatureListViewer';
import { setSelectedFeatureIdInManageProduct } from '../../actions/product';


const ViewProduct = (props) => {
  const dispatch = useDispatch();

  const { search } = props.location;
  const searchParams = getQueryParams(search);
  const { productId, stepNumber } = props.match.params;
  const { featureId } = searchParams;

  const viewRef = useRef();
  const loopCheckRetrainStatus = useRef();
  const initSelectVariant = useRef(false);
  const displayCanvasRef = useRef(null);

  const [isDetectionContinueModelOpened, setIsDetectionContinueModelOpened] = useState(false);
  const [detectionImageProductId, setDetectionImageProductId] = useState('');
  const [selectedVariation, setSelectedVariation] = useState('');
  const [isRetrainComfirmationModalOpened, setIsRetrainComfirmationModalOpened] = useState(false);
  const [isDeleteVarintModalOpened, setIsDeleteVarintModalOpened] = useState(false);
  const [isRegisterConfirmationModalOpened, setIsRegisterConfirmationModalOpened] = useState(false);
  const [isVariationModalOpened, setIsVariationModalOpened] = useState(false);
  const [isProductVariationDefineDescModalOpened, setIsProductVariationDefineDescModalOpened] = useState(false);
  const [allStepsFeatures, setAllStepsFeatures] = useState({}); // { 'stepNumber': [features] }
  const [isGridViewExpanded, setIsGridViewExpanded] = useState(true);
  const [deleteTargetVariant, setDeleteTargetVariant] = useState('');

  const {
    data: systemMetadata,
  } = useSelector((state) => systemApi.endpoints.getSystemMetadata.select()(state));
  const isContainerWindowLoadingLocked = useSelector((state) => state.setting.isContainerWindowLoadingLocked);
  const curRunningIpcSessionIds = useSelector((state) => state.setting.curRunningIpcSessionIds);
  const isInferenceRunning = useSelector((state) => state.setting.isInferenceRunning);
  const selectedFeatureId = useSelector((state) => state.product.selectedfeatureIdInManageProduct);

  const [retrainTrigger] = useModelUpdateTriggerMutation();
  const { data: productInfo, isError, error, refetch: refetchProductById } = useGetProductByIdQuery(productId || _.get(searchParams, 'newProductId', ''));
  const productVariationDefineModalDisabled = useSelector((state) => state.setting.productVariationDefineModalDisabled);
  const [lazyGetAllFeatures] = useLazyGetAllFeaturesByProductIdAndStepQuery();

  const [
    runDetectionPipeline
  ] = useRunDetectionPipelineMutation();

  const handleGetAllStepsFeatures = async (systemMetadata, productId) => {
    if (_.isEmpty(systemMetadata)) return;
    const steps = _.sum(_.get(systemMetadata, 'inspection_view_layout'));
    const curResult = {};
    for (let i = 0; i < steps; i++) {
      const res = await lazyGetAllFeatures({
        product_id: productId,
        step: i,
      });
      curResult[`${i}`] = _.get(res, 'data', []);
    }
    setAllStepsFeatures(curResult);
  };

  const handleRunDetection = async (productId, productName) => {
    if (_.isEmpty(productName)) return;
    await runDetectionPipeline({ product_id: productId, variant: productName});
    await refetchProductById();
    setIsDetectionContinueModelOpened(false);
    // set state to manually trigger the image re-render
    setDetectionImageProductId(productId);
    // callback && callback();
    props.history.push(`/aoi/view-product/${productId}/0`);
  };

  const checkIfReadyForRetrain = (modelStatus) => {
    if (_.isEmpty(modelStatus)) return true; 
    // console.log(_.filter(modelStatus, (taskStatus) => !_.includes([
    //   retrainModelTaskPhaseType.failure,
    //   retrainModelTaskPhaseType.complete,
    //   retrainModelTaskPhaseType.invalid
    // ], taskStatus.phase)));
    return _.isEmpty(_.filter(modelStatus, (taskStatus) => !_.includes([
      retrainModelTaskPhaseType.failure,
      retrainModelTaskPhaseType.complete,
      retrainModelTaskPhaseType.invalid
    ], taskStatus.phase)));
  };

  const handleRetrainModel = async (startAfterSession, pid) => {
    // just in case fetch the retrain status
    let modelStatusRes;
    try {
      modelStatusRes = await fetch(`${serverEndpoint}/getModelUpdates`);
    } catch (error) {
      handleRequestFailed('getModelUpdates', error);
      return;
    }
    const modelStatus = await modelStatusRes.json();

    if (!checkIfReadyForRetrain(modelStatus)) {
      aoiAlert(translation('notification.error.serverAlreadyHasARunningRetrainTask'), ALERT_TYPES.COMMON_ERROR);
      return;
    }

    const payload = {
      model_types: [retrainModelType.defectModel, retrainModelType.heightModel],
    };
    if (_.isInteger(pid)) payload.golden_product_id = pid;

    // lock screen
    dispatch(setContainerWindowLoadingLocked(true));

    const res = await retrainTrigger(payload);
    if (_.get(res, 'error')) {
      handleRequestFailed('retrainModel', _.get(res, 'error'));
      dispatch(setContainerWindowLoadingLocked(false));
      return;
    }

    // retrain trigger success start get retrain status loop
    loopCheckRetrainStatus.current = setInterval(async () => {
      let modelStatusRes;
      try {
        modelStatusRes = await fetch(`${serverEndpoint}/getModelUpdates`);
      } catch (error) {
        handleRequestFailed('getModelUpdates', error);
        clearInterval(loopCheckRetrainStatus.current);
        loopCheckRetrainStatus.current = null;
        dispatch(setContainerWindowLoadingLocked(false));
        return;
      }
      const modelStatus = await modelStatusRes.json();
      if (checkIfReadyForRetrain(modelStatus)) {
        clearInterval(loopCheckRetrainStatus.current);
        loopCheckRetrainStatus.current = null;
        dispatch(setContainerWindowLoadingLocked(false));
        // search for the task with greatest schedule_id
        const latestTask = _.maxBy(modelStatus, (task) => task.schedule_id);
        if (_.includes([retrainModelTaskPhaseType.failure, retrainModelTaskPhaseType.invalid], _.get(latestTask, 'phase'))) {
          aoiAlert(translation('notification.error.retrainFailed'), ALERT_TYPES.COMMON_ERROR);
          return;
        }
        aoiAlert(translation('notification.success.retrainFinished'), ALERT_TYPES.COMMON_SUCCESS);
        return;
      }
    }, 1000);
  };

  const checkRetrainStatusInInit = async () => {
    let modelStatusRes;
    try {
      modelStatusRes = await fetch(`${serverEndpoint}/getModelUpdates`);
    } catch (error) {
      handleRequestFailed('getModelUpdates', error);
      return;
    }
    const modelStatus = await modelStatusRes.json();

    if (checkIfReadyForRetrain(modelStatus)) return;

    // some tasks is in progress so lock screen
    dispatch(setContainerWindowLoadingLocked(true));

    if (loopCheckRetrainStatus.current) {
      clearInterval(loopCheckRetrainStatus.current);
      loopCheckRetrainStatus.current = null;
    }

    loopCheckRetrainStatus.current = setInterval(async () => {
      let modelStatusRes;
      try {
        modelStatusRes = await fetch(`${serverEndpoint}/getModelUpdates`);
      } catch (error) {
        handleRequestFailed('getModelUpdates', error);
        clearInterval(loopCheckRetrainStatus.current);
        loopCheckRetrainStatus.current = null;
        dispatch(setContainerWindowLoadingLocked(false));
        return;
      }
      const modelStatus = await modelStatusRes.json();

      if (checkIfReadyForRetrain(modelStatus)) {
        clearInterval(loopCheckRetrainStatus.current);
        loopCheckRetrainStatus.current = null;
        dispatch(setContainerWindowLoadingLocked(false));
        // search for the task with greatest schedule_id
        const latestTask = _.maxBy(modelStatus, (task) => task.schedule_id);
        if (_.includes([retrainModelTaskPhaseType.failure, retrainModelTaskPhaseType.invalid], _.get(latestTask, 'phase'))) {
          aoiAlert(translation('notification.error.retrainFailed'), ALERT_TYPES.COMMON_ERROR);
          return;
        }
        aoiAlert(translation('notification.success.retrainFinished'), ALERT_TYPES.COMMON_SUCCESS);
        return;
      } else if (!isContainerWindowLoadingLocked) dispatch(setContainerWindowLoadingLocked(true));
    }, 1000);
  };

  useEffect(() => {
    checkRetrainStatusInInit();

    handleGetAllStepsFeatures(systemMetadata, productId);

    return () => {
      if (loopCheckRetrainStatus.current) {
        clearInterval(loopCheckRetrainStatus.current);
        loopCheckRetrainStatus.current = null;
      }
    };
  }, []);

  useEffect(() => {
    if (!isError) return;
    handleRequestFailed('getProductById', error);
  }, [isError, error]);

  useEffect(() => {
    if (_.get(searchParams, 'newProductId') && _.get(searchParams, 'isAddProductTriggered') === 'true') {
      setIsDetectionContinueModelOpened(true);
    } else {
      setIsDetectionContinueModelOpened(false);
      setDetectionImageProductId(productId);
    }
  }, [searchParams]);

  useEffect(() => {
    if (initSelectVariant.current) return;
    if (_.isEmpty(selectedVariation) && _.get(productInfo, 'inspectables')) {
      // selecte the primordial variation by default
      const primordialVariation = _.get(_.find(_.get(productInfo, 'inspectables'), i => i.primordial), 'variant');
      if (_.isEmpty(primordialVariation)) return;
      setSelectedVariation(primordialVariation);
      handleGetAllStepsFeatures(systemMetadata, productId);
      initSelectVariant.current = true;
    }
  }, [productInfo, selectedVariation]);

  if (_.isEmpty(productInfo)) return <Spin />;

  return (
    <MainMenuLayout>
      <DetectionContinueConfirmation
        isOpened={isDetectionContinueModelOpened}
        setIsOpened={setIsDetectionContinueModelOpened}
        productId={_.get(searchParams, 'newProductId')}
        handleRunDetection={handleRunDetection}
        productInfo={productInfo}
      />
      <RetrainConfirmation
        isOpened={isRetrainComfirmationModalOpened}
        setIsOpened={setIsRetrainComfirmationModalOpened}
        handleRetrainTrigger={handleRetrainModel}
        defaultGoldenProductId={Number(productId)}
      />
      <DeleteVariantComfirmation
        isOpened={isDeleteVarintModalOpened}
        setIsOpened={setIsDeleteVarintModalOpened}
        selectedVariant={deleteTargetVariant}
        productId={productId}
        postAction={() => {
          const run = async (productInfo, systemMetadata, productId) => {
            await sleep(1500); // wait for the scene update triggered by the productInfo update to finished then refetch the features
            // await refetchFeatures();
            handleGetAllStepsFeatures(systemMetadata, productId);
            // set varitant to the primordial one
            setSelectedVariation(_.get(_.find(_.get(productInfo, 'inspectables'), i => i.primordial), 'variant'));
          };
          run(productInfo, systemMetadata, productId);
        }}
      />
      <DefineProductRegister
        isOpened={isRegisterConfirmationModalOpened}
        setIsOpened={setIsRegisterConfirmationModalOpened}
        selectedVariation={selectedVariation}
        productInfo={productInfo}
        stepNumber={stepNumber}
      />
      <ProductVariationCapture
        isOpened={isVariationModalOpened}
        setIsOpened={setIsVariationModalOpened}
        handleVariationRegisterSuccess={async (newVariant) => {
          await refetchProductById();
          await sleep(1500); // product info update will update the scene and we need to wait for it to finish before update vairant
          if (!productVariationDefineModalDisabled) setIsProductVariationDefineDescModalOpened(true);
          setSelectedVariation(newVariant);
        }}
        productInfo={productInfo}
        productId={productId}
      />
      <ProductVariationDefineDescription
        isOpened={isProductVariationDefineDescModalOpened}
        setIsOpened={setIsProductVariationDefineDescModalOpened}
      />
      <ManageBoardsLayout
        handleBack={() => window.location.href = '/aoi/manage-product'}
        productName={_.get(productInfo, 'product_name')}
        variantName={selectedVariation}
        stepNumber={stepNumber}
      >
        <div
          className='flex h-[48px] py-2 px-8 justify-between items-center self-stretch'
          style={{ background: 'rgba(255, 255, 255, 0.10)' }}
        >
          <div className='flex items-center gap-6'>
            <div className='flex items-center gap-1'>
              <span className='font-source text-[12px] font-semibold'>
                {translation('viewBoards.board')}
              </span>
              <span className='font-source text-[12px] font-normal'>
                {_.get(productInfo, 'product_name')}
              </span>
            </div>
          </div>
          <div className='flex items-center gap-6'>
            <div className='flex gap-2 self-stretch items-center'>
              <span className='font-source text-[12px] font-normal'>
                {`${getInspectionCountByInspectionViewLayout(_.get(systemMetadata, 'inspection_view_layout'))} ${translation('viewBoards.images')}`}
              </span>
            </div>
            <DarkButton
              borderColor={lightGray}
              onClick={async () => {
                if (!_.isEmpty(curRunningIpcSessionIds) || isInferenceRunning) {
                  aoiAlert(translation('notification.error.inferenceRunningPleaseStopFirst'), ALERT_TYPES.COMMON_ERROR);
                  return;
                }
                
                setIsRetrainComfirmationModalOpened(true);
              }}
            >
              {translation('viewInspection.retrainModel')}
            </DarkButton>
            <DarkButton
              borderColor={lightGray}
              onClick={async () => {
                if (_.isEmpty(productId)) {
                  aoiAlert(translation('notification.warning.productIdNotFoundInUrl'), ALERT_TYPES.COMMON_WARNING);
                  return;
                }
                if (_.isEmpty(selectedVariation)) {
                  aoiAlert(translation('notification.error.selectAVariantionFirst'), ALERT_TYPES.COMMON_ERROR);
                  return;
                }

                // check is inference is running or any training task is running
                // const setCurRunningIpcSessionIds;
                // const setIsInferenceRunning;         
                if (!_.isEmpty(curRunningIpcSessionIds) || isInferenceRunning) {
                  aoiAlert(translation('notification.error.inferenceRunningPleaseStopFirst'), ALERT_TYPES.COMMON_ERROR);
                  return;
                }

                let modelStatusRes;
                try {
                  modelStatusRes = await fetch(`${serverEndpoint}/getModelUpdates`);
                } catch (error) {
                  handleRequestFailed('getModelUpdates', error);
                  return;
                }
                const modelStatus = await modelStatusRes.json();
                if (!checkIfReadyForRetrain(modelStatus)) {
                  // if model is not ready for retrain, then some training tasks are still running
                  aoiAlert(translation('notification.error.someTrainingTaskIsRunning'), ALERT_TYPES.COMMON_ERROR);
                  return;
                }

                const selectedInspectable = _.find(_.get(productInfo, 'inspectables'), i => i.variant === selectedVariation);
                if ((_.isEmpty(selectedInspectable) || _.get(selectedInspectable, 'primordial')) && _.get(productInfo, 'inspectables', []).length > 1) {
                  aoiAlert(translation('notification.error.canNotReregisterTheOriginalProduct'), ALERT_TYPES.COMMON_ERROR);
                  return;
                }
                setIsRegisterConfirmationModalOpened(true);
              }}
            >
              {translation('viewBoards.registerAgain')}
            </DarkButton>
            <DarkButton
              borderColor={lightGray}
              onClick={() => {
                props.history.push(`/aoi/camera-preview/${productId}`);
              }}
            >
              {translation('viewBoards.updateCameraSettings')}
            </DarkButton>
          </div>
        </div>
        <div className='flex px-8 items-start gap-2 flex-1 self-stretch'>
          <div className='flex w-[338px] flex-col gap-2 items-start self-stretch'>
            <div
              className='flex flex-col items-start self-stretch rounded-[2px] border-[1px] border-gray-2'
              style={{ background: 'rgba(255, 255, 255, 0.05)' }}
            >
              <div className='flex flex-col items-start self-stretch'>
                <div
                  className='flex p-2 justify-between items-center self-stretch'
                  style={{ background: 'rgba(255, 255, 255, 0.04)' }}
                >
                  <span className='font-source text-[12px] font-semibold'>
                    {translation('common.display')}
                  </span>
                  <div
                    className='flex items-center gap-2 cursor-pointer hover:bg-gray-2 rounded-[2px] p-1 transition-all duration-150 ease-in-out'
                    onClick={() => {
                      if (!_.isEmpty(stepNumber)) props.history.push(`/aoi/view-product/${productId}`);
                    }}
                  >
                    <img src='/img/icn/icn_customGridLayout_blue.svg' alt='grid' className='w-[16px] h-[11px] fill-AOI-blue' />
                    <span className='font-source text-[12px] font-normal text-AOI-blue'>
                      {translation('common.viewAll')}
                    </span>
                  </div>
                </div>
              </div>
              <div className='flex flex-col items-center gap-1 self-stretch py-1'>
                <MultiViewGridSelection
                  layout={_.get(systemMetadata, 'inspection_view_layout')}
                  selectedViewId={Number(stepNumber)}
                  onSelectView={(viewId) => props.history.push(`/aoi/view-product/${productId}/${viewId}`)}
                />
                <span className='font-source text-[12px] font-normal'>
                  {_.isInteger(Number(stepNumber)) ? `${translation('common.detectionStep')} ${stepNumber}` : translation('common.allDetectionSteps')}
                </span>
              </div>
            </div>
            <Button
              style={{ width: '100%' }}
              onClick={() => {
                if (!_.isEmpty(curRunningIpcSessionIds) || isInferenceRunning) {
                  aoiAlert(translation('notification.error.inferenceRunningPleaseStopFirst'), ALERT_TYPES.COMMON_ERROR);
                  return;
                }
                setIsVariationModalOpened(true);
              }}
            >
              <span className='font-source text-[12px] font-normal'>
                {translation('productAnnotation.newVariation')}
              </span>
            </Button>
            {/* component list */}
            <div className='flex py-2 justify-center items-center slef-stretch gap-2.5'>
              <span className='font-source text-[14px] font-semibold'>
                {translation('productAnnotation.componentList')}
              </span>
            </div>
            <div
              className='flex gap-1 flex-col items-start self-stretch overflow-y-auto'
              style={{ height: `calc(100vh - 253px - 225px)` }}
            >
              <CustomCollapse
                style={{ width: '100%' }}
                activeKey={[stepNumber]}
                onChange={(key) => {
                  if (key.length === 0) {
                    props.history.push(`/aoi/view-product/${productId}`);
                    return;
                  }
                  props.history.push(`/aoi/view-product/${productId}/${key[1]}`);
                }}
                items={[
                  ..._.times(_.sum(_.get(systemMetadata, 'inspection_view_layout')), (index) => (
                    {
                      key: index,
                      label: <div className='flex items-center h-[22px]'>
                        <span className='font-source text-[12px] font-normal'>
                          {translation('productAnnotation.imageListItemTitle', { index: index + 1 })}
                        </span>
                      </div>,
                      children: <div className='flex flex-col items-start self-stretch'>
                        <div
                          className='flex justify-between items-center pt-1 pr-2 pb-1 pl-4 self-stretch'
                          style={{ background: stepNumber === String(index) ? 'rgba(86, 204, 242, 0.10)' : 'rgba(255, 255, 255, 0.03)' }}
                        >
                          <div className='flex gap-2 items-center'>
                            <div className='flex w-3 h-3 justify-center items-center'>
                              <img src='/img/icn/icn_bbox_white.svg' className='w-[10px] h-[10px]' alt='bbox' />
                            </div>
                            <span className={`font-source text-[12px] font-normal ${stepNumber === String(index) ? 'text-AOI-blue' : 'text-white'}`}>
                              {translation('productAnnotation.annotate')}
                            </span>
                          </div>
                          <div className='flex h-4 w-4 justify-center items-center'>
                            <img src={ stepNumber === String(index) ? '/img/icn/icn_arrowRight_blue.svg' : '/img/icn/icn_rightArrow_gray.svg' } className='w-2.5 h-2.5' alt='rightArrow' />
                          </div>
                        </div>
                        <CustomCollapse
                          style={{ width: '100%' }}
                          activeKey={[selectedVariation]}
                          onChange={(key) => {
                            if (key.length === 0) {
                              // setSelectedVariation('');
                              return;
                            }
                            setSelectedVariation(key[1]);
                          }}
                          items={_.map(_.uniq(_.map(productInfo.inspectables, (i) => i.variant)), v => (
                            {
                              key: v,
                              label: <div className='flex items-center h-[22px] justify-between'>
                                <span className='font-source text-[12px] font-normal'>
                                  {v}
                                </span>
                                { _.isEmpty(_.find(_.get(productInfo, 'inspectables'), i => i.variant === v && i.primordial)) &&
                                  <div
                                    className='flex h-6 w-6 justify-center items-center cursor-pointer hover:bg-AOI-red-hover rounded-[2px] transition-all duration-150 ease-in-out'
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      setDeleteTargetVariant(v);
                                      setIsDeleteVarintModalOpened(true);
                                    }}
                                  >
                                    <img src='/img/icn/icn_delete_red.svg' className='w-3 h-3' alt='delete' />
                                  </div>
                                }
                              </div>,
                              children: <div className='flex flex-col items-start self-stretch'>
                                {_.map(_.filter(_.get(allStepsFeatures, `${index}`, []), f => f.variant === v), feature => (
                                  <div
                                    className={`flex py-1 px-2 justify-between items-center self-stretch cursor-pointer 
                                      ${selectedFeatureId === feature.feature_id ? 'bg-[#56ccf21a]' : 'bg-[#ffffff08]'} hover:bg-[#56ccf21a] transition-all duration-300 ease-in-out`}
                                    key={feature.feature_id}
                                    onClick={() => {
                                      dispatch(setSelectedFeatureIdInManageProduct(feature.feature_id));
                                      if (viewRef.current) viewRef.current.zoomPanToFeature(feature.feature_id);
                                    }}
                                  >
                                    <div className='flex py-1 items-center flex-1 gap-2'>
                                      <div className='h-[14px] w-[14px] rounded-[50%]' style={{ backgroundColor: getColorByStr(_.get(feature, 'feature_type')) }} />
                                        <span className={`font-source text-[12px] font-normal ${selectedFeatureId === feature.feature_id ? 'text-AOI-blue' : 'text-white'}`}>
                                          {`${_.get(feature, 'feature_scope') === 'global' ? _.get(feature, 'feature_type') : _.get(feature, 'feature_type').substring(1)} ${_.get(feature, 'feature_id')}`}
                                        </span>
                                    </div>
                                  </div>
                                ))}
                              </div>,
                            }
                          ))}
                        />
                      </div>,
                    }
                  )),
                ]}
              />
            </div>
          </div>
          { _.isEmpty(stepNumber) ? (
            // full product view
            <div
              className='flex p-2 flex-col gap-2 items-start flex-1 self-stretch rounded-[2px]'
              style={{ background: 'rgba(255, 255, 255, 0.10)' }}
            >
              <div className='flex items-center justify-between w-full'>
                <div className='flex items-center gap-2 self-stretch'>
                  {/* <img src='/img/icn/icn_customGridLayout_white.svg' alt='grid' className='w-[16px] h-[11px] fill-[#fff]' /> */}
                  <span className='font-source text-[12px] font-normal'>
                    {/* {translation('viewBoards.fullProduct', { productName: _.get(productInfo, 'product_name') })} */}
                    {`${_.get(productInfo, 'product_name')} - ${translation('common.allDetectionSteps')}`}
                  </span>
                </div>
                <div className='flex justify-center items-center gap-[3px]'>
                  <div
                    className={`duration-300 cursor-not-allowed flex w-[32px] h-[32px] py-1 px-0.5 flex-col justify-center items-center rounded-[2px] gap-2.5`}
                  >
                    <img src='/img/icn/icn_carouselLayout_white.svg' className='w-[16.8px] h-[14px] fill-white' alt='carousel' />
                  </div>
                  <div
                    className={`duration-300 hover:ease-in hover:bg-gray-1 cursor-pointer flex w-[32px] h-[32px] py-1 px-0.5 flex-col justify-center items-center rounded-[2px] gap-2.5 bg-gray-1`}
                  >
                    <img src='/img/icn/icn_gridLayout_white.svg' className='w-[14px] h-[14px] fill-white' alt='grid' />
                  </div>
                </div>
              </div>
              <div className='flex flex-col w-full h-full gap-1 self-stretch'>
                {!_.isEmpty(systemMetadata) && !_.isEmpty(productInfo) && _.map(_.get(systemMetadata, 'inspection_view_layout'), (cols, row) => (
                  <div
                    className={`grid gap-x-1 self-stretch h-full`}
                    style={{ gridTemplateColumns: `repeat(${cols}, 1fr)` }}
                    key={row}
                  >
                    {_.times(cols, (col) => {
                      const curStep = _.sum(_.slice(_.get(systemMetadata, 'inspection_view_layout'), 0, row)) + col;
                      return <StaticStepFeatureListViewer
                        imageUri={_.get(_.find(_.get(productInfo, 'inspectables', []), i => i.variant === selectedVariation && i.step === Number(curStep)), 'color_map_uri')}
                        onClick={() => {
                          props.history.push(`/aoi/view-product/${productId}/${curStep}`);
                        }}
                        features={_.get(allStepsFeatures, `${curStep}`, [])}
                        displayCanvasRef={displayCanvasRef}
                      />
                    })}
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <GoldenProductInferenceStep
              initSelectedFeatureId={featureId}
              productId={productId}
              stepNumber={stepNumber}
              handleStepRedirect={(step) => props.history.push(`/aoi/view-product/${productId}/${step}`)}
              handleFullProductRedirect={() => props.history.push(`/aoi/view-product/${productId}`)}
              features={_.get(allStepsFeatures, `${stepNumber}`, [])}
              productInfo={productInfo}
              refetchProductById={refetchProductById}
              selectedVariation={selectedVariation}
              setSelectedVariation={setSelectedVariation}
              setIsDeleteVarintModalOpened={setIsDeleteVarintModalOpened}
              viewRef={viewRef}
              handleGetAllStepsFeatures={handleGetAllStepsFeatures}
            />
          )}
        </div>
      </ManageBoardsLayout>
    </MainMenuLayout>
  );
};

export default ViewProduct;