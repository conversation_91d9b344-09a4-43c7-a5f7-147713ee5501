import { fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { serverHost } from '../common/const';


export const dynamicBaseQuery = async (args, WebApi, extraOptions) => {
  const localBaseUrl = sessionStorage.getItem('baseUrl') || null;
  const baseUrl =
    localBaseUrl ||
    (await fetch('/api-endpoint').then(async (res) => {
      const baseUrl = await res.text();
      sessionStorage.setItem('baseUrl', baseUrl);
      return baseUrl;
    }));
  const rawBaseQuery = fetchBaseQuery({
    baseUrl: baseUrl,
    prepareHeaders: (headers, { getState }) => {
      if (!headers.get('Authorization')) {  
        const token = getState().user.accessToken;
        headers.set('authorization', token);
      }
    },
    timeout: 600000,
  });
  return rawBaseQuery(args, WebApi, extraOptions);
};

export const baseDevQuery = async (args, WebApi, extraOptions) => {
  // const localBaseUrl = sessionStorage.getItem('baseUrl') || null;

  const baseUrl =
    (() => {
      const baseUrl = `http://${serverHost}`;
      sessionStorage.setItem('baseUrl', baseUrl);
      return baseUrl;
    })();
  const rawBaseQuery = fetchBaseQuery({
    baseUrl: baseUrl,
    // prepareHeaders: (headers) => {
    //   if (!headers.get('Authorization') && localStorage.getItem('accessToken')) {
    //     const token = localStorage.getItem('accessToken');
    //     headers.set('authorization', token);
    //   }
    // },
    // set headers
    prepareHeaders: (headers) => {
      if (args.headers) {
        Object.keys(args.headers).forEach((key) => {
          headers.set(key, args.headers[key]);
        });
      }
    },
    timeout: 600000,
  });
  return rawBaseQuery(args, WebApi, extraOptions);
};

// export const baseQuery = process.env.NODE_ENV !== 'production' ? baseDevQuery : dynamicBaseQuery;
export const baseQuery = baseDevQuery;