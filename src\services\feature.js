import { createApi } from '@reduxjs/toolkit/query/react';
import { baseQuery } from './api';
import _ from 'lodash';
import { serverEndpoint } from '../common/const';


export const featureApi = createApi({
  reducerPath: 'featureApi',
  baseQuery,
  tagTypes: ['Feature'],
  endpoints: (build) => ({
    getAllFeaturesByProductIdAndStep: build.query({
      query: (params) => ({
        url: `/allFeatures`,
        method: 'GET',
        params: params,
      }),
      // transformResponse: (response, meta, arg) => {
      //   // for every feature update the defect_detection in feature_param
      //   const result = [];
      //   response.forEach((feature) => {
      //     if (_.get(feature, 'feature_type') === 'product') {
      //       result.push(feature);
      //       return;
      //     }
      //     let parsed = JSON.parse(_.cloneDeep(_.get(feature, 'feature_param', '{}')));
      //     const updatedSensitivity = 10 - _.get(parsed, 'defect_detection.agent_config.sensitivity', 1);
      //     parsed = _.set(parsed, 'defect_detection.agent_config.sensitivity', updatedSensitivity);
      //     const newFeature = { ...feature, feature_param: JSON.stringify(parsed) };
      //     result.push(newFeature);
      //   });
      //   return result;
      // },
      providesTags: (result, error, { product_id, step }) => [{ type: 'Feature', product_id, step }],
    }),
    updateFeatureByProductIdAndStep: build.mutation({
      query: (body) => {
        return {
          url: `/feature`,
          method: 'PUT',
          body,
        };
        // if (_.get(body, 'feature_type') === 'product') {
        //   return {
        //     url: `/feature`,
        //     method: 'PUT',
        //     body,
        //   };
        // }
        // let parsed = JSON.parse(_.cloneDeep(_.get(body, 'feature_param', '{}')));
        // // update the defect_detection
        // const updatedSensitivity = 10 - _.get(parsed, 'defect_detection.agent_config.sensitivity', 1);
        // parsed = _.set(parsed, 'defect_detection.agent_config.sensitivity', updatedSensitivity);
        // return {
        //   url: `/feature`,
        //   method: 'PUT',
        //   body: {
        //     ...body,
        //     feature_param: JSON.stringify(parsed),
        //   },
        // };
      },
      // TODO: fetch specific feature by id instead of all features
      invalidatesTags: (result, error, { product_id, step }) => [{ type: 'Feature', product_id, step }],
    }),
    addFeatureByProductIdAndStep: build.mutation({
      query: (body) => {
        return {
          url: `/addFeature`,
          method: 'POST',
          body,
        };
        // if (_.get(body, 'feature_type') === 'product') {
        //   return {
        //     url: `/addFeature`,
        //     method: 'POST',
        //     body,
        //   };
        // }
        // let parsed = JSON.parse(_.cloneDeep(_.get(body, 'feature_param', '{}')));
        // update the defect_detection
        // const updatedSensitivity = 10 - _.get(parsed, 'defect_detection.agent_config.sensitivity', 1);
        // parsed = _.set(parsed, 'defect_detection.agent_config.sensitivity', updatedSensitivity);
        // return {
        //   url: `/addFeature`,
        //   method: 'POST',
        //   body: {
        //     ...body,
        //     feature_param: JSON.stringify(parsed),
        //   },
        // };
      },
      invalidatesTags: (result, error, body) => [{ type: 'Feature', product_id: body.product_id, step: body.step }],
    }),
    removeFeatureByProductIdAndStep: build.mutation({
      // query: ({ product_id, step, feature_id, variant }) => ({
      //   url: `/feature`,
      //   method: 'DELETE',
      //   params: { product_id, step, feature_id, variant },
      // }),
      async queryFn(arg, queryApi, extraOptions, baseQuery) {
        try {
          // space will be encoded to + for some reason in rtk query
          const encodedVariantName = encodeURIComponent(arg.variant);
          const response = await fetch(`${serverEndpoint}/feature?product_id=${arg.product_id}&step=${arg.step}&feature_id=${arg.feature_id}&variant=${encodedVariantName}`, {
            method: 'DELETE',
          });

          if (!response.ok) {
            return { error: { status: response.status, message: response.statusText } };
          }

          const data = await response.json();
          return { data };
        } catch (error) {
          return { error: { status: 'FETCH_ERROR', message: error.message } };
        }
      },
      invalidatesTags: (result, error, { product_id, step }) => [{ type: 'Feature', product_id, step }],
    }),
    getCustomFeatures: build.query({
      query: ({ product_id }) => ({
        url: `/featureTypes`,
        method: 'GET',
        params: { product_id },
      }),
    }),
  }),
});

export const {
  useGetAllFeaturesByProductIdAndStepQuery,
  useUpdateFeatureByProductIdAndStepMutation,
  useRemoveFeatureByProductIdAndStepMutation,
  useAddFeatureByProductIdAndStepMutation,
  useLazyGetAllFeaturesByProductIdAndStepQuery,
  useGetCustomFeaturesQuery,
} = featureApi;