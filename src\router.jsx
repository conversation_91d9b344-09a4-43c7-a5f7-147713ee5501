import { ConnectedRouter } from "connected-react-router";
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Redirect, Route } from 'react-router';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import ManageProduct from './components/Product/ManageProducts';
import ViewProduct from './components/Product/ViewGoldenProducts';
import CameraPreview from './components/Camera/CameraPreview';
import Home from './components/Home';
import ContainerLayout from './components/layout/ContainerLayout';
import Login from './components/Login';
import Signup from './components/Signup';
import history from './history';
import { systemApi, useGetSystemMetadataQuery, useLazyGetSystemMetadataQuery } from './services/system';
import LiveInspectionList from './components/LiveDashboard/LiveInspectionList';
import InferenceDetail from './components/Inference/InferenceDetail';
import DummyInferenceDetail from './components/Inference/DummyInferenceDetail';
import { useLazyGetInferenceStatusQuery } from './services/product';
import _, { set } from 'lodash';
import { handleRequestFailed } from './common/util';
import { store } from './store';
import { useDispatch, useSelector } from 'react-redux';
import { setCurRunningIpcSessionIds, setInferenceStatusResponse, setIsInferenceRunning, setIsTrainingRuning } from './actions/setting';
import ProductCameraSetting from './components/Camera/ProductCameraSetting';
import Worklist from './components/Worklist/Worklist';
import { useGetAllSessionsQuery, useLazyGetAllSessionsQuery } from './services/session';
import ExistedInferenceDetail from './components/Inference/ExistedInferenceDetail';
import ComponentReview from './components/ComponentReview/ComponentReview';
import NewProduct from './components/Product/NewProduct';
import UpdateBackendHost from './components/modal/UpdateBackendHost';
import TrainingInProgress from './components/TrainingInProgress';
import { retrainModelTaskPhaseType, serverEndpoint } from './common/const';


const ProtectedRoute = (props) => {
  // const { data: systemMetadata } = useSelector((state) => systemApi.endpoints.getSystemMetadata.select()(state));

  // if (_.isEmpty(systemMetadata)) return <ProtectedRoute exact path='/aoi/home' component={Home} />;
  return <Route {...props} />;
};

const AOIRouter = (props) => {
  const dispatch = useDispatch();
  const { i18n } = useTranslation();

  const [systemInited, setSystemInited] = useState(false);
  const [isUpdateBackendHostModalOpened, setIsUpdateBackendHostModalOpened] = useState(false);

  const [getInferenceStatus] = useLazyGetInferenceStatusQuery();
  const [lazyGetSystemMetadata] = useLazyGetSystemMetadataQuery();
  const [getSessions] = useLazyGetAllSessionsQuery();

  const checkIfReadyForRetrain = (modelStatus) => {
    if (_.isEmpty(modelStatus)) return true;
    return _.isEmpty(_.filter(modelStatus, (taskStatus) => !_.includes([
      retrainModelTaskPhaseType.failure,
      retrainModelTaskPhaseType.complete,
      retrainModelTaskPhaseType.invalid
    ], taskStatus.phase)));
  };

  useEffect(async () => {
    if (!lazyGetSystemMetadata || !getSessions || !dispatch) return;

    const res = await lazyGetSystemMetadata();
    const runningSessions = await getSessions({ complete: -1 });

    setSystemInited(true);

    // if (res.error && window.location.pathname !== '/' && window.location.pathname !== '/aoi/home') {
    //   window.location.href = '/';
    //   console.error('Failed to get system metadata');
    //   return;
    // } else if (res.error) {
    //   setIsUpdateBackendHostModalOpened(true);
    // }

    // if (runningSessions.error && window.location.pathname !== '/' && window.location.pathname !== '/aoi/home') {
    //   window.location.href = '/';
    //   console.error('Failed to get running sessions');
    //   return;
    // } else if (res.error) {
    //   setIsUpdateBackendHostModalOpened(true);
    // }

    if (res.error || runningSessions.error) {
      setIsUpdateBackendHostModalOpened(true);
      return;
    }

    dispatch(setCurRunningIpcSessionIds(_.get(runningSessions, 'data.data', []).map((session) => session.ipc_session_id)));
  }, [
    lazyGetSystemMetadata,
    getSessions,
    dispatch,
  ]);

  useEffect(() => {
    // if (!systemInited) return;
    // refetch inference status every 1 second
    const autoUpdateInferenceStatus = setInterval(async () => {
      const res = await getInferenceStatus();
      if (res.error) {
        // handleRequestFailed('getInferenceStatus', res.error);
        setIsUpdateBackendHostModalOpened(true);
        return;
      } else {
        setIsUpdateBackendHostModalOpened(false);
      }
      const curReduxInferenceStatus = _.get(store.getState(), 'setting.isInferenceRunning');
      if (_.get(res, 'data.status') !== 'stopped' && !curReduxInferenceStatus) {
        dispatch(setIsInferenceRunning(true));
      }
      if (_.get(res, 'data.status') === 'stopped' && curReduxInferenceStatus) {
        dispatch(setIsInferenceRunning(false));
      }
      dispatch(setInferenceStatusResponse(res.data));
    }, 1000);

    const checkInitModelTrainingStatus = async () => {
      let modelStatusRes;

      try {
        modelStatusRes = await fetch(`${serverEndpoint}/getModelUpdates`);
      } catch (error) {
        console.error('Failed to get model updates');
        return;
      }

      const modelStatus = await modelStatusRes.json();
      if (!checkIfReadyForRetrain(modelStatus)) {
        dispatch(setIsTrainingRuning(true));
        // window.location.href = '/aoi/model-training-in-progress';
      }
    };

    checkInitModelTrainingStatus();

    return () => clearInterval(autoUpdateInferenceStatus);
  }, [dispatch]);

  if (!systemInited) {
    return <>
      <div>Loading system config...</div>
    </>;
  }

  return (
    <ConnectedRouter history={history} key={i18n.language}>
      <ContainerLayout>
        <UpdateBackendHost
          isOpened={isUpdateBackendHostModalOpened}
          setIsOpened={setIsUpdateBackendHostModalOpened}
          updatedRequired={true}
        />
        <Route exact path='/login' component={Login} />
        <Route exact path='/signup' component={Signup} />
        <ProtectedRoute exact path='/' component={Home} />
        <ProtectedRoute exact path='/aoi/home' component={Home} />
        <ProtectedRoute exact path='/aoi/manage-product' component={ManageProduct} />
        <ProtectedRoute exact path='/aoi/edit-product/:productId' component={NewProduct} />
        {/* <ProtectedRoute exact path='/aoi/view-product' component={ViewProduct} /> */}
        {/* <ProtectedRoute exact path='/aoi/view-product/:productId' component={ViewProduct} /> */}
        {/* <ProtectedRoute exact path='/aoi/view-product/:productId/:variant' component={ViewProduct} /> */}
        {/* <ProtectedRoute exact path='/aoi/view-product/:productId/:stepNumber' component={ViewProduct} /> */}
        <ProtectedRoute exact path='/aoi/camera-preview' component={CameraPreview} />
        {/* <ProtectedRoute exact path='/aoi/camera-preview/:productId' component={ProductCameraSetting} /> */}
        <ProtectedRoute exact path='/aoi/live-dashboard' component={LiveInspectionList} />
        {/* <ProtectedRoute exact path='/aoi/view-inference/:ipcSessionId/:productId/:ipcProductId' component={DummyInferenceDetail} /> */}
        <ProtectedRoute exact path='/aoi/view-inference/:productId/:ipcProductId' component={InferenceDetail} />
        <ProtectedRoute exact path='/aoi/view-existed-inference/:productId/:ipcProductId' component={ExistedInferenceDetail} />
        <ProtectedRoute exact path='/aoi/worklist' component={Worklist} />
        <ProtectedRoute exact path='/aoi/component-review/:goldenProductId' component={ComponentReview} />
        {/* <ProtectedRoute exact path='/aoi/model-training-in-progress' component={TrainingInProgress} /> */}
        {/* <Router  */}
      </ContainerLayout>
      <ToastContainer hideProgressBar={true} newestOnTop style={{ zIndex: '10000' }}/>
    </ConnectedRouter>
  );
};

export default AOIRouter;