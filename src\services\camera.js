import { createApi } from '@reduxjs/toolkit/query/react';
import { baseQuery } from './api';


export const cameraApi = createApi({
  reducerPath: 'cameraApi',
  baseQuery,
  tagTypes: ['Camera'],
  endpoints: (build) => ({
    getCameraCaptureFrame: build.query({
      query: ({ camera_id, product_id }) => {
        return {
          url: `/camera/preview`,
          method: 'GET',
          params: { camera_id, product_id },
        };
      }
    }),
    getAllCameraConfig: build.query({
      query: () => ({
        url: '/camera/configs',
        method: 'GET',
      }),
      providesTags: ['Camera'],
    }),
    updateCameraConfig: build.mutation({
      query: (camera_configs) => ({
        url: '/camera/configs',
        method: 'PUT',
        body: camera_configs,
      }),
      invalidatesTags: ['Camera'],
    }),
    discoverCameras: build.query({
      query: () => ({
        url: '/camera/list',
        method: 'GET',
      })
    }),
  }),
});

export const {
  useGetCameraCaptureFrameQuery,
  useLazyGetCameraCaptureFrameQuery,
  useGetAllCameraConfigQuery,
  useUpdateCameraConfigMutation,
  useLazyDiscoverCamerasQuery,
} = cameraApi;