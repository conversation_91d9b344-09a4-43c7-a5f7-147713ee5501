import { <PERSON><PERSON>, Config<PERSON><PERSON><PERSON>, Spin } from 'antd';
import _ from 'lodash';
import React, { Fragment, useEffect, useState } from 'react';
import { DarkTable, primaryColor, RoundLabel } from '../common/darkModeComponents';
import { backendAutoGenTimeToDisplayString, backendTimestampToDisplayString, convertBackendTimestampToMoment, translation } from '../common/util';
import { useGetAllProductsQuery } from '../services/product';
import { useGetAllInspectionsQuery, useGetAllSessionsQuery } from '../services/session';
import MainMenuLayout from './layout/MainMenuLayout';
import ExportCaptureData from './modal/ExportCaptureData';
import NewInspection from './modal/NewInspection';
import { useSelector } from 'react-redux';
import { ALERT_TYPES, aoiAlert } from '../common/alert';
import i18n from '../i18n';
import enLocale from 'antd/es/locale/en_US';
import cnLocale from 'antd/es/locale/zh_CN';
import { retrainModelTaskPhaseType } from '../common/const';


const Home = (props) => {
  const [isNewInspectionModalOpened, setIsNewInspectionModalOpened] = useState(false);
  const [columns, setColumns] = useState([]);
  const [isExportModalOpened, setIsExportModalOpened] = useState(false);
  const [pageSize, setPageSize] = useState(0);
  const [displayedIpc, setDisplayedIpc] = useState([]);

  const curRunningIpcSessionIds = useSelector((state) => state.setting.curRunningIpcSessionIds);
  const userType = useSelector((state) => state.setting.userType);

  // const {
  //   data: allIpcs,
  //   isLoading,
  // } = useGetAllInspectionsQuery({ limit: 20, page: 0 });

  const {
    data: recentSessions,
    isLoading,
  } = useGetAllSessionsQuery({ limit: 20, page: 0 });
  const { data: allProducts } = useGetAllProductsQuery();

  const checkIfReadyForRetrain = (modelStatus) => {
    if (_.isEmpty(modelStatus)) return true;
    return _.isEmpty(_.filter(modelStatus, (taskStatus) => !_.includes([
      retrainModelTaskPhaseType.failure,
      retrainModelTaskPhaseType.complete,
      retrainModelTaskPhaseType.invalid
    ], taskStatus.phase)));
  };

  useEffect(() => {
    if (_.isEmpty(allProducts) || _.isEmpty(_.get(recentSessions, 'data'))) return;
    setDisplayedIpc(_.get(recentSessions, 'data', []));
    setColumns([
      {
        title: translation('home.sessionId'),
        key: 'session_id',
        render: (text, record) => {
          return (
            <span className='font-source text-[12px] font-normal'>
              {record.ipc_session_id}
            </span>
          );
        },
      },
      // {
      //   title: translation('home.goldenProducts'),
      //   key: 'golden_products',
      //   render: (text, record) => {
          
      //   },
      // },
      {
        title: translation('worklist.goldenProducts'),
        key: 'product_name',
        render: (text, record) => {
          return (
            <div className='flex items-center gap-2 h-[32px]'>
              <div className='flex items-center gap-1 h-[32px] w-full overflow-y-auto flex-wrap'>
                {/* {_.map([1066, 1062, 1061, 1054], (pid, index) => { */}
                {_.map(_.get(record, 'golden_product_ids', []), (pid, index) => {
                  return <span className={'font-source text-[12px] font-formal text-white'}>
                    {`${_.get(_.find(allProducts, p => Number(p.product_id) === pid), 'product_name', 'unknwon product')}`}
                  </span>
                })}
              </div>
            </div>
          );
        },
      },
      {
        title: translation('home.startedAt'),
        key: 'started_at',
        render: (_, record) => (
          <div className='flex items-center w-[105px] gap-4'>
            <span className='font-source text-[12px] font-normal'>
              {/* {backendTimestampToDisplayString(record.started_at)} */}
              {backendAutoGenTimeToDisplayString(record.started_at)}
            </span>
          </div>
        ),
        // sorter: (a, b) => convertBackendTimestampToMoment(b.timestamp) - convertBackendTimestampToMoment(a.timestamp),
      },
      {
        title: translation('worklist.totalProducts'),
        key: 'total_products',
        render: (text, record) => {
          return (
            <span className='font-source text-[12px] font-normal'>
              {record.total_product_count}
            </span>
          );
        },
      },
      {
        title: translation('worklist.productok'),
        key: 'product_ok',
        render: (text, record) => {
          return (
            <span className='font-source text-[12px] font-normal'>
              {_.get(record, 'total_product_count') - _.get(record, 'defective_product_count', 0)}
            </span>
          );
        },
      },
      {
        title: translation('worklist.productng'),
        key: 'product_ng',
        render: (text, record) => {
          return (
            <span className='font-source text-[12px] font-normal'>
              {_.get(record, 'defective_product_count', 0)}
            </span>
          );
        },
      },
      {
        title: translation('worklist.passRate'),
        key: 'pass_rate',
        render: (text, record) => {
          return (
            <span className='font-source text-[12px] font-normal'>
              {_.get(record, 'total_product_count') === 0 ? '' : `${Math.round((1 - _.get(record, 'defective_product_count', 0) / _.get(record, 'total_product_count')) * 100, 4)}% ${translation('worklist.pass')}`}
            </span>
          );
        },
      },
      {
        title: translation('home.detectionStatus'),
        dataIndex: 'status',
        key: 'status',
        render: (text, record) => { return !_.includes(curRunningIpcSessionIds, record.ipc_session_id) ? 
          <RoundLabel style={{ width: 'fit-content' }}>
            <div className='flex items-center gap-2'>
              <img src='/img/icn/icn_checkerFlag_white.svg' alt='flag' className='w-[9px] h-[10px] fill-[#fff]' />
              <span className='font-source text-[12px] font-semibold'>
                {translation('viewBoards.detectionFinished')}
              </span>
            </div>
          </RoundLabel> :
          <RoundLabel style={{ width: 'fit-content' }} bgColor={primaryColor} textColor='#333'>
            <div className='flex items-center gap-2'>
              <ConfigProvider
                theme={{
                  components: {
                    Spin: {
                      dotSize: 10
                    },
                  },
                }}
              >
                <Spin />
              </ConfigProvider>
              <span className='font-source text-[12px] font-semibold'>
                {translation('viewBoards.detectionInProgress')}
              </span>
            </div>
          </RoundLabel>
        },
      },
      {
        title: translation('worklist.actions'),
        key: 'actions',
        render: (text, record) => {
          if (_.includes(curRunningIpcSessionIds, record.ipc_session_id)) {
            return (
              <div className='flex items-center'>
                <Button
                  type='text'
                  onClick={() => {
                    window.location.href = '/aoi/live-dashboard';
                  }}
                >
                  <span className='font-source text-[12px] font-normal text-AOI-blue'>
                    {translation('home.openInLiveList')}
                  </span>
                </Button>
              </div>
            );
          } return (<div className='flex items-center'>
            <Button
              type='text'
              onClick={() => {
                window.location.href = `/aoi/worklist?selectedSessionIdFromHome=${record.ipc_session_id}`;
              }}
            >
              <span className='font-source text-[12px] font-normal text-AOI-blue'>
                {translation('home.openInWorklist')}
              </span>
            </Button>
          </div>);
        },
      }
    ]);
  }, [recentSessions, allProducts, curRunningIpcSessionIds]);

  useEffect(() => {
    const tableTBodyHeight = window.innerHeight - 705;
    setPageSize(Math.max(Math.floor(tableTBodyHeight / 60), 1));

    const handleResize = () => {
      const tableTBodyHeight = window.innerHeight - 705;
      setPageSize(Math.max(Math.floor(tableTBodyHeight / 60), 1));
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return (
    <Fragment>
      <NewInspection
        isOpened={isNewInspectionModalOpened}
        setIsOpened={setIsNewInspectionModalOpened}
        handleRedirect={(path) => props.history.push(path)}
      />
      <ExportCaptureData
        isOpened={isExportModalOpened}
        setIsOpened={setIsExportModalOpened}
      />
      <MainMenuLayout>
        <div className='flex py-8 items-start gap-4 flex-1 self-stretch'>
          <div className='flex flex-col gap-12 flex-1 self-stretch items-start px-[140px]'>
            <div className='flex h-12 items-center gap-2.5 slef-stretch'>
              <span className='font-source text-[24px] font-semibold'>
                {translation('home.welcomeToDaoAIAOI')}
              </span>
            </div>
            <div className='flex items-start gap-6 self-stretch'>
              <div className='flex flex-col gap-6 flex-1 items-start'>
                <div className='flex flex-col gap-2 items-start self-stretch'>
                  <div className='flex justify-center items-center gap-2.5 py-2'>
                    <span className='font-source text-[20px] font-semibold'>
                      {translation('home.returnToInspection')}
                    </span>
                  </div>
                  <div className='flex items-start content-start gap-6 self-stretch flex-wrap'>
                    <div className={`flex flex-col gap-2 flex-1 items-start cursor-pointer`}
                      onClick={() => setIsNewInspectionModalOpened(true)}
                    >
                      <div
                        className={`flex h-[160px] py-12 px-6 justify-center items-center gap-2.5 self-stretch 
                          rounded-[4px] border-[1px] border-gray-1 bg-AOI-blue shadow hover:bg-AOI-blue-hover hover:shadow-lg transition-all`}
                      >
                        <div className='flex p-[1px] items-center gap-2.5'>
                          <img src='/img/icn/icn_plus_black.svg' className='w-6 h-6 fill-gray-1' alt='plus' />
                        </div>
                      </div>
                      <span className='font-source text-[16px] font-semibold'>
                        {translation('home.newInspectionWork')}
                      </span>
                    </div>
                    <div className='flex flex-col gap-2 flex-1 items-start cursor-pointer'
                      onClick={() => {
                        if (_.isEmpty(curRunningIpcSessionIds)) {
                          aoiAlert(translation('liveDashboard.noInspectionIsRunning'), ALERT_TYPES.COMMON_WARNING);
                          return;
                        }
                        props.history.push('/aoi/live-dashboard');
                      }}
                    >
                      <div
                        className={`flex h-[160px] py-12 px-6 justify-center items-center gap-2.5 self-stretch 
                          rounded-[4px] border-[1px] border-gray-1 bg-AOI-green shadow hover:bg-AOI-green-hover hover:shadow-lg transition-all`}
                      >
                        <div className='flex py-[3px] px-[4px] justify-center items-center gap-2.5'>
                          <img src='/img/icn/icn_meter_black.svg' className='w-[32px] h-[25px] fill-gray-1' alt='plus' />
                        </div>
                      </div>
                      <span className='font-source text-[16px] font-semibold'>
                        {translation('home.liveDashboard')}
                      </span>
                    </div>
                    {/* <div
                      className='flex flex-col gap-2 flex-1 items-start cursor-pointer'
                      onClick={() => setIsExportModalOpened(true)}
                    >
                      <div
                        className={`flex h-[160px] py-12 px-6 justify-center items-center gap-2.5 self-stretch 
                          rounded-[4px] border-[1px] border-gray-1 bg-AOI-yellow shadow hover:bg-AOI-yellow-hover hover:shadow-lg transition-all`}
                      >
                        <div className='flex p-[1px] items-center gap-2.5'>
                          <img src='/img/icn/icn_download_black.svg' className='w-8 h-8 fill-gray-1' alt='plus' />
                        </div>
                      </div>
                      <span className='font-source text-[16px] font-semibold'>
                        {translation('home.exportByGoldenProduct')}
                      </span>
                    </div> */}
                    <div
                      className='flex flex-col gap-2 flex-1 items-start cursor-pointer'
                      onClick={() => props.history.push('/aoi/worklist')}
                    >
                      <div
                        className={`flex h-[160px] py-12 px-6 justify-center items-center gap-2.5 self-stretch 
                          rounded-[4px] border-[1px] border-gray-1 bg-AOI-yellow shadow hover:bg-AOI-yellow-hover hover:shadow-lg transition-all`}
                      >
                        <div className='flex p-[1px] items-center gap-2.5'>
                          <img src='/img/icn/icn_checklist_black.svg' className='w-8 h-8 fill-gray-1' alt='plus' />
                        </div>
                      </div>
                      <span className='font-source text-[16px] font-semibold'>
                        {translation('home.workList')}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              {userType === 'admin' && <div className='flex flex-col gap-2 items-start flex-1'>
                <div className='flex py-2 items-center justify-center gap-2.5'>
                  <span className='font-source text-[20px] font-semibold'>
                    {translation('home.manageSystem')}
                  </span>
                </div>
                <div className='flex items-start gap-4 self-stretch flex-wrap content-start'>
                  <div
                    className='flex flex-col gap-2 flex-1 items-start cursor-pointer'
                    onClick={() => props.history.push('/aoi/manage-product')}
                  >
                    <div className={`flex h-[160px] py-2 px-6 justify-center items-center gap-4 self-stretch 
                      rounded-[4px] border-[1px] border-gray-1 bg-[#073B4C] hover:bg-[#517581] transition-all hover:shadow-lg`}>
                      <div className='flex items-center justify-center'>
                        <img className='h-8 w-8 fill-white' src='/img/icn/icn_board_white.svg' alt='products' />
                      </div>
                    </div>
                    <span className='font-source text-[16px] font-semibold'>
                      {translation('home.products')}
                    </span>
                  </div>
                  <div className='flex flex-col gap-2 flex-1 items-start cursor-pointer'
                    onClick={() => {
                      props.history.push('/aoi/camera-preview');
                    }}
                  >
                    <div className={`flex h-[160px] py-2 px-6 justify-center items-center gap-4 self-stretch 
                      rounded-[4px] border-[1px] border-gray-1 bg-[#073B4C] hover:bg-[#517581] transition-all hover:shadow-lg`}>
                      <div className='flex items-center justify-center p-[1.6px] gap-4'>
                        <img className='h-[28.8px] w-[28.8px] fill-white' src='/img/icn/icn_image_white.svg' alt='images alt' />
                      </div>
                    </div>
                    <span className='font-source text-[16px] font-semibold'>
                      {translation('home.cameraPreview')}
                    </span>
                  </div>
                </div>
              </div>}
            </div>
            <div className='flex flex-col items-start gap-4 flex-1 self-stretch'>
              <div className='flex h-12 py-2 items-end gap-6 self-stretch'>
                <div className='flex items-center gap-3'>
                  <div className='flex justify-center items-center gap-3'>
                    <div className='flex gap-2.5 items-center'>
                      <img src='/img/icn/icn_checklist_white.svg' className='w-5 h-5 fill-white' alt='checklist' />
                    </div>
                  </div>
                  <span className='font-source text-[20px] font-semibold'>
                    {translation('home.recentInspectionWork')}
                  </span>
                </div>
              </div>
              <DarkTable
                loading={isLoading}
                dataSource={displayedIpc}
                style={{ width: '100%' }}
                columns={columns}
                rowHoverable={false}
                pagination={{
                  pageSize,
                  hideOnSinglePage: true,
                  showSizeChanger: false,
                  showQuickJumper: true,
                }}
                locale={i18n.language === 'cn' ? cnLocale.Table : enLocale.Table}
              />
            </div>
          </div>
        </div>
      </MainMenuLayout>
    </Fragment>
  );
};

export default Home;