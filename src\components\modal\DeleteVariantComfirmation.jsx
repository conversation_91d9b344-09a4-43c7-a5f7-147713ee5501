import { Button, Modal } from 'antd';
import React from 'react';
import { handleRequestFailed, translation } from '../../common/util';
import { useRemoveVariantMutation } from '../../services/product';
import _ from 'lodash';
import { ALERT_TYPES, aoiAlert } from '../../common/alert';
import { RedPrimaryButtonConfigProvider } from '../../common/darkModeComponents';


const DeleteVariantComfirmation = (props) => {
  const {
    isOpened,
    setIsOpened,
    selectedVariant,
    productId,
    postAction,
  } = props;

  const [removeVariant] = useRemoveVariantMutation();

  return (
    <Modal
      open={isOpened}
      onCancel={() => setIsOpened(false)}
      title={<span className='font-source text-[16px] font-semibold'>{translation('deleteVariantComfirmation.deleteVariant')}</span>}
      footer={
        <div className='flex p-2 gap-2 items-center'>
          <Button
            style={{ width: '50%' }}
            onClick={() => setIsOpened(false)}
          >
            <span className='font-source text-[12px] font-normal'>{translation('common.cancel')}</span>
          </Button>
          <RedPrimaryButtonConfigProvider>
            <Button
              style={{ width: '50%' }}
              onClick={async () => {
                if (_.isEmpty(selectedVariant) || _.isEmpty(productId)) {
                  aoiAlert(translation('notification.error.selectAVariantionFirst'), ALERT_TYPES.COMMON_ERROR);
                  return;
                }
                const res = await removeVariant({ product_id: productId, variant: selectedVariant });
                if (res.error) {
                  handleRequestFailed('deleteVariant', res.error);
                  return;
                }
                aoiAlert(translation('notification.success.variantDeleted'), ALERT_TYPES.COMMON_SUCCESS);
                postAction();
                setIsOpened(false);
              }}
            >
              <span className='font-source text-[14px] font-normal'>{translation('common.delete')}</span>
            </Button>
          </RedPrimaryButtonConfigProvider>
        </div>
      }
    >
      <div className='flex py-6 px-4 flex-col items-start gap-8 self-stretch'>
        <span className='font-source text-[12px] font-normal'>{translation('deleteVariantComfirmation.thisActionWillDelete', { varinat: selectedVariant })}</span>
      </div>
    </Modal>
  );
};

export default DeleteVariantComfirmation;