import React from 'react';
import Two<PERSON>B<PERSON>Viwer from './TwoDBaseViewer';
import { fabric } from 'fabric-with-erasing';
import _ from 'lodash';
import { baseBboxStrokeWidth, serverEndpoint } from '../../../common/const';
import { getColorByStr } from '../../../common/util';


export default class StaticStepFeatureListViewer extends TwoDBaseViwer {
  constructor(props) {
    super(props, props.displayCanvasRef);
    this.scene = null; // 2d image
    this.fabricCanvas = null;
    this.containerRef = React.createRef();
    this.displayCanvasRef = !_.isNull(props.displayCanvasRef) ? props.displayCanvasRef : this.getCanvasRef();
    this.sceneOriginalHeight = null;
    this.sceneOriginalWidth = null;
    this.rects = [];
  };

  componentDidMount() {
    // init fabric canvas
    const fabricCanvas = new fabric.Canvas(
      this.displayCanvasRef.current,
      {
        // Disable uniform scaling
        uniformScaling: false,
      }
    );
    fabricCanvas.skipOffscreen = true;

    this.fabricCanvas = fabricCanvas;

    if (!_.isEmpty(this.props.imageUri)) {
      this.loadScene({
        imageUri: this.props.imageUri,
      });
    }

    if (!_.isEmpty(this.props.features)) {
      this.loadFeatures(this.props.features, this.props.colorByFeatureType);
    }
  };

  componentDidUpdate(prevProps) {
    // console.log('StaticStepFeatureListViewer mounted', this.props.imageUri);
    if (this.props.imageUri !== prevProps.imageUri && !_.isEmpty(this.props.imageUri)) {
      this.loadScene({
        imageUri: this.props.imageUri,
      });
    }
    if (this.props.features !== prevProps.features) {
      this.loadFeatures(this.props.features, this.props.colorByFeatureType);
    }
  };

  loadScene = async ({
    imageUri,
  }) => {
    if (!this.containerRef.current || !this.fabricCanvas) return;

    if (this.scene) {
      this.fabricCanvas.remove(this.scene);
    }

    let res;
    try {
      res = await fetch(`${serverEndpoint}/data?data_uri=${imageUri}`);
    } catch (e) {
      console.error(e);
      return;
    }

    const blob = await res.blob();
    const reader = new FileReader();
    reader.readAsDataURL(blob);
    const url = await new Promise((resolve) => {
    reader.onloadend = () => {
        resolve(reader.result);
      };
    });

    fabric.Image.fromURL(url, (img) => {
      img.set({
        selectable: false,
        evented: false,
      })
      this.fabricCanvas.setWidth(this.containerRef.current.offsetWidth);
      this.fabricCanvas.setHeight(this.containerRef.current.offsetHeight);
      this.sceneOriginalHeight = img.height;
      this.sceneOriginalWidth = img.width;
      this.fabricCanvas.add(img);
      this.scene = img;
      this.resetView();
      this.fabricCanvas.on('mouse:down', (e) => {
        this.props.onClick();
      });
      // update mouse cursor to pointer
      this.fabricCanvas.defaultCursor = 'pointer';
      // set background color to #000
      this.fabricCanvas.setBackgroundColor('#000', this.fabricCanvas.renderAll.bind(this.fabricCanvas));
      this.updateSceneZIndex();
    });
  };

  loadFeatures = async (features, colorByFeatureType=true) => {
    // dispose previous rects
    this.rects.forEach((rect) => {
      if (rect.lockIcon) this.fabricCanvas.remove(rect.lockIcon);
      this.fabricCanvas.remove(rect);
    });
    this.rects = [];

    if (!this.fabricCanvas || _.isEmpty(features)) return;
    // load features(bounding box)
    // console.log('load features');

    let zoom = this.fabricCanvas.getZoom();
    const newStrokeWidth = Math.min(baseBboxStrokeWidth, baseBboxStrokeWidth / zoom);
    // const strokeWidthDelta = newStrokeWidth - baseBboxStrokeWidth;

    // in some degree group by the feature id
    // when multiple agents existed with the same feature id the pass status should be and replationship
    let newFeatures = {}; // { feature_id: { original feature object } }
    for (const f of features) {
      if (!_.includes(_.keys(newFeatures), String(f.feature_id))) {
        newFeatures[String(f.feature_id)] = _.cloneDeep(f);
      } else if (_.get(newFeatures, `${f.feature_id}.pass`)) {
        newFeatures[String(f.feature_id)].pass = f.pass;
      }
    }

    newFeatures = _.values(newFeatures);

    for (const feature of newFeatures) {
      const pMin = _.get(feature, 'roi.points[0]');
      const pMax = _.get(feature, 'roi.points[1]');
      // this position is based on the original image size
      // hence we need to scale it to the current scene size
      let newLeft = 0;
      let newTop = 0;
      let newWidth = 0;
      let newHeight = 0;
      if (_.isNumber(this.sceneOriginalHeight) && _.isNumber(this.sceneOriginalWidth) && this.scene) {
        newLeft = pMin.x * this.scene.scaleX;
        newTop = pMin.y * this.scene.scaleY;
        newWidth = (pMax.x - pMin.x) * this.scene.scaleX;
        newHeight = (pMax.y - pMin.y) * this.scene.scaleY;
      } else {
        newLeft = pMin.x;
        newTop = pMin.y;
        newWidth = pMax.x - pMin.x;
        newHeight = pMax.y - pMin.y;
      }
      // since we are using width 5 and backend will include the pMax point so...
      newLeft -= newStrokeWidth;
      newTop -= newStrokeWidth;
      newWidth += newStrokeWidth + 1;
      newHeight += newStrokeWidth + 1;
      
      // if (strokeWidthDelta !== 0) {
      //   newLeft -= strokeWidthDelta;
      //   newTop -= strokeWidthDelta;
      //   newWidth += strokeWidthDelta;
      //   newHeight += strokeWidthDelta;
      // }
      
      let passStatusColor;
      if (!_.isEmpty(this.props.curIpc) && _.get(feature, 'feature_type') === 'product') {
        passStatusColor = _.get(this.props.curIpc, 'defect_count') === 0 ? '#81F499' : '#EB5757';
      } else {
        passStatusColor = _.get(feature, 'pass', true) ? '#81F499' : '#EB5757';
      }

      const rect = new fabric.Rect({
        left: newLeft,
        top: newTop,
        width: newWidth,
        height: newHeight,
        fill: 'transparent',
        stroke: colorByFeatureType ? getColorByStr(_.get(feature, 'feature_type')) : passStatusColor,
        strokeWidth: newStrokeWidth,
        selectable: true,
        strokeUniform: true, // Ensure stroke width remains consistent when scaling
      });

      // disable bbox rotation control
      rect.controls = {
        ...fabric.Rect.prototype.controls,
        mtr: new fabric.Control({
          visible: false,
        }),
      };
      // make bbox selectable by per pixel to avoid overlaped bbox can not be selected
      rect.perPixelTargetFind = true;
      rect.targetFindTolerance = 10;
      rect.set('scaledToScene', _.isNumber(this.sceneOriginalHeight) && _.isNumber(this.sceneOriginalWidth) && this.scene);
      this.fabricCanvas.add(rect);
      this.rects.push(rect);

      rect.set('prevTop', rect.top);
      rect.set('prevLeft', rect.left);
      rect.set('feature_id', _.get(feature, 'feature_id'));
      rect.set('feature_type', _.get(feature, 'feature_type'));
    }

    const productFeature = _.find(this.rects, (r) => String(r.get('featureType')) === String('product'));
    if (productFeature) {
      this.panZoomToFeature(productFeature.get('featureId'));
    }

    this.updateSceneZIndex();
  };

  resetView = () => {
    if (!this.fabricCanvas || !this.scene) return;
    const canvasWidth = this.fabricCanvas.getWidth();
    const canvasHeight = this.fabricCanvas.getHeight();
    const sceneWidth = this.scene.width * this.scene.scaleX;
    const sceneHeight = this.scene.height * this.scene.scaleY;
    const scaleX = canvasWidth / sceneWidth;
    const scaleY = canvasHeight / sceneHeight;
    const zoom = Math.min(scaleX, scaleY);

    const panX = (canvasWidth - sceneWidth * zoom) / 2;
    const panY = (canvasHeight - sceneHeight * zoom) / 2;

    this.fabricCanvas.viewportTransform = [zoom, 0, 0, zoom, panX, panY];
    this.fabricCanvas.renderAll();
  };

  updateSceneZIndex = () => {
    if (this.scene) {
      this.scene.moveTo(1);
    }
    this.rects.forEach((rect) => {
      rect.moveTo(2);
    });
  };

  panZoomToFeature = (featureId) => {
    const canvasWidth = this.fabricCanvas.getWidth();
    const canvasHeight = this.fabricCanvas.getHeight();
    const rect = _.find(this.rects, (r) => String(r.get('featureId')) === String(featureId));
    let zoom = Math.min(
      canvasWidth/ rect.width,
      canvasHeight / rect.height,
    );
    zoom -= zoom * 0.1;
    // zoom = Math.min(zoom, 5);

    // zoom and pan to the selected feature
    const rectCenter = new fabric.Point(
      rect.left + rect.width / 2,
      rect.top + rect.height / 2
    );

    this.fabricCanvas.zoomToPoint(rectCenter, zoom);

    const newRectCenter = fabric.util.transformPoint(rectCenter, this.fabricCanvas.viewportTransform);

    // Calculate the pan adjustment to center the cropped area
    const panX = (canvasWidth / 2 - newRectCenter.x) + this.fabricCanvas.viewportTransform[4];
    const panY = (canvasHeight / 2 - newRectCenter.y) + this.fabricCanvas.viewportTransform[5];

    // Apply the pan adjustment
    this.fabricCanvas.viewportTransform = [zoom, 0, 0, zoom, panX, panY];

    const newWidth = Math.min(baseBboxStrokeWidth, baseBboxStrokeWidth / zoom);
    const strokeWidthDelta = newWidth - this.rects[0].strokeWidth;

    this.rects.forEach((rect) => {
      // rect.set('stroke', String(rect.get('featureId')) === String(this.props.selectedFeatureId) ? '#56CCF2' : getColorByStr(_.get(rect, 'featureType')));
      // rect.set('stroke', String(rect.get('featureId')) === String(this.props.selectedFeatureId) ? '#56CCF2' : rect.get('pass') ? '#81F499' : '#EB5757');
      rect.set('stroke', String(rect.get('featureId')) === String(this.props.selectedFeatureId) ? '#e2e61c' : rect.get('pass') ? '#81F499' : '#EB5757');

      rect.set({
        strokeWidth: newWidth,
        left: rect.left - strokeWidthDelta,
        top: rect.top - strokeWidthDelta,
        width: rect.width + strokeWidthDelta,
        height: rect.height + strokeWidthDelta,
      });
      rect.setCoords();
    });

    // Re-render the canvas
    this.fabricCanvas.requestRenderAll();
  };

  clearScene = () => {
    if (this.fabricCanvas) {
      this.fabricCanvas.clear();
    }
    this.scene = null;
  };

  render() {
    return (
      <div className='w-full h-full' ref={this.containerRef}>
        {super.render()}
      </div>
    );
  }
};