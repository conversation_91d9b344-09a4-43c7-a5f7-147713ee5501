import { Button, Checkbox, ConfigProvider, Dropdown, InputNumber, Radio, Select, Slider, Switch, Tabs, Tooltip } from 'antd';
import _ from 'lodash';
import React, { Fragment, useContext, useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import * as THREE from 'three';
import { setCurCoordSystemSelectedPointPosition, setCurCoordSystemSelectingPointIndex, setIsCoordSystemSelectingPoints, setSelectedCameraView } from '../../actions/camera';
import { setContainerWindowLoadingLocked } from '../../actions/setting';
import { ALERT_TYPES, aoiAlert } from '../../common/alert';
import { defaultAlgorithmParams, defaultNewCameraSettingFrame, serverEndpoint } from '../../common/const';
import { CustomCollapse } from '../../common/darkModeComponents';
import { checkContrastBasedOnAlgorithmParams, checkOutlierStrengthBasedOnAlgorithmParams, checkSmoothingStrengthBasedOnAlgorithmParams, getCameraCountByCameraViewLayout, getContrastAlgorithmParams, getOutlierStrengthAlgorithmParams, getPointsAndTransformedCameraDetail, getSmoothingStrengthAlgorithmParams, getTranslationAndRotationFromSelectedPoints, handleRequestFailed, loadAndDecodePoints, translation } from '../../common/util';
import { useGetAllCameraConfigQuery, useLazyGetCameraCaptureFrameQuery, useUpdateCameraConfigMutation } from '../../services/camera';
import { systemApi } from '../../services/system';
import { CameraPreviewContext } from '../Context/Provider';
import MainMenuLayout from '../layout/MainMenuLayout';
import CameraPreviewInitCapture from '../modal/CameraPreviewInitCapture';
import PreviewImage from './PreviewImage';
import PreviewPointcloud from './PreviewPointcloud';
import { useGetProductByIdQuery, useUpdateProductMutation } from '../../services/product';
import CameraSettingProductRegister from '../modal/CameraSettingProductRegister';
import CaptureForNewProdCameraSetting from '../modal/CaptureForNewProdCameraSetting';


const ProductCameraSettingFragment = (props) => {
  const dispatch = useDispatch();

  // const { productId } = props.match.params;
  const {
    productId,
    productInfo,
    captureFrames,
    handleCaptureAll,
    threeDSceneViewer,
    setThreeDSceneViewer,
    systemMetadata,
    isCameraSettingCaptureConfirmOpened,
    setIsCameraSettingCaptureConfirmOpened,
    isRegisterProdConfirmOpened,
    setIsRegisterProdConfirmOpened,
  } = props;

  // const captureCount = useRef([]);

  // const [isCameraInitCaptureModalOpened, setIsCameraInitCaptureModalOpened] = useState(true);
  const [displayMode, setDisplayMode] = useState('image'); // image, pointcloud
  const [layoutMode, setLayoutMode] = useState('grid'); // grid, detail for image display
  const [settingTabKey, setSettingTabKey] = useState('1');
  const [selectedStep, setSelectedStep] = useState(0);
  const [selectedCameraConfig, setSelectedCameraConfig] = useState(null);
  const [selectedCameraStepConfig, setSelectedCameraStepConfig] = useState(null);
  // const [captureStepOptions, setCaptureStepOptions] = useState([]);
  const [selectedPointcloudFilterTab, setSelectedPointcloudFilterTab] = useState('outlier');
  const [curOutlierStrength, setCurOutlierStrength] = useState('off');
  const [frames, setFrames] = useState([]);
  // const [captureFrames, setCaptureFrames] = useState({}); // { 'cameraId': { imageUri: '', pointCloudUri: '' } }
  const [algorithmParams, setAlgorithmParams] = useState({});
  const [cameraTransform, setCameraTransform] = useState(null);
  const [curSmoothingStrength, setCurSmoothingStrength] = useState('off');
  const [curContrast, setCurContrast] = useState('off');
  const [d2CaptureSettings, setD2CaptureSettings] = useState({});
  // const [threeDSceneViewer, setThreeDSceneViewer] = useState();
  const [displayCoordSystemPoints, setDisplayCoordSystemPoints] = useState({});
  // const [isRegisterConfirmationModalOpened, setIsRegisterConfirmationModalOpened] = useState(false);
  const [ledBrightness, setLedBrightness] = useState({});
  // const [isCameraSettingCaptureConfirmOpened, setIsCameraSettingCaptureConfirmOpened] = useState(false);
  // const [isRegisterProdConfirmOpened, setIsRegisterProdConfirmOpened] = useState(false);

  // const [cameraCapture] = useLazyGetCameraCaptureFrameQuery();
  // const { data: camerasConfig } = useGetAllCameraConfigQuery();
  // const [updateCamerasConfig] = useUpdateCameraConfigMutation();
  // const { data: productInfo } = useGetProductByIdQuery(productId || 0);
  const camerasConfig = productInfo?.product_specific_sensor_config;
  const [updateProduct] = useUpdateProductMutation();

  const selectedCameraView = useSelector((state) => state.camera.selectedCameraView);
  const isCoordSystemSelectingPoints = useSelector((state) => state.camera.isCoordSystemSelectingPoints);
  // const cameraSettingCustomCoordSystemPoints = useSelector((state) => state.camera.cameraSettingCustomCoordSystemPoints);
  const curCoordSystemSelectedPointPosition = useSelector((state) => state.camera.curCoordSystemSelectedPointPosition);
  const curCoordSystemSelectingPointIndex = useSelector((state) => state.camera.curCoordSystemSelectingPointIndex);

  const handleSaveSettings = async (newConfig, configType) => {
    let newCameraStepConfig = _.cloneDeep(selectedCameraStepConfig);
    if (configType === 'frame') {
      newCameraStepConfig.config_3d.frame_settings = newConfig;
    } else if (configType === 'algorithmParams') {
      newCameraStepConfig.config_3d.algorithm_params = newConfig;
    } else if (configType === 'strengthType') {
      newCameraStepConfig.config_3d.algorithm_params = {
        ...algorithmParams,
        ...newConfig,
      };
    } else if (configType === 'cameraStep') {
      newCameraStepConfig = newConfig;
    } else if (configType === 'd2CaptureSettings') {
      newCameraStepConfig.config_2d = newConfig;
    } else if (configType === 'cameraTransform') {
      newCameraStepConfig.config_3d.transform = newConfig;
    } else if (configType === 'all') {
      // newCameraStepConfig = selectedCameraStepConfig;
      newCameraStepConfig.config_3d.frame_settings = frames;
      newCameraStepConfig.config_3d.algorithm_params = algorithmParams;
      newCameraStepConfig.config_3d.transform = cameraTransform;
      newCameraStepConfig.config_2d = d2CaptureSettings;
    }

    // update camera config
    const newCameraConfig = _.cloneDeep(selectedCameraConfig);
    newCameraConfig.camera_configs = _.map(newCameraConfig.camera_configs, (cameraConfig) => {
      if (cameraConfig.capture_step === selectedStep) {
        return newCameraStepConfig;
      }
      return cameraConfig;
    });
    // update in all camera configs
    const newCamerasConfig = !_.isEmpty(camerasConfig) ? _.cloneDeep(camerasConfig) : {};
    // for now we only update the first camera config
    const firstCameraId = _.get(newCamerasConfig, 'sensors[0].id');
    newCamerasConfig.sensors = _.map(newCamerasConfig.sensors, (cameraConfig) => {
      if (cameraConfig.id === firstCameraId) {
        return newCameraConfig;
      }
      return cameraConfig;
    });

    // set led brightness
    newCamerasConfig.led_brightness = {
      channel_1: _.get(ledBrightness, 'channel_1', 0),
      channel_2: _.get(ledBrightness, 'channel_2', 0),
      channel_3: _.get(ledBrightness, 'channel_3', 0),
      channel_4: _.get(ledBrightness, 'channel_4', 0),
    };

    // update in server
    dispatch(setContainerWindowLoadingLocked(true));
    const res = await updateProduct({
      product_id: Number(_.get(productInfo, 'product_id')),
      product_specific_sensor_config: newCamerasConfig,
    })
    // const res = await updateCamerasConfig(newCamerasConfig);
    if (_.get(res, 'error')) {
      dispatch(setContainerWindowLoadingLocked(false));
      handleRequestFailed('updateProductCameraConfig', _.get(res, 'error'));
      return;
    }
    aoiAlert(translation('notification.success.cameraConfigUpdated'), ALERT_TYPES.COMMON_SUCCESS);
    dispatch(setContainerWindowLoadingLocked(false));
  };

  const handleUpdateCoordSystem = async (newConfig, isReset) => {
    await handleSaveSettings(newConfig, 'cameraTransform');
    // manually re-capture
    handleCaptureAll();
    if (isReset && threeDSceneViewer) {
      dispatch(setIsCoordSystemSelectingPoints(false));
      dispatch(setCurCoordSystemSelectingPointIndex(null));
      dispatch(setCurCoordSystemSelectedPointPosition(null));
      threeDSceneViewer.resetCameraPosition();
    }
  };

  const handleColorChange = (color, value) => {
		let red = _.get(d2CaptureSettings, 'color_balance_red', 0.33);
		let green = _.get(d2CaptureSettings, 'color_balance_green', 0.33);
		let blue = _.get(d2CaptureSettings, 'color_balance_blue', 0.33);

		if (color === 'red') {
			const redChange = value - red;
			let greenChange = 0;
			let blueChange = 0;
			if (red + green + blue < 0.9999) {
				greenChange = 0;
				blueChange = 0;
			} else {
				greenChange =
					blue === 0 ? redChange * 1 : redChange * (green / (blue + green));
				blueChange =
					green === 0 ? redChange * 1 : redChange * (blue / (blue + green));

				if (redChange > 0) {
					greenChange = greenChange * -1;
					blueChange = blueChange * -1;
				}
				if (redChange < 0) {
					greenChange = greenChange * -1;
					blueChange = blueChange * -1;
				}
			}

			const newGreen = green + greenChange;
			const newBlue = blue + blueChange;

      red = value;

			if (value === 1) {
        setD2CaptureSettings({
          ...d2CaptureSettings,
          color_balance_green: 0,
          color_balance_blue: 0,
          color_balance_red: value,
        });
        blue = 0;
        green = 0;
			} else {
				if (green > 0.0001 && blue > 0.0001) {
          setD2CaptureSettings({
            ...d2CaptureSettings,
            color_balance_blue: Math.max(0, Math.min(1, newBlue)),
            color_balance_green: Math.max(0, Math.min(1, newGreen)),
            color_balance_red: value,
          });
          blue = Math.max(0, Math.min(1, newBlue));
          green = Math.max(0, Math.min(1, newGreen));
				} else if (green > 0.0001) {
          setD2CaptureSettings({
            ...d2CaptureSettings,
            color_balance_green: Math.max(0, Math.min(1, newGreen)),
            color_balance_red: value,
          });
          green = Math.max(0, Math.min(1, newGreen));
          blue = _.get(d2CaptureSettings, 'color_balance_blue', 0);
        } else if (blue > 0.0001) {
          setD2CaptureSettings({
            ...d2CaptureSettings,
            color_balance_blue: Math.max(0, Math.min(1, newBlue)),
            color_balance_red: value,
          });
          blue = Math.max(0, Math.min(1, newBlue));
          green = _.get(d2CaptureSettings, 'color_balance_green', 0);
        } else {
          setD2CaptureSettings({
            ...d2CaptureSettings,
            color_balance_red: value,
          });
        }
			}
		} else if (color === 'green') {
			const greenChange = value - green;
			let redChange = 0;
			let blueChange = 0;
			if (red + green + blue < 0.9999) {
				redChange = 0;
				blueChange = 0;
			} else {
				redChange =
					blue === 0 ? greenChange * 1 : greenChange * (red / (red + blue));
				blueChange =
					green === 0 ? greenChange * 1 : greenChange * (blue / (red + blue));

				if (greenChange > 0) {
					redChange = redChange * -1;
					blueChange = blueChange * -1;
				}
				if (greenChange < 0) {
					redChange = redChange * -1;
					blueChange = blueChange * -1;
				}
			}

			const newRed = red + redChange;
			const newBlue = blue + blueChange;

      green = value;

			if (value === 1) {
        setD2CaptureSettings({
          ...d2CaptureSettings,
          color_balance_red: 0,
          color_balance_blue: 0,
          color_balance_green: value,
        });
        red = 0;
        blue = 0;
			} else {
        if (red > 0.0001 && blue > 0.0001) {
          setD2CaptureSettings({
            ...d2CaptureSettings,
            color_balance_blue: Math.max(0, Math.min(1, newBlue)),
            color_balance_red: Math.max(0, Math.min(1, newRed)),
            color_balance_green: value,
          });
          red = Math.max(0, Math.min(1, newRed));
          blue = Math.max(0, Math.min(1, newBlue));
        } else if (red > 0.0001) {
          setD2CaptureSettings({
            ...d2CaptureSettings,
            color_balance_red: Math.max(0, Math.min(1, newRed)),
            color_balance_green: value,
          });
          red = Math.max(0, Math.min(1, newRed));
          blue = _.get(d2CaptureSettings, 'color_balance_blue', 0);
        } else if (blue > 0.0001) {
          setD2CaptureSettings({
            ...d2CaptureSettings,
            color_balance_blue: Math.max(0, Math.min(1, newBlue)),
            color_balance_green: value,
          });
          blue = Math.max(0, Math.min(1, newBlue));
          red = _.get(d2CaptureSettings, 'color_balance_red', 0);
        } else {
          setD2CaptureSettings({
            ...d2CaptureSettings,
            color_balance_green: value,
          });
        }
			}
		} else if (color === 'blue') {
			const blueChange = value - blue;
			let redChange = 0;
			let greenChange = 0;
			if (red + green + blue < 0.9999) {
				redChange = 0;
				greenChange = 0;
			} else {
				redChange =
					blue === 0 ? blueChange * 1 : blueChange * (red / (red + green));
				greenChange =
					green === 0 ? blueChange * 1 : blueChange * (green / (red + green));

				if (blueChange > 0) {
					redChange = redChange * -1;
					greenChange = greenChange * -1;
				}
				if (blueChange < 0) {
					redChange = redChange * -1;
					greenChange = greenChange * -1;
				}
			}

			const newRed = red + redChange;
			const newGreen = green + greenChange;

      blue = value;

			if (value === 1) {
        setD2CaptureSettings({
          ...d2CaptureSettings,
          color_balance_red: 0,
          color_balance_green: 0,
          color_balance_blue: value,
        });
        red = 0;
        green = 0;
			} else {
        if (red > 0.0001 && green > 0.0001) {
          setD2CaptureSettings({
            ...d2CaptureSettings,
            color_balance_green: Math.max(0, Math.min(1, newGreen)),
            color_balance_red: Math.max(0, Math.min(1, newRed)),
            color_balance_blue: value,
          });
          red = Math.max(0, Math.min(1, newRed));
          green = Math.max(0, Math.min(1, newGreen));
        } else if (red > 0.0001) {
          setD2CaptureSettings({
            ...d2CaptureSettings,
            color_balance_red: Math.max(0, Math.min(1, newRed)),
            color_balance_blue: value,
          });
          red = Math.max(0, Math.min(1, newRed));
          green = _.get(d2CaptureSettings, 'color_balance_green', 0);
        } else if (green > 0.0001) {
          setD2CaptureSettings({
            ...d2CaptureSettings,
            color_balance_green: Math.max(0, Math.min(1, newGreen)),
            color_balance_blue: value,
          });
          green = Math.max(0, Math.min(1, newGreen));
          red = _.get(d2CaptureSettings, 'color_balance_red', 0);
        } else {
          setD2CaptureSettings({
            ...d2CaptureSettings,
            color_balance_blue: value,
          });
        }
			}
		}
    return { red, green, blue };
	};
  
  useEffect(() => {
    // console.log('camerasConfig', camerasConfig);
    if (!camerasConfig) return;
    // const cameraConfig = _.find(_.get(camerasConfig, 'sensors'), { id: Number(selectedCameraView) });
    const cameraConfig = _.first(_.get(camerasConfig, 'sensors'));
    // if (_.isEmpty(camerasConfig) || _.isNull(selectedCameraView) || _.isEmpty(cameraConfig)) {
    //   setSelectedCameraConfig(null);
    //   setSelectedCameraStepConfig(null);
    //   setSelectedStep(null);
    //   return;
    // }

    setSelectedCameraConfig(cameraConfig);

    // setCaptureStepOptions(_.map(_.get(cameraConfig, 'camera_configs'), (stepConfig) => ({
    //   label: <span className='font-source text-[12px] font-normal'>{`${translation('cameraPreview.capture_step')} ${stepConfig.capture_step}`}</span>,
    //   value: stepConfig.capture_step,
    // })));
    const is2d = !_.get(cameraConfig, 'is_3d');
    if (is2d) setSettingTabKey('1');

    if (_.isEmpty(_.find(_.get(cameraConfig, 'camera_configs'), { capture_step: 0 }))) {
      setSelectedCameraStepConfig(null);
      setSelectedStep(null);
      return;
    }
    const cameraStepConfig = _.find(_.get(cameraConfig, 'camera_configs'), { capture_step: 0 });
    setSelectedCameraStepConfig(cameraStepConfig);
    setSelectedStep(0);

    setD2CaptureSettings(_.get(cameraStepConfig, 'config_2d', {}));
    if (!is2d) {
      // set frame, etc.
      setFrames(_.get(cameraStepConfig, 'config_3d.frame_settings', []));
      setAlgorithmParams(_.get(cameraStepConfig, 'config_3d.algorithm_params') || defaultAlgorithmParams);
      setCurOutlierStrength(checkOutlierStrengthBasedOnAlgorithmParams(_.get(cameraStepConfig, 'config_3d.algorithm_params', defaultAlgorithmParams)));
      setCurSmoothingStrength(checkSmoothingStrengthBasedOnAlgorithmParams(_.get(cameraStepConfig, 'config_3d.algorithm_params', defaultAlgorithmParams)));
      setCurContrast(checkContrastBasedOnAlgorithmParams(_.get(cameraStepConfig, 'config_3d.algorithm_params', defaultAlgorithmParams)));
      setCameraTransform(_.get(cameraStepConfig, 'config_3d.transform', null));
      if (threeDSceneViewer && !_.isEmpty(_.get(cameraStepConfig, 'config_3d.transform'))) {
        const sceneCam = threeDSceneViewer.getCamera();
        const lookAtVector = new THREE.Vector3(0,0, -1);
        lookAtVector.applyQuaternion(sceneCam.quaternion);
        const { points, transformedCameraDetail } = getPointsAndTransformedCameraDetail({
          translation: _.get(cameraStepConfig, 'config_3d.transform.translation', { x: 0, y: 0, z: 0 }),
          rotation: _.get(cameraStepConfig, 'config_3d.transform.rotation'),
          curCameraDetail: {
            position: { x: sceneCam.position.x, y: sceneCam.position.y, z: sceneCam.position.z },
            lookAt: { x: lookAtVector.x, y: lookAtVector.y, z: lookAtVector.z },
            up: { x: sceneCam.up.x, y: sceneCam.up.y, z: sceneCam.up.z },
          }
        });
        threeDSceneViewer.updateCamera(transformedCameraDetail);
        setDisplayCoordSystemPoints({
          '0': points.p0,
          '1': points.p1,
          '2': points.p2,
        });
      }
      return;
    }
  }, [camerasConfig, threeDSceneViewer, productInfo]);

  useEffect(() => {
    if (!threeDSceneViewer) return;
    if (settingTabKey !== '3') {
      threeDSceneViewer.clearCustomCoordSystemPoints();
    } else if (!_.isEmpty(displayCoordSystemPoints)) {
      threeDSceneViewer.addCustomCoordSystemPoint(_.get(displayCoordSystemPoints, '0'), 0);
      threeDSceneViewer.addCustomCoordSystemPoint(_.get(displayCoordSystemPoints, '1'), 1);
      threeDSceneViewer.addCustomCoordSystemPoint(_.get(displayCoordSystemPoints, '2'), 2);
    }
  }, [settingTabKey, threeDSceneViewer]);

  useEffect(() => {
    if (_.isNull(curCoordSystemSelectingPointIndex)) return;
    if (!_.isNull(curCoordSystemSelectedPointPosition)) {
      // update display coord system points state
      setDisplayCoordSystemPoints({
        ...displayCoordSystemPoints,
        [curCoordSystemSelectingPointIndex]: curCoordSystemSelectedPointPosition,
      });
      // reset
      dispatch(setCurCoordSystemSelectingPointIndex(null));
      dispatch(setCurCoordSystemSelectedPointPosition(null));
    }
  }, [curCoordSystemSelectedPointPosition]);

  return (
    <Fragment>
      {/* <CaptureForNewProdCameraSetting
        isOpened={isCameraSettingCaptureConfirmOpened}
        setIsOpened={setIsCameraSettingCaptureConfirmOpened}
        handleCapture={() => {
          handleCaptureAll(productId, systemMetadata);
        }}
      />
      <CaptureForNewProdCameraSetting
        isOpened={isRegisterProdConfirmOpened}
        setIsOpened={setIsRegisterProdConfirmOpened}
        handleCapture={() => handleProductRegister(Number(productId), _.get(productInfo, 'product_name'))}
      /> */}
      <div className='flex items-start gap-8 flex-1 self-stretch'>
        <div className='flex flex-col gap-2 flex-1 self-stretch items-center'>
          <div className='flex justify-between items-center self-stretch'>
            <div className='flex py-2 items-center gap-2 rounded-[4px] shadow'>
              { !_.isNull(selectedCameraView) && !_.isEmpty(_.get(captureFrames, `${selectedCameraView}.pointCloudUri`)) &&
                <Fragment>
                  <Switch
                    size='small'
                    checked={displayMode === 'pointcloud'}
                    onChange={() => {
                      if (displayMode === 'image') {
                        // switching to pointcloud mode
                        // check if layout mode is grid
                        if (layoutMode === 'grid') {
                          // if yes then switch to detail mode
                          // we don't support viewing all cameras' pointclouds
                          setLayoutMode('detail');
                          dispatch(setSelectedCameraView('0'));
                        }
                      }
                      setDisplayMode(displayMode === 'pointcloud' ? 'image' : 'pointcloud');
                    }}
                  />
                  <span className='font-source text-[12px] font-normal'>
                    {translation('cameraPreview.showPointCloud')}
                  </span>
                </Fragment>
              }
            </div>
            <div className='flex justify-center items-center gap-[3px]'>
              <div
                className={`duration-300 cursor-not-allowed flex w-[32px] h-[32px] py-1 px-0.5 flex-col justify-center items-center rounded-[2px] gap-2.5 ${layoutMode === 'detail' ? 'bg-gray-1' : ''}`}
              >
                <img src='/img/icn/icn_carouselLayout_white.svg' className='w-[16.8px] h-[14px] fill-white' alt='carousel' />
              </div>
              <div
                className={`duration-300 hover:ease-in hover:bg-gray-1 cursor-pointer flex w-[32px] h-[32px] py-1 px-0.5 flex-col justify-center items-center rounded-[2px] gap-2.5 ${layoutMode === 'grid' ? 'bg-gray-1' : ''}`}
                onClick={() => {
                  setLayoutMode('grid')
                  dispatch(setSelectedCameraView(null));
                }}
              >
                <img src='/img/icn/icn_gridLayout_white.svg' className='w-[14px] h-[14px] fill-white' alt='grid' />
              </div>
            </div>
          </div>
          { displayMode === 'image' && <PreviewImage
            frames={captureFrames}
            setLayoutMode={setLayoutMode}
            layoutMode={layoutMode}
          /> }
          { displayMode === 'pointcloud' && <PreviewPointcloud
            viewer={threeDSceneViewer}
            setViewer={setThreeDSceneViewer}
            frames={captureFrames}
          /> }
        </div>
        { !_.isNull(selectedCameraView) && !_.isEmpty(selectedCameraConfig) &&
        <div className='flex flex-col gap-4 w-[320px] items-start self-stretch rounded-[2px]'>
          {/* <CustomCollapse
            style={{ width: '100%' }}
            items={[{
              key: '1',
              label: <div className='flex items-center'>
                <span className='font-source text-[12px] font-semibold'>
                  {translation('cameraPreview.cameraSetup')}
                </span>
              </div>,
              children: <div className='flex pt-2 px-6 pb-4 flex-col items-start gap-1 self-stretch'>
                <Button
                  onClick={() => dispatch(setSelectedCameraView(null))}
                  style={{ width: '100%' }}
                >
                  <div className='flex items-center gap-2'>
                    <img src='/img/icn/icn_customGridLayout_blue.svg' alt='grid' className='w-[16px] h-[11px] fill-AOI-blue' />
                    <span className='font-source text-[12px] font-normal'>
                      {translation('viewBoards.showFullProduct')}
                    </span>
                  </div>
                </Button>
                <MultiViewGridSelection
                  layout={_.get(systemMetadata, 'camera_view_layout')}
                  selectedViewId={selectedCameraView}
                  onSelectView={(viewId) => dispatch(setSelectedCameraView(viewId))}
                />
              </div>
            }]}
          /> */}
          <div
            className='flex flex-col items-start flex-1 self-stretch rounded-[2px]'
            style={{ background: 'rgba(255, 255, 255, 0.05)' }}
          >
            <div className='flex py-3 px-4 items-start gap-2 self-stretch'>
              <Select
                style={{ width: '100%' }}
                options={_.map(_.get(camerasConfig, 'sensors'), (camera) => ({
                  label: <span className='font-source text-[12px] font-normal'>{`${translation('common.camera')}-${_.get(camera, 'id')}`}</span>,
                  value: camera.id,
                }))}
                value={!_.isNull(selectedCameraView) ? Number(selectedCameraView) : null}
                onChange={(value) => {
                  dispatch(setSelectedCameraView(value));
                }}
              />
            </div>
            {/* { !_.isEmpty(selectedCameraConfig) &&
              <div className='flex py-3 px-4 items-start gap-2 self-stretch'>
                <Select
                  style={{ width: '100%' }}
                  options={captureStepOptions}
                  onChange={(value) => {
                    setSelectedStep(value);
                  }}
                  value={selectedStep}
                />
              </div>
            } */}
            <div className='w-full h-[1px]' style={{ background: 'rgba(255, 255, 255, 0.10)' }} />
            { _.isEmpty(selectedCameraStepConfig) ? 
              <div className='flex flex-col items-center flex-1 self-stretch justify-center'>
                <span className='font-source text-[12px] font-normal'>
                  {translation('cameraPreview.cameraConfigEmpty')}
                </span>
              </div>
              :
              <div
                className='flex py-2 flex-col items-start flex-1 self-stretch'
                style={{ background: 'rgba(255, 255, 255, 0.05)' }}
              >
                <div className='flex flex-col self-stretch gap-1 p-2'>
                  <span className='font-source text-[14px] font-semibold'>
                    {translation('cameraPreview.ledBrightness')}
                  </span>
                  <div
                    className='gap-2.5 w-full'
                    style={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)' }}
                  >
                    <div className='flex items-center gap-1'>
                      <span className='font-source text-[12px] font-normal whitespace-nowrap'>
                        {translation('cameraPreview.channel1')}
                      </span>
                      <InputNumber
                        controls={false}
                        size='small'
                        min={0}
                        max={999}
                        precision={0}
                        addonAfter={'us'}
                        value={_.get(ledBrightness, 'channel_1', 0)}
                        onChange={(value) => {
                          setLedBrightness({
                            ...ledBrightness,
                            channel_1: value,
                          });
                        }}
                      />
                    </div>
                    <div className='flex items-center gap-1'>
                      <span className='font-source text-[12px] font-normal whitespace-nowrap'>
                        {translation('cameraPreview.channel2')}
                      </span>
                      <InputNumber
                        controls={false}
                        size='small'
                        min={0}
                        max={999}
                        addonAfter={'us'}
                        value={_.get(ledBrightness, 'channel_2', 0)}
                        onChange={(value) => {
                          setLedBrightness({
                            ...ledBrightness,
                            channel_2: value,
                          });
                        }}
                      />
                    </div>
                  </div>
                  <div
                    className='gap-2.5 w-full'
                    style={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)' }}
                  >
                    <div className='flex items-center gap-1'>
                      <span className='font-source text-[12px] font-normal whitespace-nowrap'>
                        {translation('cameraPreview.channel3')}
                      </span>
                      <InputNumber
                        controls={false}
                        size='small'
                        min={0}
                        max={999}
                        addonAfter={'us'}
                        value={_.get(ledBrightness, 'channel_3', 0)}
                        onChange={(value) => {
                          setLedBrightness({
                            ...ledBrightness,
                            channel_3: value,
                          });
                        }}
                      />
                    </div>
                    <div className='flex items-center gap-1'>
                      <span className='font-source text-[12px] font-normal whitespace-nowrap'>
                        {translation('cameraPreview.channel4')}
                      </span>
                      <InputNumber
                        controls={false}
                        size='small'
                        min={0}
                        max={999}
                        addonAfter={'us'}
                        value={_.get(ledBrightness, 'channel_4', 0)}
                        onChange={(value) => {
                          setLedBrightness({
                            ...ledBrightness,
                            channel_4: value,
                          });
                        }}
                      />
                    </div>
                  </div>
                </div>
                <div className='px-2 flex flex-col w-full'>
                  <Tabs
                    activeKey={settingTabKey}
                    onChange={(key) => setSettingTabKey(key)}
                    items={[
                      {
                        key: '1',
                        label: <span className='font-source text-[12px]'>
                          {translation('cameraPreview.frameSettings')}
                        </span>,
                      },
                      {
                        key: '2',
                        label: <span className='font-source text-[12px]'>
                          {translation('cameraPreview.pointCloudFilter')}
                        </span>,
                      },
                      {
                        key: '3',
                        label: <span className='font-source text-[12px]'>
                          {translation('cameraPreview.coordinateSystem')}
                        </span>,
                      },
                      {
                        key: '4',
                        label: <span className='font-source text-[12px]'>
                          {translation('cameraPreview.twoDCapture')}
                        </span>,
                      },
                      {
                        key: '5',
                        label: <span className='font-source text-[12px]'>
                          {translation('cameraPreview.colorBalance')}
                        </span>
                      },
                    ]}
                  />
                </div>
                {/* frame settings(3d) starts */}
                { settingTabKey === '1' && _.get(selectedCameraConfig, 'is_3d') &&
                  <div className='flex gap-2 items-start self-stretch flex-col'>
                    <div className='flex py-1 px-4 flex-col items-start gap-2.5 self-stretch'>
                      <Button
                        style={{ width: '100%' }}
                        onClick={() => {
                          if (_.isEmpty(frames)) {
                            setFrames([defaultNewCameraSettingFrame]);
                            // handleSaveSettings([defaultNewCameraSettingFrame], 'frame');
                          } else {
                            setFrames([...frames, defaultNewCameraSettingFrame]);
                            // handleSaveSettings([...frames, defaultNewCameraSettingFrame], 'frame');
                          }
                        }}
                      >
                        <div className='flex items-start gap-2'>
                          <div className='flex h-[18px] justify-center items-center'>
                            <img src='/img/icn/icn_add_white.svg' className='w-2 h-2' alt='add' />
                          </div>
                          <span className='font-source text-[12px] font-normal text-white'>
                            {translation('cameraPreview.newFrame')}
                          </span>
                        </div>
                      </Button>
                    </div>
                    <CustomCollapse
                      bordered={false}
                      style={{ width: '100%' }}
                      items={_.map(frames, (frame, index) => ({
                        key: String(index),
                        label: <div className='flex items-center justify-between w-full'>
                          <span className='font-source text-[12px] font-semibold'>
                            {`${translation('cameraPreview.frame')} ${index + 1}`}
                          </span>
                          <Dropdown
                            menu={{
                              items: [
                                {
                                  key: '1',
                                  label: <span className='font-source text-[12px] font-normal'>
                                    {translation('common.remove')}
                                  </span>,
                                  onClick: () => {
                                    const newFrames = _.cloneDeep(frames);
                                    newFrames.splice(index, 1);
                                    setFrames(newFrames);
                                    // handleSaveSettings(newFrames, 'frame');
                                  },
                                },
                              ],
                            }}
                          >
                            <div className='flex w-6 h-6 justify-center items-center cursor-pointer'>
                              <div className='flex w-[14px] flex-col items-center justify-center shrink-0'>
                                <img src='/img/icn/icn_ellipsis_white.svg' className='w-[14px] h-[2.3px]' alt='ellipsis' />
                              </div>
                            </div>
                          </Dropdown>
                        </div>,
                        children: <div className='flex px-4 py-2 flex-col items-start gap-1 self-stretch'>
                          <div className='flex flex-col items-start gap-1 self-stretch'>
                            <div style={{ display: 'grid', gridTemplateColumns: '84px 1fr 36px', gap: '0px 16px', width: '100%' }}>
                              <div className='flex items-center w-[84px] py-1 self-stretch'>
                                <span className='font-source text-[12px] font-normal'>
                                  {translation('cameraPreview.exposureStop')}
                                </span>
                              </div>
                              <Slider
                                min={-1}
                                max={4}
                                value={_.get(frame, 'exposure_stop')}
                                onChange={(value) => {
                                  const newFrames = _.cloneDeep(frames);
                                  newFrames[index].exposure_stop = value;
                                  setFrames(newFrames);
                                }}
                                onChangeComplete={(value) => {
                                  const newFrames = _.cloneDeep(frames);
                                  newFrames[index].exposure_stop = value;
                                  setFrames(newFrames);
                                  // handleSaveSettings(newFrames, 'frame');
                                }}
                              />
                              <div className='flex items-start gap-0.5 self-stretch'>
                                <InputNumber
                                  controls={false}
                                  value={_.get(frame, 'exposure_stop')}
                                  min={-1}
                                  max={4}
                                  // onBlur={(e) => {
                                  //   const newFrames = _.cloneDeep(frames);
                                  //   newFrames[index].exposure_stop = Number(e.target.value);
                                  //   setFrames(newFrames);
                                  //   // handleSaveSettings(newFrames, 'frame');
                                  // }}
                                  onChange={(value) => {
                                    const newFrames = _.cloneDeep(frames);
                                    newFrames[index].exposure_stop = value;
                                    setFrames(newFrames);
                                  }}
                                />
                              </div>
                            </div>
                            <div style={{ display: 'grid', gridTemplateColumns: '84px 1fr 36px', gap: '0px 16px', width: '100%' }}>
                              <div className='flex items-center w-[84px] py-1 self-stretch'>
                                <span className='font-source text-[12px] font-normal'>
                                  {translation('cameraPreview.brightness')}
                                </span>
                              </div>
                              <Slider
                                min={1}
                                max={3}
                                value={_.get(frame, 'brightness')}
                                onChange={(value) => {
                                  const newFrames = _.cloneDeep(frames);
                                  newFrames[index].brightness = value;
                                  setFrames(newFrames);
                                }}
                                onChangeComplete={(value) => {
                                  const newFrames = _.cloneDeep(frames);
                                  newFrames[index].brightness = value;
                                  setFrames(newFrames);
                                  // handleSaveSettings(newFrames, 'frame');
                                }}
                              />
                              <div className='flex items-start gap-0.5 self-stretch'>
                                <InputNumber
                                  controls={false}
                                  value={_.get(frame, 'brightness')}
                                  min={1}
                                  max={3}
                                  // onBlur={(e) => {
                                  //   const newFrames = _.cloneDeep(frames);
                                  //   newFrames[index].brightness = Number(e.target.value);
                                  //   setFrames(newFrames);
                                  //   // handleSaveSettings(newFrames, 'frame');
                                  // }}
                                  onChange={(value) => {
                                    const newFrames = _.cloneDeep(frames);
                                    newFrames[index].brightness = value;
                                    setFrames(newFrames);
                                  }}
                                />
                              </div>
                            </div>
                            <div style={{ display: 'grid', gridTemplateColumns: '84px 1fr 72px', gap: '0px 16px', width: '100%', alignItems: 'center' }}>
                              <div className='flex items-center w-[84px] py-1 self-stretch'>
                                <span className='font-source text-[12px] font-normal'>
                                  {translation('cameraPreview.gain')}
                                </span>
                              </div>
                              <Slider
                                min={0}
                                max={3}
                                value={_.get(frame, 'gain')}
                                onChange={(value) => {
                                  const newFrames = _.cloneDeep(frames);
                                  newFrames[index].gain = value;
                                  setFrames(newFrames);
                                }}
                                onChangeComplete={(value) => {
                                  const newFrames = _.cloneDeep(frames);
                                  newFrames[index].gain = value;
                                  setFrames(newFrames);
                                  // handleSaveSettings(newFrames, 'frame');
                                }}
                              />
                              <div className='flex items-center gap-0.5 self-stretch'>
                                <InputNumber
                                  size='small'
                                  style={{ width: '72px' }}
                                  controls={false}
                                  value={_.get(frame, 'gain')}
                                  min={0}
                                  max={3}
                                  // onBlur={(e) => {
                                  //   const newFrames = _.cloneDeep(frames);
                                  //   newFrames[index].gain = Number(e.target.value);
                                  //   setFrames(newFrames);
                                  //   // handleSaveSettings(newFrames, 'frame');
                                  // }}
                                  onChange={(value) => {
                                    const newFrames = _.cloneDeep(frames);
                                    newFrames[index].gain = value;
                                    setFrames(newFrames);
                                  }}
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                      }))}
                    />
                  </div>
                }
                {/* frame settings(3d) ends */}
                {/* frame settings(2d) starts */}
                { settingTabKey === '1' && !_.get(selectedCameraConfig, 'is_3d') &&
                  <div className='flex px-4 flex-col items-start gap-1 self-stretch'>
                    <div className='flex flex-col items-start gap-1 self-stretch'>
                      <div style={{ display: 'grid', gridTemplateColumns: '84px 1fr 72px', gap: '0px 16px', width: '100%', alignItems: 'center' }}>
                        <span className='font-source text-[12px] font-normal'>
                          {translation('cameraPreview.exposureTime')}
                        </span>
                        <Slider
                          min={20}
                          max={450}
                          value={_.get(d2CaptureSettings, 'exposure_time', 20)}
                          onChange={(value) => {
                            // const newCameraStepConfig = _.cloneDeep(selectedCameraStepConfig);
                            // newCameraStepConfig.config_2d.exposure_time = value;
                            // setSelectedCameraStepConfig(newCameraStepConfig);
                            setD2CaptureSettings({
                              ...d2CaptureSettings,
                              exposure_time: value,
                            });
                            // handleSaveSettings(newCameraStepConfig, 'cameraStep');
                          }}
                        />
                        <div className='flex items-center gap-0.5 self-stretch'>
                          <InputNumber
                            size='small'
                            style={{ width: '72px' }}
                            controls={false}
                            min={20}
                            max={450}
                            value={_.get(d2CaptureSettings, 'exposure_time', 20)}
                            // onBlur={(e) => {
                            //   const newCameraStepConfig = _.cloneDeep(selectedCameraStepConfig);
                            //   newCameraStepConfig.config_2d.exposure_time = Number(e.target.value);
                            //   setSelectedCameraStepConfig(newCameraStepConfig);
                            //   // handleSaveSettings(newCameraStepConfig, 'cameraStep');
                            // }}
                            onChange={(value) => {
                              // const newCameraStepConfig = _.cloneDeep(selectedCameraStepConfig);
                              // newCameraStepConfig.config_2d.exposure_time = value;
                              // setSelectedCameraStepConfig(newCameraStepConfig);
                              setD2CaptureSettings({
                                ...d2CaptureSettings,
                                exposure_time: value,
                              });
                            }}
                          />
                        </div>
                      </div>
                      <div style={{ display: 'grid', gridTemplateColumns: '84px 1fr 72px', gap: '0px 16px', width: '100%', alignItems: 'center' }}>
                        <span className='font-source text-[12px] font-normal'>
                          {translation('cameraPreview.gain')}
                        </span>
                        <Slider
                          min={0}
                          max={1000}
                          value={_.get(d2CaptureSettings, 'gain', 0)}
                          onChange={(value) => {
                            // const newCameraStepConfig = _.cloneDeep(selectedCameraStepConfig);
                            // newCameraStepConfig.config_2d.exposure_time = value;
                            // setSelectedCameraStepConfig(newCameraStepConfig);
                            // handleSaveSettings(newCameraStepConfig, 'cameraStep');
                            setD2CaptureSettings({
                              ...d2CaptureSettings,
                              gain: value,
                            });
                          }}
                        />
                        <div className='flex items-center gap-0.5 self-stretch'>
                          <InputNumber
                            size='small'
                            style={{ width: '72px' }}
                            controls={false}
                            min={0}
                            max={1000}
                            value={_.get(d2CaptureSettings, 'gain')}
                            // onBlur={(e) => {
                            //   const newCameraStepConfig = _.cloneDeep(selectedCameraStepConfig);
                            //   newCameraStepConfig.config_2d.exposure_time = Number(e.target.value);
                            //   setSelectedCameraStepConfig(newCameraStepConfig);
                            //   // handleSaveSettings(newCameraStepConfig, 'cameraStep');
                            // }}
                            onChange={(value) => {
                              // const newCameraStepConfig = _.cloneDeep(selectedCameraStepConfig);
                              // newCameraStepConfig.config_2d.exposure_time = value;
                              // setSelectedCameraStepConfig(newCameraStepConfig);
                              setD2CaptureSettings({
                                ...d2CaptureSettings,
                                gain: value,
                              });
                            }}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                }
                {/* frame settings(2d) ends */}
                {/* pointcloud filter starts */}
                { settingTabKey === '2' &&
                  <div className='flex py-1 flex-col items-start justify-center gap-4 self-stretch'>
                    <div className='flex px-4 flex-col items-start gap-2 self-stretch'>
                      <Select
                        style={{ width: '100%' }}
                        options={[
                          {
                            label: <span className='font-source text-[12px] font-normal'>
                              {translation('cameraPreview.outlier')}
                            </span>,
                            value: 'outlier',
                          },
                          {
                            label: <span className='font-source text-[12px] font-normal'>
                              {translation('cameraPreview.smoothing')}
                            </span>,
                            value: 'smoothing',
                          },
                          {
                            label: <span className='font-source text-[12px] font-normal'>
                              {translation('cameraPreview.contrast')}
                            </span>,
                            value: 'contrast',
                          },
                          {
                            label: <span className='font-source text-[12px] font-normal'>
                              {translation('cameraPreview.correction')}
                            </span>,
                            value: 'correction',
                          },
                        ]}
                        value={selectedPointcloudFilterTab}
                        onChange={(value) => setSelectedPointcloudFilterTab(value)}
                      />
                    </div>
                    <div className='w-full h-[1px]' style={{ background: 'rgba(255, 255, 255, 0.10)' }} />
                    {/* outlier starts */}
                    { selectedPointcloudFilterTab === 'outlier' &&
                      <Fragment>
                        <div className='flex px-4 flex-col items-start gap-2 self-stretch'>
                          <div className='flex items-center gap-4 self-stretch'>
                            <div className='flex items-center px-0.5 flex-1 gap-1'>
                              <span className='font-source text-[12px] font-normal whitespace-nowrap'>
                                {translation('cameraPreview.outlierStrength')}
                              </span>

                            </div>
                            <Select
                              style={{ width: '100%' }}
                              value={curOutlierStrength}
                              onChange={(value) => {
                                setCurOutlierStrength(value);
                                setAlgorithmParams({
                                  ...algorithmParams,
                                  ...getOutlierStrengthAlgorithmParams(value),
                                });
                                // handleSaveSettings({
                                //   ...algorithmParams,
                                //   ...getOutlierStrengthAlgorithmParams(value)
                                // }, 'algorithmParams');
                              }}
                              options={[
                                {
                                  label: <span className='font-source text-[12px] font-normal'>
                                    {translation('cameraPreview.off')}
                                  </span>,
                                  value: 'off',
                                },
                                {
                                  label: <span className='font-source text-[12px] font-normal'>
                                    {translation('cameraPreview.weak')}
                                  </span>,
                                  value: 'weak',
                                },
                                {
                                  label: <span className='font-source text-[12px] font-normal'>
                                    {translation('cameraPreview.normal')}
                                  </span>,
                                  value: 'normal',
                                },
                                {
                                  label: <span className='font-source text-[12px] font-normal'>
                                    {translation('cameraPreview.strong')}
                                  </span>,
                                  value: 'strong',
                                },
                                {
                                  label: <span className='font-source text-[12px] font-normal'>
                                    {translation('cameraPreview.customized')}
                                  </span>,
                                  value: 'customized',
                                },
                              ]}
                            />
                          </div>
                        </div>
                        <div className='w-full h-[1px]' style={{ background: 'rgba(255, 255, 255, 0.10)' }} />
                        <div className='flex px-4 flex-col items-start gap-2 self-stretch'>
                          <div className='flex items-center gap-2 self-stretch'>
                            <span className='font-source text-[12px] font-normal'>
                              {translation('cameraPreview.outlierRemoval')}
                            </span>
                          </div>
                          <div className='flex flex-col items-start gap-1 self-stretch'>
                            <div className='flex flex-col items-start gap-1 self-stretch'>
                              <div className='flex py-1 flex-col justify-center gap-2 self-stretch items-start'>
                                <div className='flex h-[26px] py-1 items-center gap-2 self-stretch'>
                                  <Checkbox
                                    size='small'
                                    checked={_.get(algorithmParams, 'enable_outlier', false)}
                                    onChange={(e) => {
                                      const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                      newAlgorithmParams.enable_outlier = e.target.checked;
                                      setAlgorithmParams(newAlgorithmParams);
                                      // handleSaveSettings(newAlgorithmParams, 'algorithmParams');
                                    }}
                                    disabled={curOutlierStrength !== 'customized'}
                                  />
                                  <span className='font-source text-[12px] font-normal'>
                                    {translation('cameraPreview.outlierThresholdLevel')}
                                  </span>
                                </div>
                                <div className='flex h-[26px] items-center gap-2 self-stretch'>
                                  <div className='flex flex-col flex-1'>
                                    <Slider
                                      min={0}
                                      max={100}
                                      disabled={curOutlierStrength !== 'customized' || _.get(algorithmParams, 'enable_outlier') === false}
                                      value={_.get(algorithmParams, 'outlier')}
                                      onChange={(value) => {
                                        const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                        newAlgorithmParams.outlier = value;
                                        setAlgorithmParams(newAlgorithmParams);
                                      }}
                                      onChangeComplete={(value) => {
                                        const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                        newAlgorithmParams.outlier = value;
                                        setAlgorithmParams(newAlgorithmParams);
                                        // handleSaveSettings(newAlgorithmParams, 'algorithmParams');
                                      }}
                                    />
                                  </div>
                                  <div className='flex items-start gap-2 self-stretch'>
                                    <InputNumber
                                      style={{ width: '36px' }}
                                      size='small'
                                      controls={false}
                                      min={0}
                                      max={100}
                                      value={_.get(algorithmParams, 'outlier')}
                                      // onBlur={(e) => {
                                      //   const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                      //   newAlgorithmParams.outlier = Number(e.target.value);
                                      //   setAlgorithmParams(newAlgorithmParams);
                                      //   // handleSaveSettings(newAlgorithmParams, 'algorithmParams');
                                      // }}
                                      onChange={(value) => {
                                        const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                        newAlgorithmParams.outlier = value;
                                        setAlgorithmParams(newAlgorithmParams);
                                      }}
                                      disabled={curOutlierStrength !== 'customized' || _.get(algorithmParams, 'enable_outlier') === false}
                                    />
                                  </div>
                                </div>
                              </div>
                            </div>
                            <div className='flex flex-col items-start gap-1 self-stretch'>
                              <div className='flex py-1 flex-col justify-center gap-2 self-stretch items-start'>
                                <div className='flex h-[26px] py-1 items-center gap-2 self-stretch'>
                                  <Checkbox
                                    size='small'
                                    checked={_.get(algorithmParams, 'enable_face_normal', false)}
                                    onChange={(e) => {
                                      const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                      newAlgorithmParams.enable_face_normal = e.target.checked;
                                      setAlgorithmParams(newAlgorithmParams);
                                      // handleSaveSettings(newAlgorithmParams, 'algorithmParams');
                                    }}
                                    disabled={curOutlierStrength !== 'customized'}
                                  />
                                  <span className='font-source text-[12px] font-normal'>
                                    {translation('cameraPreview.faceNormalFilter')}
                                  </span>
                                </div>
                                <div className='flex h-[26px] items-center gap-2 self-stretch'>
                                  <div className='flex flex-col flex-1'>
                                    <Slider
                                      min={0}
                                      max={40}
                                      disabled={curOutlierStrength !== 'customized' || _.get(algorithmParams, 'enable_face_normal') === false}
                                      value={_.get(algorithmParams, 'face_normal_angle')}
                                      onChange={(value) => {
                                        const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                        newAlgorithmParams.face_normal_angle = value;
                                        setAlgorithmParams(newAlgorithmParams);
                                      }}
                                      onChangeComplete={(value) => {
                                        const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                        newAlgorithmParams.face_normal_angle = value;
                                        setAlgorithmParams(newAlgorithmParams);
                                        // handleSaveSettings(newAlgorithmParams, 'algorithmParams');
                                      }}
                                    />
                                  </div>
                                  <div className='flex items-start gap-2 self-stretch'>
                                    <InputNumber
                                      style={{ width: '36px' }}
                                      size='small'
                                      controls={false}
                                      min={0}
                                      max={40}
                                      value={_.get(algorithmParams, 'face_normal_angle')}
                                      // onBlur={(e) => {
                                      //   const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                      //   newAlgorithmParams.face_normal_angle = Number(e.target.value);
                                      //   setAlgorithmParams(newAlgorithmParams);
                                      //   // handleSaveSettings(newAlgorithmParams, 'algorithmParams');
                                      // }}
                                      onChange={(value) => {
                                        const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                        newAlgorithmParams.face_normal_angle = value;
                                        setAlgorithmParams(newAlgorithmParams);
                                      }}
                                      disabled={curOutlierStrength !== 'customized' || _.get(algorithmParams, 'enable_face_normal') === false}
                                    />
                                  </div>
                                </div>
                              </div>
                            </div>
                            <div className='flex flex-col items-start gap-1 self-stretch'>
                              <div className='flex py-1 flex-col justify-center gap-2 self-stretch items-start'>
                                <div className='flex h-[26px] py-1 items-center gap-2 self-stretch'>
                                  <Checkbox
                                    size='small'
                                    checked={_.get(algorithmParams, 'enable_cluster_filter', false)}
                                    onChange={(e) => {
                                      const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                      newAlgorithmParams.enable_cluster_filter = e.target.checked;
                                      setAlgorithmParams(newAlgorithmParams);
                                      // handleSaveSettings(newAlgorithmParams, 'algorithmParams');
                                    }}
                                    disabled={curOutlierStrength !== 'customized'}
                                  />
                                  <span className='font-source text-[12px] font-normal'>
                                    {translation('cameraPreview.clusterFilter')}
                                  </span>
                                </div>
                                <div className='flex h-[26px] py-1 pl-8 items-center gap-2 self-stretch'>
                                  <span className='font-source text-[12px] font-normal'>
                                    {translation('cameraPreview.strength')}
                                  </span>
                                </div>
                                <div className='flex h-[26px] pl-8 items-center gap-2 self-stretch'>
                                  <div className='flex flex-col flex-1'>
                                    <Slider
                                      min={0}
                                      max={50}
                                      disabled={curOutlierStrength !== 'customized' || _.get(algorithmParams, 'enable_cluster_filter') === false}
                                      value={_.get(algorithmParams, 'cluster_filter_strength')}
                                      onChange={(value) => {
                                        const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                        newAlgorithmParams.cluster_filter_strength = value;
                                        setAlgorithmParams(newAlgorithmParams);
                                      }}
                                      onChangeComplete={(value) => {
                                        const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                        newAlgorithmParams.cluster_filter_strength = value;
                                        setAlgorithmParams(newAlgorithmParams);
                                        // handleSaveSettings(newAlgorithmParams, 'algorithmParams');
                                      }}
                                    />
                                  </div>
                                  <div className='flex items-start gap-2 self-stretch'>
                                    <InputNumber
                                      style={{ width: '36px' }}
                                      size='small'
                                      controls={false}
                                      min={0}
                                      max={50}
                                      value={_.get(algorithmParams, 'cluster_filter_strength')}
                                      // onBlur={(e) => {
                                      //   const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                      //   newAlgorithmParams.cluster_filter_strength = Number(e.target.value);
                                      //   setAlgorithmParams(newAlgorithmParams);
                                      //   // handleSaveSettings(newAlgorithmParams, 'algorithmParams');
                                      // }}
                                      onChange={(value) => {
                                        const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                        newAlgorithmParams.cluster_filter_strength = value;
                                        setAlgorithmParams(newAlgorithmParams);
                                      }}
                                      disabled={curOutlierStrength !== 'customized' || _.get(algorithmParams, 'enable_cluster_filter') === false}
                                    />
                                  </div>
                                </div>
                                <div className='flex h-[26px] py-1 pl-8 items-center gap-2 self-stretch'>
                                  <span className='font-source text-[12px] font-normal'>
                                    {translation('cameraPreview.neighborDistance')}
                                  </span>
                                </div>
                                <div className='flex h-[26px] items-center gap-2 self-stretch pl-8'>
                                  <div className='flex flex-col flex-1'>
                                    <Slider
                                      min={5}
                                      max={100}
                                      disabled={curOutlierStrength !== 'customized' || _.get(algorithmParams, 'enable_cluster_filter') === false}
                                      value={_.get(algorithmParams, 'cluster_neighbor_distance')}
                                      onChange={(value) => {
                                        const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                        newAlgorithmParams.cluster_neighbor_distance = value;
                                        setAlgorithmParams(newAlgorithmParams);
                                      }}
                                      onChangeComplete={(value) => {
                                        const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                        newAlgorithmParams.cluster_neighbor_distance = value;
                                        setAlgorithmParams(newAlgorithmParams);
                                        // handleSaveSettings(newAlgorithmParams, 'algorithmParams');
                                      }}
                                    />
                                  </div>
                                  <div className='flex items-start gap-2 self-stretch'>
                                    <InputNumber
                                      style={{ width: '36px' }}
                                      size='small'
                                      controls={false}
                                      min={5}
                                      max={100}
                                      value={_.get(algorithmParams, 'cluster_neighbor_distance')}
                                      // onBlur={(e) => {
                                      //   const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                      //   newAlgorithmParams.cluster_neighbor_distance = Number(e.target.value);
                                      //   setAlgorithmParams(newAlgorithmParams);
                                      //   // handleSaveSettings(newAlgorithmParams, 'algorithmParams');
                                      // }}
                                      onChange={(value) => {
                                        const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                        newAlgorithmParams.cluster_neighbor_distance = value;
                                        setAlgorithmParams(newAlgorithmParams);
                                      }}
                                      disabled={curOutlierStrength !== 'customized' || _.get(algorithmParams, 'enable_cluster_filter') === false}
                                    />
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </Fragment>
                    }
                    {/* outlier ends */}
                    {/* smoothing starts */}
                    { selectedPointcloudFilterTab === 'smoothing' &&
                      <Fragment>
                        <div className='flex px-4 flex-col items-start gap-2 self-stretch'>
                          <div className='flex items-center gap-4 self-stretch'>
                            <div className='flex items-center px-0.5 flex-1 gap-1'>
                              <span className='font-source text-[12px] font-normal whitespace-nowrap'>
                                {translation('cameraPreview.smoothingStrength')}
                              </span>

                            </div>
                            <Select
                              style={{ width: '100%' }}
                              value={curSmoothingStrength}
                              onChange={(value) => {
                                setCurSmoothingStrength(value);
                                setAlgorithmParams({
                                  ...algorithmParams,
                                  ...getSmoothingStrengthAlgorithmParams(value)
                                });
                                // handleSaveSettings({
                                //   ...algorithmParams,
                                //   ...getSmoothingStrengthAlgorithmParams(value)
                                // }, 'algorithmParams');
                              }}
                              options={[
                                {
                                  label: <span className='font-source text-[12px] font-normal'>
                                    {translation('cameraPreview.off')}
                                  </span>,
                                  value: 'off',
                                },
                                {
                                  label: <span className='font-source text-[12px] font-normal'>
                                    {translation('cameraPreview.weak')}
                                  </span>,
                                  value: 'weak',
                                },
                                {
                                  label: <span className='font-source text-[12px] font-normal'>
                                    {translation('cameraPreview.normal')}
                                  </span>,
                                  value: 'normal',
                                },
                                {
                                  label: <span className='font-source text-[12px] font-normal'>
                                    {translation('cameraPreview.strong')}
                                  </span>,
                                  value: 'strong',
                                },
                                {
                                  label: <span className='font-source text-[12px] font-normal'>
                                    {translation('cameraPreview.customized')}
                                  </span>,
                                  value: 'customized',
                                },
                              ]}
                            />
                          </div>
                        </div>
                        <div className='w-full h-[1px]' style={{ background: 'rgba(255, 255, 255, 0.10)' }} />
                        <div className='flex px-4 flex-col items-start gap-2 self-stretch'>
                        <div className='flex items-center gap-2 self-stretch'>
                            <span className='font-source text-[12px] font-normal'>
                              {translation('cameraPreview.smoothingSettings')}
                            </span>
                          </div>
                          <div className='flex flex-col items-start gap-1 self-stretch'>
                            <div className='flex flex-col items-start gap-1 self-stretch'>
                              <div className='flex py-1 flex-col justify-center gap-2 self-stretch items-start'>
                                <div className='flex h-[26px] py-1 items-center gap-2 self-stretch'>
                                  <Checkbox
                                    size='small'
                                    checked={_.get(algorithmParams, 'enable_gaussian', false)}
                                    onChange={(e) => {
                                      const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                      newAlgorithmParams.enable_gaussian = e.target.checked;
                                      setAlgorithmParams(newAlgorithmParams);
                                      // handleSaveSettings(newAlgorithmParams, 'algorithmParams');
                                    }}
                                    disabled={curSmoothingStrength !== 'customized'}
                                  />
                                  <span className='font-source text-[12px] font-normal'>
                                    {translation('cameraPreview.gaussianFilter')}
                                  </span>
                                </div>
                                <div className='flex h-[26px] items-center gap-2 self-stretch'>
                                  <div className='flex flex-col flex-1'>
                                    <Slider
                                      min={0}
                                      max={40}
                                      disabled={curSmoothingStrength !== 'customized' || _.get(algorithmParams, 'enable_gaussian') === false}
                                      value={_.get(algorithmParams, 'gaussian_strength')}
                                      onChange={(value) => {
                                        const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                        newAlgorithmParams.gaussian_strength = value;
                                        setAlgorithmParams(newAlgorithmParams);
                                      }}
                                      onChangeComplete={(value) => {
                                        const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                        newAlgorithmParams.gaussian_strength = value;
                                        setAlgorithmParams(newAlgorithmParams);
                                        // handleSaveSettings(newAlgorithmParams, 'algorithmParams');
                                      }}
                                    />
                                  </div>
                                  <div className='flex items-start gap-2 self-stretch'>
                                    <InputNumber
                                      style={{ width: '36px' }}
                                      size='small'
                                      controls={false}
                                      min={0}
                                      max={40}
                                      value={_.get(algorithmParams, 'gaussian_strength')}
                                      // onBlur={(e) => {
                                      //   const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                      //   newAlgorithmParams.gaussian_strength = Number(e.target.value);
                                      //   setAlgorithmParams(newAlgorithmParams);
                                      //   // handleSaveSettings(newAlgorithmParams, 'algorithmParams');
                                      // }}
                                      onChange={(value) => {
                                        const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                        newAlgorithmParams.gaussian_strength = value;
                                        setAlgorithmParams(newAlgorithmParams);
                                      }}
                                      disabled={curSmoothingStrength !== 'customized' || _.get(algorithmParams, 'enable_gaussian') === false}
                                    />
                                  </div>
                                </div>
                              </div>
                            </div>
                            <div className='flex flex-col items-start gap-1 self-stretch'>
                              <div className='flex py-1 flex-col justify-center gap-2 self-stretch items-start'>
                                <div className='flex h-[26px] py-1 items-center gap-2 self-stretch'>
                                  <Checkbox
                                    size='small'
                                    checked={_.get(algorithmParams, 'enable_median', false)}
                                    onChange={(e) => {
                                      const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                      newAlgorithmParams.enable_median = e.target.checked;
                                      setAlgorithmParams(newAlgorithmParams);
                                      // handleSaveSettings(newAlgorithmParams, 'algorithmParams');
                                    }}
                                    disabled={curSmoothingStrength !== 'customized'}
                                  />
                                  <span className='font-source text-[12px] font-normal'>
                                    {translation('cameraPreview.medianFilter')}
                                  </span>
                                </div>
                                <div className='flex h-[26px] items-center gap-2 self-stretch'>
                                  <div className='flex flex-col flex-1'>
                                    <Slider
                                      min={3}
                                      max={5}
                                      step={2}
                                      disabled={curSmoothingStrength !== 'customized' || _.get(algorithmParams, 'enable_median') === false}
                                      value={_.get(algorithmParams, 'median_kernel_size')}
                                      onChange={(value) => {
                                        const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                        newAlgorithmParams.median_kernel_size = value;
                                        setAlgorithmParams(newAlgorithmParams);
                                      }}
                                      onChangeComplete={(value) => {
                                        const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                        newAlgorithmParams.median_kernel_size = value;
                                        setAlgorithmParams(newAlgorithmParams);
                                        // handleSaveSettings(newAlgorithmParams, 'algorithmParams');
                                      }}
                                    />
                                  </div>
                                  <div className='flex items-start gap-2 self-stretch'>
                                    <InputNumber
                                      style={{ width: '36px' }}
                                      size='small'
                                      controls={false}
                                      min={3}
                                      max={5}
                                      step={2}
                                      value={_.get(algorithmParams, 'median_kernel_size')}
                                      // onBlur={(e) => {
                                      //   const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                      //   newAlgorithmParams.median_kernel_size = Number(e.target.value);
                                      //   setAlgorithmParams(newAlgorithmParams);
                                      //   // handleSaveSettings(newAlgorithmParams, 'algorithmParams');
                                      // }}
                                      onChange={(value) => {
                                        const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                        newAlgorithmParams.median_kernel_size = value;
                                        setAlgorithmParams(newAlgorithmParams);
                                      }}
                                      disabled={curSmoothingStrength !== 'customized' || _.get(algorithmParams, 'enable_median') === false}
                                    />
                                  </div>
                                </div>
                              </div>
                            </div>
                            <div className='flex flex-col items-start gap-1 self-stretch'>
                              <div className='flex py-1 flex-col justify-center gap-2 self-stretch items-start'>
                                <div className='flex h-[26px] py-1 items-center gap-2 self-stretch justify-between'>
                                  <div className='flex h-[26px] py-1 items-center gap-2 self-stretch'>
                                    <Checkbox
                                      size='small'
                                      checked={_.get(algorithmParams, 'enable_smooth', false)}
                                      onChange={(e) => {
                                        const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                        newAlgorithmParams.enable_smooth = e.target.checked;
                                        setAlgorithmParams(newAlgorithmParams);
                                        // handleSaveSettings(newAlgorithmParams, 'algorithmParams');
                                      }}
                                      disabled={curSmoothingStrength !== 'customized'}
                                    />
                                    <span className='font-source text-[12px] font-normal'>
                                      {translation('cameraPreview.smoothFilter')}
                                    </span>
                                  </div>
                                  <Select
                                    disabled={curSmoothingStrength !== 'customized' || _.get(algorithmParams, 'enable_smooth') === false}
                                    options={[
                                      {
                                        value: 'OneHundredth',
                                        label: '0.01',
                                      },
                                      {
                                        value: 'FiveHundredths',
                                        label: '0.05',
                                      },
                                      {
                                        value: 'OneTenth',
                                        label: '0.1',
                                      },
                                      {
                                        value: 'Half',
                                        label: '0.5',
                                      },
                                      {
                                        value: 'One',
                                        label: '1.0',
                                      },
                                      {
                                        value: 'Two',
                                        label: '2.0',
                                      },
                                    ]}
                                    size="small"
                                    value={_.get(algorithmParams, 'smooth_granularity')}
                                    onChange={(value) => {
                                      const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                      newAlgorithmParams.smooth_granularity = value;
                                      setAlgorithmParams(newAlgorithmParams);
                                      // handleSaveSettings(newAlgorithmParams, 'algorithmParams');
                                    }}
                                  />
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </Fragment>
                    }
                    {/* smoothing ends */}
                    {/* contrast starts */}
                    { selectedPointcloudFilterTab === 'contrast' &&
                      <Fragment>
                        <div className='flex px-4 flex-col items-start gap-2 self-stretch'>
                          <div className='flex items-center gap-4 self-stretch'>
                            <div className='flex items-center px-0.5 flex-1 gap-1'>
                              <span className='font-source text-[12px] font-normal whitespace-nowrap'>
                                {translation('cameraPreview.contrast')}
                              </span>

                            </div>
                            <Select
                              style={{ width: '100%' }}
                              value={curContrast}
                              onChange={(value) => {
                                setCurContrast(value);
                                setAlgorithmParams({
                                  ...algorithmParams,
                                  ...getContrastAlgorithmParams(value)
                                });
                                // handleSaveSettings({
                                //   ...algorithmParams,
                                //   ...getContrastAlgorithmParams(value)
                                // }, 'algorithmParams');
                              }}
                              options={[
                                {
                                  label: <span className='font-source text-[12px] font-normal'>
                                    {translation('cameraPreview.off')}
                                  </span>,
                                  value: 'off',
                                },
                                {
                                  label: <span className='font-source text-[12px] font-normal'>
                                    {translation('cameraPreview.weak')}
                                  </span>,
                                  value: 'weak',
                                },
                                {
                                  label: <span className='font-source text-[12px] font-normal'>
                                    {translation('cameraPreview.normal')}
                                  </span>,
                                  value: 'normal',
                                },
                                {
                                  label: <span className='font-source text-[12px] font-normal'>
                                    {translation('cameraPreview.strong')}
                                  </span>,
                                  value: 'strong',
                                },
                                {
                                  label: <span className='font-source text-[12px] font-normal'>
                                    {translation('cameraPreview.customized')}
                                  </span>,
                                  value: 'customized',
                                },
                              ]}
                            />
                          </div>
                        </div>
                        <div className='w-full h-[1px]' style={{ background: 'rgba(255, 255, 255, 0.10)' }} />
                        <div className='flex px-4 flex-col items-start gap-2 self-stretch'>
                          <div className='flex items-center gap-2 self-stretch'>
                            <span className='font-source text-[12px] font-normal'>
                              {translation('cameraPreview.contrastSettings')}
                            </span>
                          </div>
                          <div className='flex flex-col items-start gap-1 self-stretch'>
                            <div className='flex flex-col items-start gap-1 self-stretch'>
                              <div className='flex py-1 flex-col justify-center gap-2 self-stretch items-start'>
                                { _.get(algorithmParams, 'projector_pattern') === 'PhasePattern' &&
                                  <div className='flex h-[26px] py-1 items-center gap-2 self-stretch'>
                                    <Checkbox
                                      size='small'
                                      checked={_.get(algorithmParams, 'saturation', false)}
                                      onChange={(e) => {
                                        const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                        newAlgorithmParams.saturation = e.target.checked;
                                        setAlgorithmParams(newAlgorithmParams);
                                        // handleSaveSettings(newAlgorithmParams, 'algorithmParams');
                                      }}
                                      disabled={curContrast !== 'customized'}
                                    />
                                    <span className='font-source text-[12px] font-normal'>
                                      {translation('cameraPreview.removeOverexposeRegion')}
                                    </span>
                                  </div>
                                }
                                <div className='flex h-[26px] py-1 items-center gap-2 self-stretch'>
                                  <Checkbox
                                    size='small'
                                    checked={_.get(algorithmParams, 'enable_intensity', false)}
                                    onChange={(e) => {
                                      const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                      newAlgorithmParams.enable_intensity = e.target.checked;
                                      setAlgorithmParams(newAlgorithmParams);
                                      // handleSaveSettings(newAlgorithmParams, 'algorithmParams');
                                    }}
                                    disabled={curContrast !== 'customized'}
                                  />
                                  <span className='font-source text-[12px] font-normal'>
                                    {translation('cameraPreview.intensityThreshold')}
                                  </span>
                                </div>
                                <div className='flex h-[26px] items-center gap-2 self-stretch'>
                                  <div className='flex flex-col flex-1'>
                                    <Slider
                                      min={0}
                                      max={50}
                                      disabled={curContrast !== 'customized' || _.get(algorithmParams, 'enable_intensity') === false}
                                      value={_.get(algorithmParams, 'intensity')}
                                      onChange={(value) => {
                                        const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                        newAlgorithmParams.intensity = value;
                                        setAlgorithmParams(newAlgorithmParams);
                                      }}
                                      onChangeComplete={(value) => {
                                        const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                        newAlgorithmParams.intensity = value;
                                        setAlgorithmParams(newAlgorithmParams);
                                        // handleSaveSettings(newAlgorithmParams, 'algorithmParams');
                                      }}
                                    />
                                  </div>
                                  <div className='flex items-start gap-2 self-stretch'>
                                    <InputNumber
                                      style={{ width: '36px' }}
                                      size='small'
                                      controls={false}
                                      min={0}
                                      max={50}
                                      value={_.get(algorithmParams, 'intensity')}
                                      // onBlur={(e) => {
                                      //   const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                      //   newAlgorithmParams.intensity = Number(e.target.value);
                                      //   setAlgorithmParams(newAlgorithmParams);
                                      //   // handleSaveSettings(newAlgorithmParams, 'algorithmParams');
                                      // }}
                                      onChange={(v) => {
                                        const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                        newAlgorithmParams.intensity = Number(v);
                                        setAlgorithmParams(newAlgorithmParams);
                                      }}
                                      disabled={curContrast !== 'customized' || _.get(algorithmParams, 'enable_gaussian') === false}
                                    />
                                  </div>
                                </div>
                              </div>
                            </div>
                            <div className='flex flex-col items-start gap-1 self-stretch'>
                              <div className='flex py-1 flex-col justify-center gap-2 self-stretch items-start'>
                                <div className='flex h-[26px] py-1 items-center gap-2 self-stretch justify-between'>
                                  <div className='flex h-[26px] py-1 items-center gap-2 self-stretch'>
                                    <Checkbox
                                      size='small'
                                      checked={_.get(algorithmParams, 'enable_phase_quality', false)}
                                      onChange={(e) => {
                                        const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                        newAlgorithmParams.enable_phase_quality = e.target.checked;
                                        setAlgorithmParams(newAlgorithmParams);
                                        // handleSaveSettings(newAlgorithmParams, 'algorithmParams');
                                      }}
                                      disabled={curContrast !== 'customized'}
                                    />
                                    <span className='font-source text-[12px] font-normal'>
                                      {translation('cameraPreview.removeLowQualityRegion')}
                                    </span>
                                  </div>
                                  <div className='flex items-start gap-2 self-stretch'>
                                    <InputNumber
                                      style={{ width: '50px' }}
                                      size='small'
                                      controls={false}
                                      min={0}
                                      max={0.5}
                                      step={0.05}
                                      value={_.get(algorithmParams, 'phase_quality_threshold')}
                                      // onBlur={(e) => {
                                      //   const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                      //   newAlgorithmParams.phase_quality_threshold = Number(e.target.value);
                                      //   setAlgorithmParams(newAlgorithmParams);
                                      //   // handleSaveSettings(newAlgorithmParams, 'algorithmParams');
                                      // }}
                                      onChange={(v) => {
                                        const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                        newAlgorithmParams.phase_quality_threshold = Number(v);
                                        setAlgorithmParams(newAlgorithmParams);
                                      }}
                                      disabled={curContrast !== 'customized' || _.get(algorithmParams, 'enable_phase_quality') === false}
                                    />
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </Fragment>
                    }
                    {/* contrast ends */}
                    {/* correction starts */}
                    { selectedPointcloudFilterTab === 'correction' &&
                      <Fragment>
                        <div className='flex px-4 flex-col items-start gap-2 self-stretch'>
                          <div className='flex flex-col items-start gap-1 self-stretch'>
                            <div className='flex flex-col items-start gap-1 self-stretch'>
                              <div className='flex py-1 flex-col justify-center gap-2 self-stretch items-start'>
                                <div className='flex h-[26px] py-1 items-center gap-2 self-stretch'>
                                  <Checkbox
                                    size='small'
                                    checked={_.get(algorithmParams, 'enable_fill_hole', false)}
                                    onChange={(e) => {
                                      const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                      newAlgorithmParams.enable_fill_hole = e.target.checked;
                                      setAlgorithmParams(newAlgorithmParams);
                                      // handleSaveSettings(newAlgorithmParams, 'algorithmParams');
                                    }}
                                  />
                                  <span className='font-source text-[12px] font-normal'>
                                    {translation('cameraPreview.fillGaps')}
                                  </span>
                                </div>
                                { _.get(algorithmParams, 'enable_fill_hole') &&
                                  <div className='flex pl-8 items-center gap-2 self-stretch flex-col'>
                                    <div className='flex h-[26px] py-1 items-center gap-2 self-stretch'>
                                      <span className='font-source text-[12px] font-normal'>
                                        {translation('cameraPreview.holeSize')}
                                      </span>
                                    </div>
                                    <div className='flex h-[26px] items-center gap-2 self-stretch'>
                                      <div className='flex flex-col flex-1'>
                                        <Slider
                                          min={1}
                                          max={100}
                                          disabled={_.get(algorithmParams, 'enable_fill_hole') === false}
                                          value={_.get(algorithmParams, 'hole_size', 0)}
                                          onChange={(value) => {
                                            const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                            newAlgorithmParams.hole_size = value;
                                            setAlgorithmParams(newAlgorithmParams);
                                          }}
                                          onChangeComplete={(value) => {
                                            const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                            newAlgorithmParams.hole_size = value;
                                            setAlgorithmParams(newAlgorithmParams);
                                            // handleSaveSettings(newAlgorithmParams, 'algorithmParams');
                                          }}
                                        />
                                      </div>
                                      <div className='flex items-start gap-2 self-stretch'>
                                        <InputNumber
                                          style={{ width: '36px' }}
                                          size='small'
                                          controls={false}
                                          min={1}
                                          max={100}
                                          value={_.get(algorithmParams, 'hole_size', 0)}
                                          // onBlur={(e) => {
                                          //   const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                          //   newAlgorithmParams.hole_size = Number(e.target.value);
                                          //   setAlgorithmParams(newAlgorithmParams);
                                          //   // handleSaveSettings(newAlgorithmParams, 'algorithmParams');
                                          // }}
                                          onChange={(v) => {
                                            const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                            newAlgorithmParams.hole_size = Number(v);
                                            setAlgorithmParams(newAlgorithmParams);
                                          }}
                                          disabled={_.get(algorithmParams, 'enable_fill_hole') === false}
                                        />
                                      </div>
                                    </div>
                                    <div className='flex h-[26px] py-1 items-center gap-2 self-stretch'>
                                      <span className='font-source text-[12px] font-normal'>
                                        {translation('cameraPreview.depthDifference')}
                                      </span>
                                    </div>
                                    <div className='flex h-[26px] items-center gap-2 self-stretch'>
                                      <div className='flex flex-col flex-1'>
                                        <Slider
                                          min={1}
                                          max={100}
                                          disabled={_.get(algorithmParams, 'enable_fill_hole') === false}
                                          value={_.get(algorithmParams, 'depth_diff', 1)}
                                          onChange={(value) => {
                                            const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                            newAlgorithmParams.depth_diff = value;
                                            setAlgorithmParams(newAlgorithmParams);
                                          }}
                                          onChangeComplete={(value) => {
                                            const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                            newAlgorithmParams.depth_diff = value;
                                            setAlgorithmParams(newAlgorithmParams);
                                            // handleSaveSettings(newAlgorithmParams, 'algorithmParams');
                                          }}
                                        />
                                      </div>
                                      <div className='flex items-start gap-2 self-stretch'>
                                        <InputNumber
                                          style={{ width: '36px' }}
                                          size='small'
                                          controls={false}
                                          min={1}
                                          max={100}
                                          value={_.get(algorithmParams, 'depth_diff', 1)}
                                          // onBlur={(e) => {
                                          //   const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                          //   newAlgorithmParams.depth_diff = Number(e.target.value);
                                          //   setAlgorithmParams(newAlgorithmParams);
                                          //   // handleSaveSettings(newAlgorithmParams, 'algorithmParams');
                                          // }}
                                          onChange={(v) => {
                                            const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                            newAlgorithmParams.depth_diff = Number(v);
                                            setAlgorithmParams(newAlgorithmParams);
                                          }}
                                          disabled={_.get(algorithmParams, 'enable_fill_hole') === false}
                                        />
                                      </div>
                                    </div>
                                  </div>
                                }
                              </div>
                              <div className='flex py-1 flex-col justify-center gap-2 self-stretch items-start'>
                                <div className='flex h-[26px] py-1 items-center gap-2 self-stretch justify-between'>
                                  <div className='flex h-[26px] py-1 items-center gap-2 self-stretch'>
                                    <Checkbox
                                      size='small'
                                      checked={_.get(algorithmParams, 'enable_contrast_distortion', false)}
                                      onChange={(e) => {
                                        const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                        newAlgorithmParams.enable_contrast_distortion = e.target.checked;
                                        setAlgorithmParams(newAlgorithmParams);
                                        // handleSaveSettings(newAlgorithmParams, 'algorithmParams');
                                      }}
                                    />
                                    <span className='font-source text-[12px] font-normal'>
                                      {translation('cameraPreview.contrastDistortion')}
                                    </span>
                                  </div>
                                  <div className='flex flex-col flex-1 items-center h-[26px] justify-center'>
                                    <Select
                                      style={{ width: '100%' }}
                                      size='small'
                                      options={[
                                        {
                                          value: 'Correct',
                                          label: <span className='font-source text-[12px] font-normal'>
                                            {translation('cameraPreview.correct')}
                                          </span>,
                                        },
                                        {
                                          value: 'Remove',
                                          label: <span className='font-source text-[12px] font-normal'>
                                            {translation('cameraPreview.remove')}
                                          </span>
                                        },
                                      ]}
                                      value={_.get(algorithmParams, 'contrast_distortion_treatment', 'Correct')}
                                      onChange={(value) => {
                                        const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                        newAlgorithmParams.contrast_distortion_treatment = value;
                                        setAlgorithmParams(newAlgorithmParams);
                                        // handleSaveSettings(newAlgorithmParams, 'algorithmParams');
                                      }}
                                    />
                                  </div>
                                </div>
                                { _.get(algorithmParams, 'enable_contrast_distortion') &&
                                  <div className='flex pl-8 items-center gap-2 self-stretch flex-col'>
                                    <div className='flex h-[26px] py-1 items-center gap-2 self-stretch'>
                                      <span className='font-source text-[12px] font-normal'>
                                        {_.get(algorithmParams, 'contrast_distortion_treatment', 'Correct') === 'Correct' ?
                                          translation('cameraPreview.correctLevel') :
                                          translation('cameraPreview.removeLevel')
                                        }
                                      </span>
                                    </div>
                                    <div className='flex h-[26px] items-center gap-2 self-stretch'>
                                      <div className='flex flex-col flex-1'>
                                        <Slider
                                          min={0}
                                          max={0.15}
                                          step={0.01}
                                          disabled={_.get(algorithmParams, 'enable_contrast_distortion') === false}
                                          value={_.get(algorithmParams, 'contrast_distortion_strength')}
                                          onChange={(value) => {
                                            const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                            newAlgorithmParams.contrast_distortion_strength = value;
                                            setAlgorithmParams(newAlgorithmParams);
                                          }}
                                          onChangeComplete={(value) => {
                                            const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                            newAlgorithmParams.contrast_distortion_strength = value;
                                            setAlgorithmParams(newAlgorithmParams);
                                            // handleSaveSettings(newAlgorithmParams, 'algorithmParams');
                                          }}
                                        />
                                      </div>
                                      <div className='flex items-start gap-2 self-stretch'>
                                        <InputNumber
                                          style={{ width: '60px' }}
                                          size='small'
                                          controls={false}
                                          min={0}
                                          max={0.15}
                                          step={0.01}
                                          value={_.get(algorithmParams, 'contrast_distortion_strength')}
                                          onChange={(v) => {
                                            const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                            newAlgorithmParams.contrast_distortion_strength = Number(v);
                                            setAlgorithmParams(newAlgorithmParams);
                                          }}
                                          // onBlur={(e) => {
                                          //   const newAlgorithmParams = _.cloneDeep(algorithmParams);
                                          //   newAlgorithmParams.contrast_distortion_strength = Number(e.target.value);
                                          //   setAlgorithmParams(newAlgorithmParams);
                                          //   // handleSaveSettings(newAlgorithmParams, 'algorithmParams');
                                          // }}
                                          disabled={_.get(algorithmParams, 'enable_contrast_distortion') === false}
                                        />
                                      </div>
                                    </div>
                                  </div>
                                }
                              </div>
                            </div>
                          </div>
                        </div>
                      </Fragment>
                    }
                    {/* correction ends */}
                  </div>
                }
                {/* pointcloud filter ends */}
                {/* coordinate system starts */}
                { settingTabKey === '3' &&
                  <div className='flex py-4 flex-col content-center items-start gap-4 self-stretch'>
                    <div className='flex px-4 flex-col items-start gap-2 self-stretch'>
                      <div className='flex items-center gap-4 self-stretch'>
                        <div className='flex px-0.5 gap-1 flex-1 rounded-[2px] items-center'>
                          <span className='font-source text-[12px] font-normal whitespace-nowrap'>
                            {translation('cameraPreview.coordinateSystem')}
                          </span>
                          <img className='w-2.5 h-2.5' src='/img/icn/icn_questionCircle_white.svg' alt='question' />
                        </div>
                        <div className='flex py-1 px-2 flex-1 items-center'>
                          <Select
                            size='small'
                            options={[
                              {
                                value: 0,
                                label: <span className='font-source text-[12px] font-normal'>
                                  {translation('cameraPreview.defaultCoordinateSystem')}
                                </span>
                              },
                              {
                                value: 1,
                                label: <span className='font-source text-[12px] font-normal'>
                                  {translation('cameraPreview.customCoordinateSystem')}
                                </span>
                              },
                            ]}
                            value={(_.isEmpty(cameraTransform) && !isCoordSystemSelectingPoints) ? 0 : 1}
                            onChange={(value) => {
                              if (value === 0) {
                                setCameraTransform(null);
                                setDisplayCoordSystemPoints({});
                                handleSaveSettings(null, 'cameraTransform');
                                dispatch(setIsCoordSystemSelectingPoints(false));
                              } else {
                                dispatch(setIsCoordSystemSelectingPoints(true));
                              }
                            }}
                          />
                        </div>
                      </div>
                    </div>
                    { (!_.isEmpty(cameraTransform) || isCoordSystemSelectingPoints) &&
                      <Fragment>
                        <div className='w-full h-[1px]' style={{ background: 'rgba(255, 255, 255, 0.10)' }} />
                        <div className='flex px-4 flex-col items-start gap-2 self-stretch'>
                          <div className='flex flex-col items-start gap-2 self-stretch'>
                            {/* <div className='flex items-center gap-2 self-stretch'>
                              <Switch size='small' />
                              <span className='font-source text-[12px] font-normal'>
                                {translation('cameraPreview.customCoordinateSystemLower')}
                              </span>
                            </div> */}
                            <div className='flex flex-col items-start self-stretch'>
                              <span className='font-source text-[12px] font-normal self-stretch'>
                                {translation('cameraPreview.toDefineACoordSystem')}
                              </span>
                              <span className='font-source text-[12px] font-normal self-stretch'>
                                {translation('cameraPreview.firstPoint')}
                              </span>
                              <span className='font-source text-[12px] font-normal self-stretch'>
                                {translation('cameraPreview.secondPoint')}
                              </span>
                              <span className='font-source text-[12px] font-normal self-stretch'>
                                {translation('cameraPreview.thirdPoint')}
                              </span>
                            </div>
                            <div className='flex flex-col items-start gap-2 self-stretch'>
                              <div className='flex item-start gap-2 w-full'>
                                <div className='flex py-1 flex-col justify-center w-[40px] items-start'>
                                  <span className='font-source text-[12px] font-normal'>
                                    {translation('cameraPreview.pointOne')}
                                  </span>
                                </div>
                                <div className='flex py-1 flex-col justify-center flex-1 items-start'>
                                  { !_.get(displayCoordSystemPoints, '0') ?
                                    <Button
                                      style={{ width: '100%' }}
                                      onClick={() => {
                                        if (displayMode !== 'pointcloud') {
                                          if (layoutMode === 'grid') {
                                            setLayoutMode('detail');
                                            dispatch(setSelectedCameraView('0'));
                                          }
                                          setDisplayMode('pointcloud');
                                        }
                                        // work flow:
                                        // selecting point index changes
                                        // preview point cloud component add mousedown event
                                        // user click on point cloud and point cloud component will dispatch the intersection point
                                        // event listener will be removed
                                        // this component update the point
                                        // dispatch the point index to null for reset
                                        dispatch(setCurCoordSystemSelectingPointIndex('0'));
                                      }}
                                    >
                                      <span className='font-source text-[12px] font-normal text-AOI-blue'>
                                        {translation('cameraPreview.setPointOne')}
                                      </span>
                                    </Button> :
                                    <div className='flex items-center gap-1 self-stretch'>
                                      <Tooltip
                                        title={`${_.round(_.get(displayCoordSystemPoints, '0.x', 0), 2)},${_.round(_.get(displayCoordSystemPoints, '0.y', 0), 2)},${_.round(_.get(displayCoordSystemPoints, '0.z', 0), 2)}`}
                                      >
                                        <InputNumber
                                          style={{ width: '50%' }}
                                          controls={false}
                                          disabled
                                          value={`${_.round(_.get(displayCoordSystemPoints, '0.x', 0), 2)},${_.round(_.get(displayCoordSystemPoints, '0.y', 0), 2)},${_.round(_.get(displayCoordSystemPoints, '0.z', 0), 2)}`}
                                        />
                                      </Tooltip>
                                      <Button
                                        style={{ width: '50%' }}
                                        onClick={() => {
                                          setDisplayCoordSystemPoints({
                                            ...displayCoordSystemPoints,
                                            '0': null
                                          });
                                          if (threeDSceneViewer) threeDSceneViewer.clearCustomCoordSystemPointByIndex(0);
                                        }}
                                      >
                                        <span className='font-source text-[12px] font-normal text-AOI-blue'>
                                          {translation('common.reset')}
                                        </span>
                                      </Button>
                                    </div>
                                  }
                                </div>
                              </div>
                              <div className='flex item-start gap-2 w-full'>
                                <div className='flex py-1 flex-col justify-center w-[40px] items-start'>
                                  <span className='font-source text-[12px] font-normal'>
                                    {translation('cameraPreview.pointTwo')}
                                  </span>
                                </div>
                                <div className='flex py-1 flex-col justify-center flex-1 items-start'>
                                  { !_.get(displayCoordSystemPoints, '1') ?
                                    <Button
                                      style={{ width: '100%' }}
                                      onClick={() => {
                                        if (displayMode !== 'pointcloud') {
                                          if (layoutMode === 'grid') {
                                            setLayoutMode('detail');
                                            dispatch(setSelectedCameraView('0'));
                                          }
                                          setDisplayMode('pointcloud');
                                        }
                                        dispatch(setCurCoordSystemSelectingPointIndex('1'));
                                      }}
                                    >
                                      <span className='font-source text-[12px] font-normal text-AOI-blue'>
                                        {translation('cameraPreview.setPointTwo')}
                                      </span>
                                    </Button> :
                                    <div className='flex items-center gap-1 self-stretch'>
                                      <Tooltip
                                        title={`${_.round(_.get(displayCoordSystemPoints, '1.x', 0), 2)},${_.round(_.get(displayCoordSystemPoints, '1.y', 0), 2)},${_.round(_.get(displayCoordSystemPoints, '1.z', 0), 2)}`}
                                      >
                                        <InputNumber
                                          style={{ width: '50%' }}
                                          controls={false}
                                          disabled
                                          value={`${_.round(_.get(displayCoordSystemPoints, '1.x', 0), 2)},${_.round(_.get(displayCoordSystemPoints, '1.y', 0), 2)},${_.round(_.get(displayCoordSystemPoints, '1.z', 0), 2)}`}
                                        />
                                      </Tooltip>
                                      <Button
                                        style={{ width: '50%' }}
                                        onClick={() => {
                                          setDisplayCoordSystemPoints({
                                            ...displayCoordSystemPoints,
                                            '1': null
                                          });
                                          if (threeDSceneViewer) threeDSceneViewer.clearCustomCoordSystemPointByIndex(1);
                                        }}
                                      >
                                        <span className='font-source text-[12px] font-normal text-AOI-blue'>
                                          {translation('common.reset')}
                                        </span>
                                      </Button>
                                    </div>
                                  }
                                </div>
                              </div>
                              <div className='flex item-start gap-2 w-full'>
                                <div className='flex py-1 flex-col justify-center w-[40px] items-start'>
                                  <span className='font-source text-[12px] font-normal'>
                                    {translation('cameraPreview.pointThree')}
                                  </span>
                                </div>
                                <div className='flex py-1 flex-col justify-center flex-1 items-start'>
                                  { !_.get(displayCoordSystemPoints, '2') ?
                                    <Button
                                      style={{ width: '100%' }}
                                      onClick={() => {
                                        if (displayMode !== 'pointcloud') {
                                          if (layoutMode === 'grid') {
                                            setLayoutMode('detail');
                                            dispatch(setSelectedCameraView('0'));
                                          }
                                          setDisplayMode('pointcloud');
                                        }
                                        dispatch(setCurCoordSystemSelectingPointIndex('2'));
                                      }}
                                    >
                                      <span className='font-source text-[12px] font-normal text-AOI-blue'>
                                        {translation('cameraPreview.setPointThree')}
                                      </span>
                                    </Button> :
                                    <div className='flex items-center gap-1 self-stretch'>
                                      <Tooltip
                                        title={`${_.round(_.get(displayCoordSystemPoints, '2.x', 0), 2)},${_.round(_.get(displayCoordSystemPoints, '2.y', 0), 2)},${_.round(_.get(displayCoordSystemPoints, '2.z', 0), 2)}`}
                                      >
                                        <InputNumber
                                          style={{ width: '50%' }}
                                          controls={false}
                                          disabled
                                          value={`${_.round(_.get(displayCoordSystemPoints, '2.x', 0), 2)},${_.round(_.get(displayCoordSystemPoints, '2.y', 0), 2)},${_.round(_.get(displayCoordSystemPoints, '2.z', 0), 2)}`}
                                        />
                                      </Tooltip>
                                      <Button
                                        style={{ width: '50%' }}
                                        onClick={() => {
                                          setDisplayCoordSystemPoints({
                                            ...displayCoordSystemPoints,
                                            '2': null
                                          });
                                          if (threeDSceneViewer) threeDSceneViewer.clearCustomCoordSystemPointByIndex(2);
                                        }}
                                      >
                                        <span className='font-source text-[12px] font-normal text-AOI-blue'>
                                          {translation('common.reset')}
                                        </span>
                                      </Button>
                                    </div>
                                  }
                                </div>
                              </div>
                            </div>
                            <div className='flex py-4 items-start gap-2 self-stretch'>
                              <Button
                                style={{ width: '50%' }}
                                onClick={() => {
                                  dispatch(setCurCoordSystemSelectingPointIndex(null));
                                  dispatch(setCurCoordSystemSelectedPointPosition(null));
                                  setDisplayCoordSystemPoints({});
                                  if (threeDSceneViewer) threeDSceneViewer.clearCustomCoordSystemPoints();
                                  // handleUpdateCoordSystem({
                                  //   "translation": {
                                  //     "x": 0,
                                  //     "y": 0,
                                  //     "z": 0
                                  //   },
                                  //   "rotation": {
                                  //     "basis_0": {
                                  //       "x": 1,
                                  //       "y": 0,
                                  //       "z": 0
                                  //     },
                                  //     "basis_1": {
                                  //       "x": 0,
                                  //       "y": 1,
                                  //       "z": 0
                                  //     },
                                  //     "basis_2": {
                                  //       "x": 0,
                                  //       "y": 0,
                                  //       "z": 1
                                  //     }
                                  //   }
                                  // }, true);
                                  handleUpdateCoordSystem(null, true);
                                }}
                              >
                                <span className='font-source text-[12px] font-normal text-AOI-blue'>
                                  {translation('common.resetAll')}
                                </span>
                              </Button>
                              <Button
                                style={{ width: '50%' }}
                                onClick={() => {
                                  // check if all three points are selected
                                  if (_.get(displayCoordSystemPoints, '0') && _.get(displayCoordSystemPoints, '1') && _.get(displayCoordSystemPoints, '2')) {
                                    const newCameraTransform = getTranslationAndRotationFromSelectedPoints({
                                      p0: _.get(displayCoordSystemPoints, '0'),
                                      p1: _.get(displayCoordSystemPoints, '1'),
                                      p2: _.get(displayCoordSystemPoints, '2'),
                                    });
                                    // handleSaveSettings(newCameraTransform, 'cameraTransform');
                                    if (threeDSceneViewer) threeDSceneViewer.clearCustomCoordSystemPoints();
                                    handleUpdateCoordSystem(newCameraTransform, false);
                                    return;
                                  }
                                  aoiAlert(translation('notification.error.pleaseSelectAllPointsForCoordinateSystem'), ALERT_TYPES.COMMON_ERROR);
                                  return;
                                }}
                              >
                                <span className='font-source text-[12px] font-normal text-AOI-blue'>
                                  {translation('common.apply')}
                                </span>
                              </Button>
                            </div>
                          </div>
                        </div>
                      </Fragment>
                    }
                  </div>
                }
                {/* coordinate system ends */}
                {/* 2d settings starts(not the 2d in 3d camera) */}
                { settingTabKey === '4' &&
                  <div className='flex px-4 flex-col items-start gap-2 self-stretch'>
                    {/* <div className='flex items-center gap-2 self-stretch'>
                      <span className='font-source text-[12px] font-normal'>
                        {translation('cameraPreview.outlierRemoval')}
                      </span>
                    </div> */}
                    <div className='flex flex-col items-start gap-1 self-stretch'>
                      <div className='flex flex-col items-start gap-1 self-stretch'>
                        <div className='flex py-1 flex-col justify-center gap-2 self-stretch items-start'>
                          <div className='flex h-[26px] py-1 items-center gap-2 self-stretch'>
                            <Checkbox
                              size='small'
                              checked={_.get(d2CaptureSettings, 'exposure_mode') !== 'Disabled'}
                              onChange={() => {
                                let newD2CaptureSettings = _.cloneDeep(d2CaptureSettings);
                                _.get(d2CaptureSettings, 'exposure_mode') === 'Disabled' ?
                                  newD2CaptureSettings.exposure_mode = 'Auto' :
                                  newD2CaptureSettings.exposure_mode = 'Disabled';
                                setD2CaptureSettings(newD2CaptureSettings);
                                // handleSaveSettings(newD2CaptureSettings, 'd2CaptureSettings');
                              }}
                            />
                            <span className='font-source text-[12px] font-normal'>
                              {translation('cameraPreview.twoDCapture')}
                            </span>
                          </div>
                          <div className='flex flex-col items-start gap-1 self-stretch'>
                            <span className='font-source text-[12px] font-normal'>
                              {translation('cameraPreview.exposureMode')}
                            </span>
                            <Radio.Group
                              disabled={_.get(d2CaptureSettings, 'exposure_mode') === 'Disabled'}
                              value={_.get(d2CaptureSettings, 'exposure_mode')}
                              onChange={(e) => {
                                let newD2CaptureSettings = _.cloneDeep(d2CaptureSettings);
                                newD2CaptureSettings.exposure_mode = e.target.value;
                                setD2CaptureSettings(newD2CaptureSettings);
                                // handleSaveSettings(newD2CaptureSettings, 'd2CaptureSettings');
                              }}
                            >
                              <div className="flex flex-col items-start self-stretch">
                                <div className="flex flex-col items-start gap-2 self-stretch">
                                  <div className="flex pl-2 items-center gap-2 self-stretch">
                                    <Radio value={'Timed'}>
                                      <div className="flex items-center gap-4 flex-1">
                                        <span className="text-xs font-semibold w-9">
                                          {translation('cameraPreview.timed')}
                                        </span>
                                        <div className="flex flex-col items-start gap-1 flex-1">
                                          <div className="flex items-center gap-2 self-stretch">
                                            <div className="flex py-1 justify-end items-center flex-1 self-stretch">
                                              <span className="text-xs whitespace-nowrap">
                                                {translation('cameraPreview.exposureTime')}
                                              </span>
                                            </div>
                                            <div className="flex items-start gap-0.5 flex-1 self-stretch">
                                              <InputNumber
                                                controls={false}
                                                disabled={
                                                  _.get(
                                                    d2CaptureSettings,
                                                    'exposure_mode'
                                                  ) !== 'Timed'
                                                }
                                                size="small"
                                                value={_.get(
                                                  d2CaptureSettings,
                                                  'exposure_time'
                                                )}
                                                onChange={(value) => {
                                                  let newD2CaptureSettings = _.cloneDeep(d2CaptureSettings);
                                                  newD2CaptureSettings.exposure_time = value;
                                                  setD2CaptureSettings(newD2CaptureSettings);
                                                  // handleSaveSettings(newD2CaptureSettings, 'd2CaptureSettings');
                                                }}
                                                min={10}
                                                max={450}
                                              />
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                                    </Radio>
                                  </div>
                                  {/* <div className="flex pl-2 items-center gap-2 self-stretch">
                                    <Radio value={'Flash'}>
                                      <div className="flex items-center gap-4 flex-1">
                                        <span className="text-xs font-semibold text-gray-2 w-9">
                                          {translation('cameraPreview.flash')}
                                        </span>
                                        <div className="flex flex-col items-start gap-1 flex-1">
                                          <div className="flex items-center gap-2 self-stretch">
                                            <div className="flex py-1 justify-end items-center flex-1 self-stretch">
                                              <span className="text-gray-2 text-xs  whitespace-nowrap">
                                                {translation('cameraPreview.exposureTime')}
                                              </span>
                                            </div>
                                            <div className="flex items-start gap-0.5 flex-1 self-stretch">
                                              <InputNumber
                                                size="small"
                                                value={_.get(
                                                  d2CaptureSettings,
                                                  'exposure_time'
                                                )}
                                                onChange={(value) => {
                                                  let newD2CaptureSettings = _.cloneDeep(d2CaptureSettings);
                                                  newD2CaptureSettings.exposure_time = value;
                                                  setD2CaptureSettings(newD2CaptureSettings);
                                                  handleSaveSettings(newD2CaptureSettings, 'd2CaptureSettings');
                                                }}
                                                min={10}
                                                max={450}
                                                disabled={
                                                  _.get(
                                                    d2CaptureSettings,
                                                    'exposure_mode'
                                                  ) !== 'Flash'
                                                }
                                              />
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                                    </Radio>
                                  </div> */}
                                </div>
                              </div>
                            </Radio.Group>
                          </div>
                          <div className='flex flex-col items-start gap-1 self-stretch'>
                            <span className='font-source text-[12px] font-normal'>
                              {translation('cameraPreview.triggerMode')}
                            </span>
                            <Select
                              style={{ width: '120px' }}
                              size='small'
                              value={_.get(d2CaptureSettings, 'trigger_mode')}
                              onChange={(value) => {
                                let newD2CaptureSettings = _.cloneDeep(d2CaptureSettings);
                                newD2CaptureSettings.trigger_mode = value;
                                setD2CaptureSettings(newD2CaptureSettings);
                                // handleSaveSettings(newD2CaptureSettings, 'd2CaptureSettings');
                              }}
                              options={[
                                {
                                  label: <span className='font-source text-[12px] font-normal'>
                                    {translation('cameraPreview.software')}
                                  </span>,
                                  value: 'software',
                                },
                                {
                                  label: <span className='font-source text-[12px] font-normal'>
                                    {translation('cameraPreview.hardware')}
                                  </span>,
                                  value: 'hardware',
                                },
                              ]}
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                }
                {/* 2d settings ends */}
                {/* color balance for 2d camera starts */}
                {settingTabKey === '5' &&
                  <div className='flex px-4 flex-col items-start gap-2 self-stretch'>
                    <div className='flex gap-2 self-stretch justify-center items-center py-1'>
                      <div className="flex items-center gap-2">
                        <div className="flex items-center gap-2">
                          <span className="text-xs text-gray-2">{translation('cameraPreview.red')}</span>
                          <InputNumber
                            value={_.get(d2CaptureSettings, 'color_balance_red', 0.33)}
                            disabled
                            size='small'
                            style={{ width: '54px' }}
                          />
                        </div>

                        <div className="flex items-center gap-2">
                          <span className="text-xs text-gray-2">{translation('cameraPreview.green')}</span>
                          <InputNumber
                            value={_.get(d2CaptureSettings, 'color_balance_green', 0.33)}
                            disabled
                            size='small'
                            style={{ width: '54px' }}
                          />
                        </div>

                        <div className="flex items-center gap-2">
                          <span className="text-xs text-gray-2">{translation('cameraPreview.blue')}</span>
                          <InputNumber
                            value={_.get(d2CaptureSettings, 'color_balance_blue', 0.33)}
                            disabled
                            size='small'
                            style={{ width: '54px' }} 
                          />
                        </div>
                      </div>
                    </div>

                    <ConfigProvider
                      theme={{
                        components: {
                          Slider: {
                            trackBg: '',
                            trackHoverBg: '',
                            dotSize: 4,
                          },
                        },
                      }}
                    >

                      <div className="flex flex-col items-start gap-1 self-stretch">
                        <div className="flex items-center gap-4 self-stretch">
                          <div className="flex w-8 items-start gap-2">
                            <div className="flex items-start gap-0.5">
                              <div className="flex py-1 items-center self-stretch">
                                <span className={`text-xs text-gray-2 w-8`}>
                                  {translation('cameraPreview.red')}
                                </span>
                              </div>
                            </div>
                          </div>

                          <div className="w-full ">
                            <Slider
                              min={0}
                              max={1}
                              step={0.0001}
                              value={_.get(d2CaptureSettings, 'color_balance_red', 0.33) || 0.33}
                              onChange={(value) => handleColorChange('red', value)}
                              styles={{
                                rail: {
                                  background:
                                    'linear-gradient(270deg, #F00 45.31%, rgba(255, 255, 255, 0) 100%), #000',
                                },
                              }}
                              onChangeComplete={(value) => {
                                handleColorChange('red', value);
                                // const { red, green, blue } = handleColorChange('red', value);
                                // handleSaveSettings({
                                //   ...d2CaptureSettings,
                                //   color_balance_red: red,
                                //   color_balance_green: green,
                                //   color_balance_blue: blue,
                                // }, 'd2CaptureSettings');
                              }}
                            />
                          </div>
                        </div>
                      </div>

                      <div className="flex flex-col items-start gap-4 self-stretch">
                        <div className="flex items-center gap-4 self-stretch">
                          <div className="flex w-8 items-start gap-2">
                            <div className="flex items-start gap-0.5">
                              <div className="flex py-1 items-center self-stretch">
                                <span className={`text-xs text-gray-2 w-8`}>
                                  {translation('cameraPreview.green')}
                                </span>
                              </div>
                            </div>
                          </div>

                          <div className="w-full ">
                            <Slider
                              min={0}
                              max={1}
                              step={0.01}
                              value={_.get(
                                d2CaptureSettings,
                                'color_balance_green',
                                0.33
                              )}
                              onChange={(value) => handleColorChange('green', value) || 0.33}
                              styles={{
                                rail: {
                                  background:
                                    'linear-gradient(270deg, #00CF2E 45.31%, rgba(255, 255, 255, 0.00) 100%), #000',
                                },
                              }}
                              onChangeComplete={(value) => {
                                handleColorChange('green', value);
                                // const { red, green, blue } = handleColorChange('green', value);
                                // handleSaveSettings({
                                //   ...d2CaptureSettings,
                                //   color_balance_red: red,
                                //   color_balance_green: green,
                                //   color_balance_blue: blue,
                                // }, 'd2CaptureSettings');
                              }}
                            />
                          </div>
                        </div>
                      </div>

                      <div className="flex flex-col items-start gap-4 self-stretch">
                        <div className="flex items-center gap-4 self-stretch">
                          <div className="flex w-8 items-start gap-2">
                            <div className="flex items-start gap-0.5">
                              <div className="flex py-1 items-center self-stretch">
                                <span className={`text-xs text-gray-2 w-8`}>
                                  {translation('cameraPreview.blue')}
                                </span>
                              </div>
                            </div>
                          </div>

                          <div className="w-full ">
                            <Slider
                              min={0}
                              max={1}
                              step={0.01}
                              value={_.get(d2CaptureSettings, 'color_balance_blue', 0.33)}
                              onChange={(value) => handleColorChange('blue', value)}
                              styles={{
                                rail: {
                                  background:
                                    'linear-gradient(270deg, #006CCF 45.31%, rgba(255, 255, 255, 0.00) 100%), #000',
                                },
                              }}
                              onChangeComplete={(value) => {
                                handleColorChange('blue', value);
                                // const { red, green, blue } = handleColorChange('blue', value);
                                // handleSaveSettings({
                                //   ...d2CaptureSettings,
                                //   color_balance_red: red,
                                //   color_balance_green: green,
                                //   color_balance_blue: blue,
                                // }, 'd2CaptureSettings');
                              }}
                            />
                          </div>
                        </div>
                      </div>

                    </ConfigProvider>
                  </div>
                }
                {/* color balance for 2d camera ends */}
                { settingTabKey !== '3' &&
                  <div className='flex py-4 items-start gap-2 self-stretch px-4'>
                    <Button
                      style={{ width: '100%'}}
                      onClick={() => {
                        handleSaveSettings(null, 'all');
                      }}
                    >
                      <span className='font-source text-[12px] font-semibold'>
                        {translation('common.save')}
                      </span>
                    </Button>
                  </div>
                }
              </div>
            }
          </div>
        </div>
        }
      </div>
    </Fragment>
  );
};

export default ProductCameraSettingFragment;