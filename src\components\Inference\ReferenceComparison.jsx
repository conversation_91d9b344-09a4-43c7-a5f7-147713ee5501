import _ from 'lodash';
import React, { Fragment, useEffect, useRef, useState } from 'react';
import { backendAutoGenTimeToDisplayString, backendTimestampToDisplayString, convertFeedbackCorrectnessToChoice, convertGrayScaleArrayBufferToGreenToRedImage, getFeatureTypeDisplayText, handleRequestFailed, mapRange, translation } from '../../common/util';
import { defectDetection, feedbackMaskRequiredAgent, heightDiff, paixianToolAgent, serverEndpoint, threeDLineItems } from '../../common/const';
import CloseOutlined from '@ant-design/icons/CloseOutlined';
import { Button, Checkbox, ConfigProvider, Radio, Select, Tabs, Tooltip } from 'antd';
import HeightDiffComparison from '../Scene/HeightDiffComparison';
// import CustomTabs from '../../common/styledComponent/customTabs';
import HeightDiffStacked from '../Scene/HeightDiffStacked';
import styled from 'styled-components';
import { useAnnotateFeatureMutation, useCancelAnnotationMutation } from '../../services/session';
import { ALERT_TYPES, aoiAlert } from '../../common/alert';
import { useLazyLineItemResultsQuery } from '../../services/product';
import SubmitNGFeedbackScoreLowerThanMaxOk from '../modal/SubmitNGFeedbackScoreLowerThanMaxOk';
import ReferenceImageViewer from '../common/viewer/ReferenceImageViewer';


const ReferenceComparison = (props) => {
  const {
    goldenProduct,
    selectedFeatureId,
    curSessionStepInfo,
    goldenProductId,
    ipcProductId,
    // ipcProductIdRef,
    // sessionInfo,
    inspectionInfo,
    step,
    handleReDirect,
    selectedLineItem,
    curDisplayOptionsBrightness,
    curDisplayOptionsContrast,
    curDisplayOptionsSaturation,
    // isSharpnessEnabled,
    goldenProdFeatures,
    setSelectedLineItem,
    setSelectedFeatureId,
    fetchFeatures,
    setIsDrawMaskEnabled,
    drawMaskViewerRef,
    isDrawMaskEnabled,
  } = props;
  
  const ngConfirmCallbackRef = useRef(null);

  const goldenProductCanvasRef = useRef(null);
  const inferenceProductCanvasRef = useRef(null);

  // const [getAllFeatures] = useLazyGetAllFeaturesByProductIdAndStepQuery();
  // const [getFrame] = useLazyGetFrameByProductIdAndStepQuery();

  const [annotateFeature] = useAnnotateFeatureMutation();
  const [cancelAnnotation] = useCancelAnnotationMutation();
  const [getLineItemResults] = useLazyLineItemResultsQuery();

  const [goldenProductCroppedSrc, setGoldenProductCroppedSrc] = useState('');
  const [inferenceProductCroppedSrc, setInferenceProductCroppedSrc] = useState('');
  const [selectedFeature, setSelectedFeature] = useState(null);
  const [heightDiffIpcCloudUri, setHeightDiffIpcCloudUri] = useState('');
  const [tabItems, setTabItems] = useState([]);
  const [activeTabKey, setActiveTabKey] = useState('0');
  const [defectMapSrc, setDefectMapSrc] = useState('');
  const [showDlOutputMask, setShowDlOutputMask] = useState(false);
  const [activeHeightDiffComparisonView, setActiveHeightDiffComparisonView] = useState('separate'); // separate, stacked
  const [selectedDetail, setSelectedDetail] = useState(null);
  const [isIpcCloudVisible, setIsIpcCloudVisible] = useState(true);
  const [isGoldenCloudVisible, setIsGoldenCloudVisible] = useState(true);
  const [pointCloudDisplayedView, setPointCloudDisplayedView] = useState('top');
  const [selectedParsedErrorDetail, setSelectedParsedErrorDetail] = useState(null);
  const [isNGFeedbackConfirmationModalOpened, setIsNGFeedbackConfirmationModalOpened] = useState(false);
  const [ngConfirmPayload, setNgConfirmPayload] = useState(null);

  const handleCancelFeedback = async ({
    selectedDetail,
    selectedFeatureId,
    curSessionStepInfo,
    ipcProductId,
    selectedFeature,
    step,
    setIsDrawMaskEnabled,
  }) => {
    if (_.isEmpty(selectedFeature)) return;
    const selectedStepInfo = _.find(curSessionStepInfo, (item) => item.feature_id === selectedFeatureId && item.detail === selectedDetail);
    if (_.isEmpty(_.get(selectedStepInfo, 'feedback'))) return;
    const res = await cancelAnnotation({
      variant: _.get(selectedFeature, 'variant'),
      product_id: Number(ipcProductId),
      step: Number(step),
      feature_id: Number(selectedFeatureId),
      line_item_name: _.get(selectedStepInfo, 'detail'),
    });
    if (_.get(res, 'error')) {
      handleRequestFailed('cancelAnnotation', _.get(res, 'error'));
      return;
    }
    fetchFeatures();
    aoiAlert(translation('notification.success.cancelAnnotation'), ALERT_TYPES.COMMON_SUCCESS);
  };

  const handleFeedbackGoodClick = async ({
    selectedDetail,
    selectedFeatureId,
    curSessionStepInfo,
    ipcProductId,
    selectedFeature,
    step,
    setIsDrawMaskEnabled,
  }) => {
    if (_.isEmpty(selectedFeature)) return;
    const selectedStepInfo = _.find(curSessionStepInfo, (item) => item.feature_id === selectedFeatureId && item.detail === selectedDetail);

    if ((_.get(selectedStepInfo, 'feedback.correct') === true && _.get(selectedStepInfo, 'pass')) || (_.get(selectedStepInfo, 'feedback.correct') === false && !_.get(selectedStepInfo, 'pass'))) {
      aoiAlert(translation('notification.warning.correctFeedbackProvided'), ALERT_TYPES.COMMON_WARNING);
      return;
    }
    setIsDrawMaskEnabled(false);
    const res = await annotateFeature({
      variant: _.get(selectedFeature, 'variant'),
      product_id: Number(ipcProductId),
      step: Number(step),
      feature_id: Number(selectedFeatureId),
      line_item_name: _.get(selectedStepInfo, 'detail'),
      is_inference_correct: _.get(selectedStepInfo, 'pass') === true,
    });
    if (_.get(res, 'error')) {
      handleRequestFailed('annotateFeature', _.get(res, 'error'));
      return;
    }
    aoiAlert(translation('notification.success.inferenceAnnotation'), ALERT_TYPES.COMMON_SUCCESS);
    fetchFeatures();
  };

  const handleFeedbackNotGoodClick = async ({
    curSessionStepInfo,
    selectedFeatureId,
    ipcProductId,
    selectedFeature,
    step,
    selectedDetail,
    setIsDrawMaskEnabled,
    goldenProductId,
  }) => {
    if (_.isEmpty(selectedFeature)) return;
    const selectedStepInfo = _.find(curSessionStepInfo, (item) => item.feature_id === selectedFeatureId && item.detail === selectedDetail);
    if ((_.get(selectedStepInfo, 'feedback.correct') === false && _.get(selectedStepInfo, 'pass')) || (_.get(selectedStepInfo, 'feedback.correct') === true && !_.get(selectedStepInfo, 'pass'))) {
      aoiAlert(translation('notification.warning.defectFeedbackProvided'), ALERT_TYPES.COMMON_WARNING);
      return;
    }
    // if (_.includes(feedbackMaskRequiredAgent, _.get(selectedStepInfo, 'detail'))) {
    //   aoiAlert(translation('notification.info.maskReqiredForFeedback'), ALERT_TYPES.COMMON_INFO);
    //   setIsDrawMaskEnabled(true);
    //   return;
    // }

    // check the max score amoung this feature's all training training example which has good feedback
    const goodFeedback = await getLineItemResults({
      golden_product_id: Number(goldenProductId),
      feedback: true,
      feature_id: Number(selectedFeatureId),
      has_feedback: true,
    });

    let goodFeedbackMaxScore = 0;

    // console.log('goodFeedback', goodFeedback);
    // console.log('selectedStepInfo', selectedStepInfo);

    if (!_.isEmpty(_.get(goodFeedback, 'data.line_item_results', {}))) {
      for (const key of _.keys(_.get(goodFeedback, 'data.line_item_results', []))) {
        const twoDResult = _.find(_.get(goodFeedback, `data.line_item_results.${key}`, {}), r => r.detail === defectDetection);
        if (!twoDResult) continue;
        goodFeedbackMaxScore = _.max([goodFeedbackMaxScore, twoDResult.confidence]);
      }
    }

    // console.log('goodFeedbackMaxScore', goodFeedbackMaxScore);
    // console.log('selectedStepInfo score', _.get(selectedStepInfo, 'confidence', -1));

    // check if this record's confidence is lower than the max score
    if (_.get(selectedStepInfo, 'confidence', -1) !== -1 && goodFeedbackMaxScore > 0 && _.get(selectedStepInfo, 'confidence') < goodFeedbackMaxScore) {
      setNgConfirmPayload({
        variant: _.get(selectedFeature, 'variant'),
        product_id: Number(ipcProductId),
        step: Number(step),
        feature_id: Number(selectedFeatureId),
        line_item_name: _.get(selectedStepInfo, 'detail'),
        is_inference_correct: _.get(selectedStepInfo, 'pass') === false,
      });
      ngConfirmCallbackRef.current = () => {
        fetchFeatures();
      };
      setIsNGFeedbackConfirmationModalOpened(true);
      return;
    }

    const res = await annotateFeature({
      variant: _.get(selectedFeature, 'variant'),
      product_id: Number(ipcProductId),
      step: Number(step),
      feature_id: Number(selectedFeatureId),
      line_item_name: _.get(selectedStepInfo, 'detail'),
      is_inference_correct: _.get(selectedStepInfo, 'pass') === false,
    });
    if (_.get(res, 'error')) {
      handleRequestFailed('annotateFeature', _.get(res, 'error'));
      return;
    }
    fetchFeatures();
    aoiAlert(translation('notification.success.inferenceAnnotation'), ALERT_TYPES.COMMON_SUCCESS);
  };

  const handleSaveMaskAndFeedback = async ({
    curSessionStepInfo,
    selectedFeatureId,
    ipcProductId,
    selectedFeature,
    step,
    drawMaskViewerRef,
  }) => {
    if (_.isEmpty(selectedFeature)) return;
    const selectedStepInfo = _.find(curSessionStepInfo, (item) => item.feature_id === selectedFeatureId && item.detail === selectedDetail);
    if (drawMaskViewerRef.current) {
      const drawnMaskBase64 = drawMaskViewerRef.current.outputDrawnMaskAsBase64();
      if (_.isEmpty(drawnMaskBase64)) {
        aoiAlert(translation('notification.warning.emptyMask'), ALERT_TYPES.COMMON_WARNING);
        return;
      }
      // console.log('drawnMaskBase64', drawnMaskBase64);
      // console.log('height', _.get(selectedFeature, 'roi.points.1.y') - _.get(selectedFeature, 'roi.points.0.y'),);
      // console.log('width', _.get(selectedFeature, 'roi.points.1.x') - _.get(selectedFeature, 'roi.points.0.x'),);
  
      const res = await annotateFeature({
        variant: _.get(selectedFeature, 'variant'),
        product_id: Number(ipcProductId),
        step: Number(step),
        feature_id: Number(selectedFeatureId),
        is_inference_correct: _.get(selectedStepInfo, 'pass') === false,
        line_item_name: _.get(selectedStepInfo, 'detail'),
        mask_data: drawnMaskBase64,
        mask_height: _.get(selectedFeature, 'roi.points.1.y') - _.get(selectedFeature, 'roi.points.0.y'),
        mask_width: _.get(selectedFeature, 'roi.points.1.x') - _.get(selectedFeature, 'roi.points.0.x'),
      });
      if (_.get(res, 'error')) {
        handleRequestFailed('annotateFeature', _.get(res, 'error'));
        return;
      }
      aoiAlert(translation('notification.success.inferenceAnnotation'), ALERT_TYPES.COMMON_SUCCESS);
    }
    setIsDrawMaskEnabled(false);
    fetchFeatures();
  };

  useEffect(() => {
    if (!_.isInteger(selectedFeatureId) || _.isEmpty(step)) return;
    // fetch golden product and inference's frames
    // then crop them based on the feature's roi
    const fetchAndCrop = async (goldenProdFeatures, selectedFeatureId, curSessionStepInfo) => {
      // console.log('fetchAndCrop', goldenProdFeatures, selectedFeatureId);
      const curFeature = _.find(goldenProdFeatures, (item) => item.feature_id === selectedFeatureId);
      const selectedIpcFeatureInfo = _.find(curSessionStepInfo, (item) => item.feature_id === selectedFeatureId);
      const defectDetectionInfo = _.find(curSessionStepInfo, (item) => item.feature_id === selectedFeatureId && item.detail === defectDetection);
      if (_.isUndefined(curFeature) || _.isEmpty(_.get(curFeature, 'cropped_color_map_uri'))) return;
      if (_.isEmpty(selectedIpcFeatureInfo) || _.isEmpty(_.get(selectedIpcFeatureInfo, 'cropped_color_map_uri'))) return;
      setSelectedFeature(curFeature);

      // golden product
      // we download the image from feature list's uri directly from now
      const goldenProdCroppedImageUri = _.get(curFeature, 'cropped_color_map_uri');
      let res;
      try {
        res = await fetch(`${serverEndpoint}/data?data_uri=${goldenProdCroppedImageUri}`);
      } catch (error) {
        handleRequestFailed('fetchGoldenCropped', error);
        return;
      }
      const blob = await res.blob();
      const reader = new FileReader();
      reader.readAsDataURL(blob);
      const goldenDataUrl = await new Promise((resolve) => {
        reader.onload = (event) => {
          resolve(event.target.result);
        };
      });
      setGoldenProductCroppedSrc(goldenDataUrl);

      // inference
      // backend store the inspection cropped image now so also download it directly
      // no need to crop it
      try {
        res = await fetch(`${serverEndpoint}/data?data_uri=${_.get(selectedIpcFeatureInfo, 'cropped_color_map_uri')}`);
      } catch (e) {
        handleRequestFailed('fetchIpcCropped', e);
        return;
      }
      const blob2 = await res.blob();
      const reader2 = new FileReader();
      reader2.readAsDataURL(blob2);
      const inferenceDataUrl = await new Promise((resolve) => {
        reader2.onload = (event) => {
          resolve(event.target.result);
        };
      });
      setInferenceProductCroppedSrc(inferenceDataUrl);

      // dl output mask
      if (!_.isEmpty(defectDetectionInfo) && _.get(defectDetectionInfo, 'error')) {
        let errorJson;
        try {
          errorJson = JSON.parse(_.get(defectDetectionInfo, 'error'));
        } catch (e) {
          console.error('errorJson parse error', e);
          setDefectMapSrc('');
        }
        if (_.isEmpty(_.get(errorJson, 'defect_mask'))) {
          setDefectMapSrc('');
        } else {
          try {
            res = await fetch(`${serverEndpoint}/data?data_uri=${_.get(errorJson, 'defect_mask')}`);
          } catch (e) {
            // handleRequestFailed('fetchDefectMask', e);
            return;
          }
          const blob3 = await res.blob();
          const reader3 = new FileReader();
          reader3.readAsArrayBuffer(blob3);
          const defectMask = await new Promise((resolve) => {
            reader3.onload = (event) => {
              resolve(event.target.result);
            };
          });

          // console.log('defectMask', defectMask);
          const width = _.get(curFeature, 'roi.points[1].x') - _.get(curFeature, 'roi.points[0].x') + 1;
          const height = _.get(curFeature, 'roi.points[1].y') - _.get(curFeature, 'roi.points[0].y') + 1;
          const newDataUrl = await convertGrayScaleArrayBufferToGreenToRedImage(defectMask, width, height);
          setDefectMapSrc(newDataUrl);
        }
      }
    };

    fetchAndCrop(goldenProdFeatures, selectedFeatureId, curSessionStepInfo);
  }, [
    selectedFeatureId,
    step,
    curSessionStepInfo,
    goldenProductId,
    ipcProductId,
    inspectionInfo,
    goldenProdFeatures,
  ]);

  useEffect(() => {
    if (_.isNull(selectedFeatureId) || _.isEmpty(selectedLineItem) || _.isEmpty(curSessionStepInfo)) return;

    const lineItemStr = _.get(_.split(selectedLineItem, '-'), '1');
    if (_.isEmpty(lineItemStr)) {
      setSelectedDetail(null);
      return;
    }

    setSelectedDetail(lineItemStr);

    const lineItem = _.find(curSessionStepInfo, (item) => item.feature_id === selectedFeatureId && item.detail === lineItemStr);
    if (_.isEmpty(lineItem)) return;

    if (_.includes(threeDLineItems, lineItemStr)) {
      setTabItems([
        {
          key: '0',
          label: <span className='font-source text-[12px] font-normal'>{translation('viewInspection.comparison2d')}</span>,
        },
        {
          key: '1',
          label: <span className='font-source text-[12px] font-normal'>{translation('viewInspection.heightDifferenceComparison')}</span>,
        },
      ]);
      setHeightDiffIpcCloudUri(_.get(lineItem, 'cropped_point_cloud_uri', ''));
    } else {
      setTabItems([
        {
          key: '0',
          label: <span className='font-source text-[12px] font-normal'>{translation('viewInspection.comparison2d')}</span>,
        },
      ]);
      setActiveTabKey('0');
      setHeightDiffIpcCloudUri('');
    }

    if (!_.isEmpty(_.get(lineItem, 'error', '{}'))) {
      try {
        setSelectedParsedErrorDetail(_.get(JSON.parse(_.get(lineItem, 'error')), 'error_detail', null));
      } catch (e) {
        setSelectedParsedErrorDetail(null);
      }
    }
  }, [curSessionStepInfo, selectedFeatureId, selectedLineItem]);

  if (_.isEmpty(selectedFeature)) return null;

  return (
    <Fragment>
    <SubmitNGFeedbackScoreLowerThanMaxOk
      isOpened={isNGFeedbackConfirmationModalOpened}
      setIsOpened={setIsNGFeedbackConfirmationModalOpened}
      ngConfirmPayload={ngConfirmPayload}
      callbackRef={ngConfirmCallbackRef}
    />
    <div className='flex w-[458px] flex-col items-start self-stretch bg-[#ffffff0d] flex-1'>
      <div className='flex pt-1 pr-4 pl-4 items-center self-stretch h-[40px] w-full'>
        <ConfigProvider
          theme={{
            components: {
              Tabs: {
                cardBg: '#131313',
                colorBgContainer: 'rgba(86, 204, 242, 0.16)',
                cardHeight: '36',
                cardPadding: '8px 12px',
                colorPrimary: '#fff',
                itemHoverColor: '#fff',
                itemColor: '#E0E0E0',
              },
            },
          }}
        >
          <CustomTabs
            style={{ width: '100%', height: '100%' }}
            type='card'
            activeKey={selectedDetail}
            onChange={(k) => {
              setSelectedDetail(k);
              setSelectedLineItem(`${selectedFeatureId}-${k}`);
            }}
            items={_.map(_.filter(curSessionStepInfo, i => i.feature_id === Number(selectedFeatureId)), i => ({
              label: <div className='flex items-center gap-1 self-stretch justify-center'>
                { !i.pass &&
                  <div className='flex w-3 h-3 flex-col justify-center items-center'>
                    <img src='/img/icn/icn_warning_red.svg' className='w-4 h-[10.5px] shrink' alt='warning' />
                  </div>
                }
                <span
                  className={`font-source text-[12px] font-${selectedDetail === i.detail ? 'semibold' : 'normal'}`}
                  // style={{ color: selectedDetail === i.detail ? '#fff' : '#E0E0E0' }}
                >
                  {translation(`viewInspection.lineItem.${i.detail}`)}
                </span>
              </div>,
              key: i.detail,
              children: null
            }))}
          />
        </ConfigProvider>
      </div>
      {!_.isEmpty(selectedDetail) &&
        <div className='flex py-2 px-4 justify-between items-start self-stretch border-[1px] border-gray-1 bg-[#56ccf229]'>
          <div className='flex py-[3px] flex-col items-start gap-2 flex-1'>
            <div className='flex items-center gap-6 self-stretch'>
              <div className='flex items-start gap-2 self-stretch'>
                <span className='font-source text-[14px] font-semibold w-[84px]'>
                  {translation('viewInspection.AIPrediction')}
                </span>
              </div>
              <div className='flex flex-col items-start gap-0.5 self-stretch'>
                <div className='flex items-center gap-1 self-stretch'>
                  <span className='font-source text-[12px] font-normal'>
                    {_.get(selectedFeature, 'feature_scope') === 'global' ? _.get(selectedFeature, 'feature_type') : _.get(selectedFeature, 'feature_type').substring(1)}
                  </span>
                  <span className='font-source text-[12px] font-normal'>
                    -
                  </span>
                  <div className='flex items-center gap-1 self-stretch py-[2px]'>
                    { _.get(_.find(curSessionStepInfo, (item) => item.feature_id === selectedFeatureId && item.detail === selectedDetail), 'pass') ?
                      <Fragment>
                        <img src='/img/icn/icn_checkCircle_green.svg' className='w-4 h-[10.5px] shrink' alt='warning' />
                        <span className='font-source text-[12px] font-normal'>
                          {translation(`viewInspection.annotateFeedbackOptions.${selectedDetail}.good`)}
                        </span>
                      </Fragment>
                    :
                      <Fragment>
                        <img src='/img/icn/icn_warning_red.svg' className='w-4 h-[10.5px] shrink' alt='warning' />
                        <span className='font-source text-[12px] font-normal'>
                        {translation(`viewInspection.annotateFeedbackOptions.${selectedDetail}.notGood`)}
                        </span>
                      </Fragment>
                    }
                  </div>
                </div>
                {!_.isEmpty(selectedParsedErrorDetail) && _.isNumber(_.get(selectedParsedErrorDetail, 'ai_deviation_score')) && selectedDetail === defectDetection &&
                  <div className='flex items-center gap-1 self-stretch'>
                    <span className='font-source text-[12px] font-normal'>
                      {translation('common.ai_deviation_score')}:
                    </span>
                    {_.get(selectedParsedErrorDetail, 'ai_deviation_score') === -1 ?
                      <span className='font-source text-[12px] font-normal'>
                        {translation('common.modelNotFound')}
                      </span>
                    :
                      <span className='font-source text-[12px] font-normal'>
                        {_.round(_.get(selectedParsedErrorDetail, 'ai_deviation_score', 0), 4)}
                      </span>
                    }
                  </div>
                }
                {!_.isEmpty(selectedParsedErrorDetail) && _.isNumber(_.get(selectedParsedErrorDetail, 'ai_deviation_threshold')) && selectedDetail === defectDetection &&
                  <div className='flex items-center gap-1 self-stretch'>
                    <span className='font-source text-[12px] font-normal'>
                      {translation('common.ai_deviation_threshold')}:
                    </span>
                    {_.get(selectedParsedErrorDetail, 'ai_deviation_threshold') === -1 ?
                      <span className='font-source text-[12px] font-normal'>
                        {translation('common.modelNotFound')}
                      </span>
                    :
                      <span className='font-source text-[12px] font-normal'>
                        {_.round(_.get(selectedParsedErrorDetail, 'ai_deviation_threshold', 0), 4)}
                      </span>
                    }
                  </div>
                }
                {!_.isEmpty(selectedParsedErrorDetail) && _.isNumber(_.get(selectedParsedErrorDetail, 'height_difference', null)) && selectedDetail === heightDiff &&
                  <div className='flex items-center gap-1 self-stretch'>
                    <span className='font-source text-[12px] font-normal'>
                      {translation('common.height_difference')} (mm):
                    </span>
                    <span className='font-source text-[12px] font-normal'>
                      {_.round(_.get(selectedParsedErrorDetail, 'height_difference', 0), 4)}
                    </span>
                  </div>
                }
                {!_.isEmpty(selectedParsedErrorDetail) && _.isNumber(_.get(selectedParsedErrorDetail, 'height_difference_threshold', null)) && selectedDetail === heightDiff &&
                  <div className='flex items-center gap-1 self-stretch'>
                    <span className='font-source text-[12px] font-normal'>
                      {translation('common.height_difference_threshold')} (mm):
                    </span>
                    <span className='font-source text-[12px] font-normal'>
                      {_.round(_.get(selectedParsedErrorDetail, 'height_difference_threshold', 0), 4)}
                    </span>
                  </div>
                }
                {/* {!_.isEmpty(selectedParsedErrorDetail) && _.isNumber(_.get(selectedParsedErrorDetail, 'tolerance', null)) && selectedDetail === heightDiff &&
                  <div className='flex items-center gap-1 self-stretch'>
                    <span className='font-source text-[12px] font-normal'>
                      {translation('common.tolerance')}:
                    </span>
                    <span className='font-source text-[12px] font-normal'>
                      {_.get(selectedParsedErrorDetail, 'tolerance', 0)}
                    </span>
                  </div>
                } */}
              </div>
            </div>
            <div className='flex items-center gap-6 self-stretch'>
              <span className='font-source text-[14px] font-normal w-[84px]'>
                {translation('viewInspection.feedback')}
              </span>
              <Select
                style={{ width: '90px' }}
                popupMatchSelectWidth={false}
                size='small'
                options={[
                  {
                    label: <div className='flex items-center gap-1 self-stretch'>
                      <img src='/img/icn/icn_checkCircle_green.svg' className='w-4 h-[10.5px] shrink' alt='good' />
                      <span className='font-source text-[12px] font-normal'>
                        {translation(`viewInspection.annotateFeedbackOptions.${selectedDetail}.good`)}
                      </span>
                    </div>,
                    value: true, 
                  },
                  {
                    label: <div className='flex items-center gap-1 self-stretch'>
                      <img src='/img/icn/icn_warning_red.svg' className='w-4 h-[10.5px] shrink' alt='notGood' />
                      <span className='font-source text-[12px] font-normal'>
                        {translation(`viewInspection.annotateFeedbackOptions.${selectedDetail}.notGood`)}
                      </span>
                    </div>,
                    value: false,
                  },
                  {
                    label: <span className='font-source text-[12px] font-normal'>
                      {translation('viewInspection.annotateFeedbackOptions.notProvided')}
                    </span>,
                    value: 'np',
                  }
                ]}
                value={_.isEmpty(_.get(_.find(curSessionStepInfo, (item) => item.feature_id === selectedFeatureId && item.detail === selectedDetail), 'feedback'))
                  ?'np'
                  :convertFeedbackCorrectnessToChoice(
                    _.get(_.find(curSessionStepInfo, (item) => item.feature_id === selectedFeatureId && item.detail === selectedDetail), 'feedback.correct'),
                    _.get(_.find(curSessionStepInfo, (item) => item.feature_id === selectedFeatureId && item.detail === selectedDetail), 'pass')
                  )
                }
                onChange={(value) => {
                  if (value === 'np') {
                    handleCancelFeedback({
                      selectedDetail,
                      selectedFeatureId,
                      curSessionStepInfo,
                      ipcProductId,
                      selectedFeature,
                      step,
                      setIsDrawMaskEnabled,
                    })
                    return;
                  }
                  if (value === true) {
                    handleFeedbackGoodClick({
                      selectedDetail,
                      selectedFeatureId,
                      curSessionStepInfo,
                      ipcProductId,
                      selectedFeature,
                      step,
                      setIsDrawMaskEnabled,
                    });
                  } else {
                    handleFeedbackNotGoodClick({
                      curSessionStepInfo,
                      selectedFeatureId,
                      ipcProductId,
                      selectedFeature,
                      step,
                      selectedDetail,
                      setIsDrawMaskEnabled,
                      goldenProductId,
                    });
                  }
                }}
              />
              { !isDrawMaskEnabled &&
              !_.isEmpty(_.get(_.find(curSessionStepInfo, i => i.feature_id === selectedFeatureId && i.detail === selectedDetail), 'feedback')) &&
              !_.isEmpty(_.get(_.find(curSessionStepInfo, i => i.feature_id === selectedFeatureId && i.detail === selectedDetail), 'feedback.defect_mask_uri')) &&
                <Button
                  size='small'
                  disabled={isDrawMaskEnabled}
                  onClick={() => {
                    setIsDrawMaskEnabled(true);
                  }}
                >
                  <div className='flex items-center gap-1'>
                    <img src='/img/icn/icn_pencil_blue.svg' alt='pencil' className='w-3 h-3' />
                    <span className='font-source text-[12px] font-normal text-AOI-blue'>
                      {translation('viewInspection.editPrevMask')}
                    </span>
                  </div>
                </Button>
              }
              { isDrawMaskEnabled &&
                <Fragment>
                  <Button
                    size='small'
                    onClick={() => handleSaveMaskAndFeedback({
                      curSessionStepInfo,
                      selectedFeatureId,
                      ipcProductId,
                      selectedFeature,
                      step,
                      drawMaskViewerRef,
                    })}
                  >
                    <span className='font-source text-[12px] font-semibold'>
                      {translation('viewInspection.submitAsFeedback')}
                    </span>
                  </Button>
                  <Button
                      size='small'
                      onClick={() => {
                        setIsDrawMaskEnabled(false);
                      }}
                    >
                      <span className='font-source text-[12px] font-semibold'>
                        {translation('common.cancel')}
                      </span>
                    </Button>
                </Fragment>
              }
            </div>
          </div>
        </div>
      }
      <div className='flex py-2 flex-col gap-2 items-start flex-1 self-stretch'>
        <div className='flex flex-col justify-center items-center self-stretch'>
          <div className='flex py-1 px-2 items-center gap-2 self-stretch'>
            <div className='flex w-6 h-6 flex-col justify-center items-center'>
              <img src='/img/icn/icn_swap_white.svg' className='w-[17px] h-[12px] shrink' alt='swap' />
            </div>
            <span className='font-source text-[12px] font-semibold'>
              {translation('viewInspection.referenceComparison')}
            </span>
          </div>
        </div>
        <div className='flex px-4 flex-col items-start flex-1 self-stretch'>
          { selectedDetail === heightDiff &&
            <Fragment>
              <div className='flex py-2 flex-col items-start gap-0.5 self-stretch'>
                <span className='font-source text-[12px] font-normal'>
                  {translation('common.pointCloud')}
                </span>
                <div className='flex flex-col items-start gap-0.5 self-stretch'>
                  <div className='p-1 flex justify-between items-center self-stretch '>
                    <div className='flex items-center gap-2'>
                      <div
                        className='flex w-6 h-6 flex-col justify-center items-center cursor-pointer hover:bg-gray-1 rounded-[2px] gap-2.5 transition-all duration-300'
                        onClick={() => setIsIpcCloudVisible(!isIpcCloudVisible)}
                      >
                        { isIpcCloudVisible ?
                          <img src='/img/icn/icn_visible_white.svg' className='w-[14px] h-[9.5px] shrink' alt='visible' />
                          :
                          <img src='/img/icn/icn_invisible_white.svg' className='w-[14px] h-[11.5px] shrink' alt='invisible' />
                        }
                      </div>
                      <div className='w-[14px] h-[14px] rounded-[2px] border-[1px] border-gray-3 bg-default-yellow' />
                      <span className='font-source text-[12px] font-normal'>
                        {translation('viewInspection.sample')}
                      </span>
                    </div>
                    <div className='flex gap-1 justify-center items-center'>
                      <div className='flex w-[14px] h-[14px] items-center justify-center'>
                        <img src='/img/icn/icn_calendar_white.svg' className='w-[10px] h-[10px] shrink' alt='calendar' />
                      </div>
                      <span className='font-source text-[10px] font-normal'>
                        {backendAutoGenTimeToDisplayString(_.get(inspectionInfo, 'timestamp'))}
                      </span>
                    </div>
                  </div>
                  <div className='p-1 flex justify-between items-center self-stretch '>
                    <div className='flex items-center gap-2'>
                      <div
                        className='flex w-6 h-6 flex-col justify-center items-center cursor-pointer hover:bg-gray-1 rounded-[2px] gap-2.5 transition-all duration-300'
                        onClick={() => setIsGoldenCloudVisible(!isGoldenCloudVisible)}
                      >
                        { isGoldenCloudVisible ?
                        <img src='/img/icn/icn_visible_white.svg' className='w-[14px] h-[9.5px] shrink' alt='visible' />
                        :
                        <img src='/img/icn/icn_invisible_white.svg' className='w-[14px] h-[11.5px] shrink' alt='invisible' />
                        }
                      </div>
                      <div className='w-[14px] h-[14px] rounded-[2px] border-[1px] border-gray-3 bg-default-purple-2' />
                      <span className='font-source text-[12px] font-normal'>
                        {translation('common.goldenProduct')}
                      </span>
                    </div>
                    <div className='flex gap-1 justify-center items-center'>
                      <div className='flex w-[14px] h-[14px] items-center justify-center'>
                        <img src='/img/icn/icn_calendar_white.svg' className='w-[10px] h-[10px] shrink' alt='calendar' />
                      </div>
                      <span className='font-source text-[10px] font-normal'>
                        {backendAutoGenTimeToDisplayString(_.get(goldenProduct, 'last_modified'))}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div className='flex py-1 flex-col items-start gap-2.5 self-stretch flex-1'>
                <div className='flex items-center gap-2.5 self-stretch'>
                  <div className='flex items-center gap-1'>
                    <div
                      className='flex w-[32px] h-[24px] justify-center items-center gap-2.5 rounded-[2px] hover:bg-gray-2 cursor-pointer transition-all duration-300'
                      onClick={() => {
                        setPointCloudDisplayedView('top');
                        setIsIpcCloudVisible(true);
                        setIsGoldenCloudVisible(true);
                        setActiveHeightDiffComparisonView('stacked');
                      }}
                    >
                      { activeHeightDiffComparisonView === 'stacked' ?
                        <img src='/img/icn/icn_overlay_blue.svg' className='w-[13.8px] h-[14px] shrink' alt='overlay' />
                        :
                        <img src='/img/icn/icn_overlay_white.svg' className='w-[13.8px] h-[14px] shrink' alt='overlay' />
                      }
                    </div>
                    <div
                      className='flex w-[32px] h-[24px] justify-center items-center gap-2.5 rounded-[2px] hover:bg-gray-2 cursor-pointer transition-all duration-300'
                      onClick={() => {
                        setPointCloudDisplayedView('top');
                        setIsIpcCloudVisible(true);
                        setIsGoldenCloudVisible(true);
                        setActiveHeightDiffComparisonView('separate');
                      }}
                    >
                      { activeHeightDiffComparisonView === 'separate' ?
                        <img src='/img/icn/icn_separate_blue.svg' className='w-[13.8px] h-[14px] shrink' alt='separate' />
                        :
                      <img src='/img/icn/icn_separate_white.svg' className='w-[13.8px] h-[14px] shrink' alt='separate' />
                      }
                    </div>
                  </div>
                  <Select
                    popupMatchSelectWidth={false}
                    style={{ width: '120px' }}
                    value={pointCloudDisplayedView}
                    onChange={(value) => setPointCloudDisplayedView(value)}
                    options={[
                      // {
                      //   label: <div className='flex h-[26px] py-1 px-3 items-center gap-2 self-stretch'>
                      //     <img src='/img/icn/icn_perspective_white.svg' className='w-[12px] h-[12px] shrink' alt='perspective' />
                      //     <span className='font-source text-[12px] font-normal'>
                      //       {translation('viewInspection.perspective')}
                      //     </span>
                      //   </div>,
                      //   value: 'perspective',
                      // },
                      {
                        label: <div className='flex h-[26px] py-1 px-3 items-center gap-2 self-stretch'>
                          <img src='/img/icn/icn_topView_white.svg' className='w-[12px] h-[12px] shrink' alt='top' />
                          <span className='font-source text-[12px] font-normal'>
                            {translation('viewInspection.top')}
                          </span>
                        </div>,
                        value: 'top',
                      },
                      {
                        label: <div className='flex h-[26px] py-1 px-3 items-center gap-2 self-stretch'>
                          <img src='/img/icn/icn_frontView_white.svg' className='w-[12px] h-[12px] shrink' alt='front' />
                          <span className='font-source text-[12px] font-normal'>
                            {translation('viewInspection.front')}
                          </span>
                        </div>,
                        value: 'front',
                      },
                      {
                        label: <div className='flex h-[26px] py-1 px-3 items-center gap-2 self-stretch'>
                          <img src='/img/icn/icn_backView_white.svg' className='w-[12px] h-[12px] shrink' alt='back' />
                          <span className='font-source text-[12px] font-normal'>
                            {translation('viewInspection.back')}
                          </span>
                        </div>,
                        value: 'back',
                      },
                      {
                        label: <div className='flex h-[26px] py-1 px-3 items-center gap-2 self-stretch'>
                          <img src='/img/icn/icn_leftView_white.svg' className='w-[12px] h-[12px] shrink' alt='left' />
                          <span className='font-source text-[12px] font-normal'>
                            {translation('viewInspection.left')}
                          </span>
                        </div>,
                        value: 'left',
                      },
                      {
                        label: <div className='flex h-[26px] py-1 px-3 items-center gap-2 self-stretch'>
                          <img src='/img/icn/icn_rightView_white.svg' className='w-[12px] h-[12px] shrink' alt='right' />
                          <span className='font-source text-[12px] font-normal'>
                            {translation('viewInspection.right')}
                          </span>
                        </div>,
                        value: 'right',
                      },
                    ]}
                  />
                  {/* <Button
                    onClick={() => setPointCloudDisplayedView('top')}
                  >
                    <span className='font-source text-[12px] font-normal'>
                      {translation('common.resetView')}
                    </span>
                  </Button> */}
                </div>
                <div className='flex flex-col flex-1 items-center justify-center h-full w-full'>
                  { activeHeightDiffComparisonView === 'separate' &&
                    <HeightDiffComparison
                      goldenCloudUri={_.get(selectedFeature, 'cropped_point_cloud_uri', '')}
                      ipcCloudUri={heightDiffIpcCloudUri}
                      inspectionInfo={inspectionInfo}
                      goldenProduct={goldenProduct}
                      isIpcCloudVisible={isIpcCloudVisible}
                      isGoldenCloudVisible={isGoldenCloudVisible}
                      setIsIpcCloudVisible={setIsIpcCloudVisible}
                      setIsGoldenCloudVisible={setIsGoldenCloudVisible}
                      pointCloudDisplayedView={pointCloudDisplayedView}
                    />
                  }
                  { activeHeightDiffComparisonView === 'stacked' &&
                    <HeightDiffStacked
                      goldenCloudUri={_.get(selectedFeature, 'cropped_point_cloud_uri', '')}
                      ipcCloudUri={heightDiffIpcCloudUri}
                      inspectionInfo={inspectionInfo}
                      goldenProduct={goldenProduct}
                      isIpcCloudVisible={isIpcCloudVisible}
                      isGoldenCloudVisible={isGoldenCloudVisible}
                      setIsIpcCloudVisible={setIsIpcCloudVisible}
                      setIsGoldenCloudVisible={setIsGoldenCloudVisible}
                      pointCloudDisplayedView={pointCloudDisplayedView}
                    />
                  }
                </div>
              </div>
            </Fragment>
          }
          { selectedDetail === defectDetection &&
            <Fragment>
              <div className='flex flex-col flex-1 self-stretch items-start justify-center gap-2'>
                <div className={`flex ${window.innerHeight > 700 ? 'flex-col' : ''} flex-1 gap-2 self-stretch`}>
                  <div className='flex px-4 flex-col items-center gap-2 self-stretch flex-1'>
                    <div className='flex justify-between items-center w-full'>
                      <span className='font-source text-[12px] font-normal self-stretch'>
                        {translation('viewInspection.sample')}
                      </span>
                      <div className='flex items-center gap-1'>
                        <div className='flex w-[14px] h-[14px] items-center justify-center'>
                          <img src='/img/icn/icn_calendar_white.svg' className='w-[10px] h-[10px] shrink' alt='calendar' />
                        </div>
                        <span className='font-source text-[10px] font-normal'>
                          {backendAutoGenTimeToDisplayString(_.get(inspectionInfo, 'timestamp'))}
                        </span>
                      </div>
                    </div>
                    <div className='flex flex-col flex-1 self-stretch items-start justify-center h-full'>
                      <div className='relative w-full h-full'>
                        <div
                          className='absolute top-0 left-0 z-[30] w-full h-full'
                          style={{
                            height: `${window.innerHeight > 700 ? 'calc((100vh - 388px - 135px)/2)' : '100%'}`,
                            width: '100%',
                          }}
                        >
                          <ReferenceImageViewer
                            src={inferenceProductCroppedSrc}
                            curDisplayOptionsBrightness={curDisplayOptionsBrightness}
                            curDisplayOptionsContrast={curDisplayOptionsContrast}
                            curDisplayOptionsSaturation={curDisplayOptionsSaturation}
                            // isSharpnessEnabled={isSharpnessEnabled}
                            displayCanvasRef={inferenceProductCanvasRef}
                            selectedParsedErrorDetail={selectedParsedErrorDetail}
                            selectedDetail={selectedDetail}
                          />
                        </div>
                        {/* {showDlOutputMask && !_.isEmpty(defectMapSrc) &&
                          <img
                            src={defectMapSrc}
                            className='object-contain absolute top-0 left-0 z-[2]'
                            style={{
                              height: 'calc((100vh - 388px - 200px - 54px)/2)',
                              width: '100%',
                            }}
                            alt='defect'
                          />
                        } */}
                      </div>
                    </div>
                    {/* <div className='flex flex-col gap-1 self-stretch items-start'>
                      <div className='flex py-1 gap-2 items-center self-stretch'>
                        <Checkbox onChange={(e) => setShowDlOutputMask(e.target.checked)} />
                        <span className='font-source text-[12px] font-normal'>
                          {translation('viewInspection.displayDefectHeatmap')}
                        </span>
                      </div>
                      <div className='flex py-1 flex-col items-start self-stretch gap-1'>
                        <div
                          className='h-1 self-stretch w-full rounded-[2px]'
                          style={{ background: 'linear-gradient(90deg, #FF0606 0%, #F2FF00 40%, rgba(255, 255, 255, 0.00) 98.5%)' }}
                        />
                        <div className='flex self-stretch items-center justify-between'>
                          <span className='font-source text-[12px] font-normal'>
                            {translation('viewInspection.highlyDefective')}
                          </span>
                          <span className='font-source text-[12px] font-normal'>
                            {translation('viewInspection.notDefective')}
                          </span>
                        </div>
                      </div>
                    </div> */}
                  </div>
                  <div className='flex px-4 flex-col items-center flex-1 gap-2 self-stretch'>
                    <div className='flex justify-between items-center w-full'>
                      <span className='font-source text-[12px] font-normal self-stretch'>
                        {translation('common.goldenProduct')}
                      </span>
                      <div className='flex items-center gap-1'>
                        <div className='flex w-[14px] h-[14px] items-center justify-center'>
                          <img src='/img/icn/icn_calendar_white.svg' className='w-[10px] h-[10px] shrink' alt='calendar' />
                        </div>
                        <span className='font-source text-[10px] font-normal'>
                          {backendAutoGenTimeToDisplayString(_.get(goldenProduct, 'last_modified'))}
                        </span>
                      </div>
                    </div>
                    <div className='flex flex-col flex-1 self-stretch items-start justify-center h-full'>
                      <div
                        className='w-full h-full'
                        style={{
                          height: `${window.innerHeight > 700 ? 'calc((100vh - 388px - 135px)/2)' : '100%'}`,
                          width: '100%',
                        }}
                      >
                        <ReferenceImageViewer
                          src={goldenProductCroppedSrc}
                          curDisplayOptionsBrightness={curDisplayOptionsBrightness}
                          curDisplayOptionsContrast={curDisplayOptionsContrast}
                          curDisplayOptionsSaturation={curDisplayOptionsSaturation}
                          // isSharpnessEnabled={isSharpnessEnabled}
                          displayCanvasRef={goldenProductCanvasRef}
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <Button
                  type='text'
                  style={{ width: '100%' }}
                  onClick={() => handleReDirect(`/aoi/edit-product/${goldenProductId}?editStep=1&detectionStep${step}&featureId=${selectedFeatureId}`)}
                >
                  <span className='font-source text-[12px] font-semibold text-AOI-blue'>
                    {translation('viewInspection.viewGoldenProduct')}
                  </span>
                </Button>
              </div>
            </Fragment>
          }
        </div>
      </div>
    </div>
    </Fragment>
  );
};

const CustomTabs = styled(Tabs)`
  .ant-tabs-nav {
    margin: 0;
  }
`;

export default ReferenceComparison;