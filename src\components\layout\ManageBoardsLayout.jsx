import React from 'react';
import { translation } from '../../common/util';
import _ from 'lodash';


const ManageBoardsLayout = (props) => {
  const {
    handleBack,
    productName,
    variantName,
    stepNumber,
  } = props;

  return (
    <div
      className={`flex pt-2 pr-8 pb-4 pl-8 flex-col items-start gap-4 flex-1 self-stretch
        rounded-[2px] bg-[#131313]
      `}
    >
      <div className='flex h-12 items-center py-2 gap-3 self-stretch'>
        <div
          className='flex w-6 h-6 cursor-pointer justify-center items-center gap-2.5 hover:bg-[#c4c4c44d] rounded-[4px] transition-all duration-300'
          onClick={() => handleBack()}
        >
          <img src='/img/icn/icn_arrowLeft_white.svg' alt='back' className='w-[8px] h-[16px] shrink' />
        </div>
        <div className='flex w-5 h-5 p-0.5 justify-center items-center gap-2.5'>
          <img src='/img/icn/icn_board_white.svg' alt='board' className='w-5 h-5 fill-white shrink-0' />
        </div>
        <span className='font-source text-[20px] font-semibold'>
          {`${translation('manageBoards.manageBoards')}${!_.isEmpty(productName) ? `: ${productName}` : ''}`}
          {/* ${!_.isEmpty(variantName) ? `-${variantName}` : ''}${!_.isEmpty(stepNumber) && !_.isUndefined(stepNumber) ? `-${stepNumber}` : ''} */}
        </span>
      </div>
      <div className='w-full h-[1px] bg-[#333]' />
      {props.children}
    </div>
  );
};

export default ManageBoardsLayout;