import React, { useState } from 'react';
import { DarkButton, DarkModal } from '../../common/darkModeComponents';
import { handleRequestFailed, translation } from '../../common/util';
import { Select } from 'antd';
import { useGetAllProductsQuery, useLazyGetInferenceStatusQuery, useRunInferencePipelineMutation, useStopInferenceMutation } from '../../services/product';
import _ from 'lodash';
import { ALERT_TYPES, aoiAlert } from '../../common/alert';
import { useLazyGetAllSessionsQuery } from '../../services/session';
import { useDispatch } from 'react-redux';
import { setCurRunningIpcSessionIds } from '../../actions/setting';
import { retrainModelTaskPhaseType, serverEndpoint } from '../../common/const';


const NewInspection = (props) => {
  const dispatch = useDispatch();

  const { isOpened, setIsOpened, handleRedirect } = props;

  const [selectedProductId, setSelectedProductId] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  const { data: allProducts, isLoading: isGetAllProductsLoading } = useGetAllProductsQuery();
  const [startInference] = useRunInferencePipelineMutation();
  const [getInferenceStatus] = useLazyGetInferenceStatusQuery();
  const [getSessions] = useLazyGetAllSessionsQuery();

  const checkIfReadyForRetrain = (modelStatus) => {
    if (_.isEmpty(modelStatus)) return true;
    return _.isEmpty(_.filter(modelStatus, (taskStatus) => !_.includes([
      retrainModelTaskPhaseType.failure,
      retrainModelTaskPhaseType.complete,
      retrainModelTaskPhaseType.invalid
    ], taskStatus.phase)));
  };

  return (
    <DarkModal
      open={isOpened}
      onCancel={() => {
        if (isLoading) return;
        setIsOpened(false);
      }}
      title={<span className='font-source text-[16px] font-semibold'>
        {translation('newInspection.newInspection')}
      </span>}
      footer={
        <div className='flex p-2 items-start gap-2 self-stretch justify-center'>
          <DarkButton
            style={{ width: '50%' }}
            onClick={async () => {
              setIsOpened(false);
            }}
            loading={isLoading}
          >
            {translation('common.cancel')}
          </DarkButton>
          <DarkButton
            loading={isLoading}
            isPrimary
            style={{ width: '50%' }}
            onClick={async () => {
              if (!_.isString(selectedProductId)) {
                aoiAlert(translation('notification.error.selectAGoldenProduct'), ALERT_TYPES.COMMON_ERROR);
                return;
              }
              setIsLoading(true);
              const res = await getInferenceStatus();
              if (_.get(res, 'data.status') !== 'stopped') {
                aoiAlert(translation('notification.error.inferenceAlreadyRunning'), ALERT_TYPES.COMMON_ERROR);
                setIsLoading(false);
                return;
              }

              let modelStatusRes;
              try {
                modelStatusRes = await fetch(`${serverEndpoint}/getModelUpdates`);
              } catch (error) {
                handleRequestFailed('getModelUpdates', error);
                setIsLoading(false);
                return;
              }
              const modelStatus = await modelStatusRes.json();
              if (!checkIfReadyForRetrain(modelStatus)) {
                // if model is not ready for retrain, then some training tasks are still running
                aoiAlert(translation('notification.error.someTrainingTaskIsRunning'), ALERT_TYPES.COMMON_ERROR);
                setIsLoading(false);
                return;
              }

              const startInfRes = await startInference(Number(selectedProductId));
              if (startInfRes.error) {
                setIsLoading(false);
                handleRequestFailed('startInference', startInfRes.error);
                return;
              }

              const sessionRes = await getSessions({ complete: -1, limit: 1 });

              if (sessionRes.error) {
                handleRequestFailed('getStartedSession', sessionRes.error);
                setIsLoading(false);
                return;
              }

              if (_.get(sessionRes, 'data.data.length', 0) === 0) {
                handleRequestFailed('getStartedSession', 'No running session found');
                setIsLoading(false);
                return;
              }

              dispatch(setCurRunningIpcSessionIds([_.get(sessionRes, 'data.data[0].ipc_session_id', null)]));

              setIsLoading(false);
              setIsOpened(false);
              handleRedirect(`/aoi/live-dashboard?isNewInspectionTriggered=true`);
            }}
          >
            {translation('newInspection.startInspection')}
          </DarkButton>
        </div>
      }
    >
      <div className='flex py-6 px-4 flex-col gap-8 items-start self-stretch'>
        <div className='flex flex-col gap-2 items-start self-stretch'>
          <span className='font-source text-[14px] font-semibold'>
            {translation('newInspection.inspectionSetup')}
          </span>
          <div className='flex gap-6 items-center self-stretch'>
            <span className='font-source text-[12px] font-semibold whitespace-nowrap'>
              {translation('newInspection.chooseAProduct')}
            </span>
            <Select
              style={{ width: '100%' }}
              options={_.filter(allProducts, (product) => product.is_golden).map((product) => ({
                label: product.product_name,
                value: product.product_id,
              }))}
              onChange={(value) => {
                setSelectedProductId(value);
              }}
              value={selectedProductId}
              loading={isGetAllProductsLoading}
            />
          </div>
        </div>
        <span className='font-source text-[14px] font-semibold'>
          {translation('newInspection.startInferenceDesc')}
        </span>
        {/* <div className='flex flex-col items-start gap-2 self-stretch'>
          <span className='font-source text-[12px] font-normal'>
            {translation('newInspection.selectedProductInfo')}
          </span>
          <div className='flex gap-2 flex-col items-start self-stretch'>
            <div className='flex flex-col gap-0.5 self-stretch items-start'>
              <div
                className='flex py-1 px-2 gap-[14px] self-stretch items-center rounded-[4px]'
                style={{ background: 'rgba(0, 0, 0, 0.20)' }}
              >
                <div className='flex py-1 items-center gap-2'>
                  <span className='font-source text-[12px] font-semibold'>
                    {translation('newInspection.description')}
                  </span>
                </div>
                <span className='font-source text-[12px] font-normal'>
                  {_.get(_.find(allProducts, { product_id: selectedProductId }), 'description')}
                </span>
              </div>
            </div>
            <div className='flex flex-col gap-0.5 self-stretch items-start'>
              <div
                className='flex py-1 px-2 gap-[14px] self-stretch items-center rounded-[4px]'
                style={{ background: 'rgba(0, 0, 0, 0.20)' }}
              >
                <div className='flex py-1 items-center gap-2'>
                  <span className='font-source text-[12px] font-semibold'>
                    {translation('newInspection.aiModel')}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div> */}
      </div>
    </DarkModal>
  )
};

export default NewInspection;