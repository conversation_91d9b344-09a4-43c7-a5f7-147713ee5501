import React, { useEffect } from 'react';
import { handleRequestFailed, translation } from '../../common/util';
import { Checkbox, Tooltip } from 'antd';
import _ from 'lodash';
import { useUpdateCurProductComponentToggleMutation } from '../../services/product';
import { ALERT_TYPES, aoiAlert } from '../../common/alert';


const LiveComponentFilter = (props) => {
  const {
    isLiveComponentFilterOpened,
    setIsLiveComponentFilterOpened,
    currentFilterMap,
    setCurrentFilterMap,
  } = props;

  // const [currentFilterMap, setCurrentFilterMap] = useState({
  //   // 'test1': true,
  //   // 'test2': true,
  //   // 'test3': true,
  //   // 'test4': false,
  //   // 'longTypeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee': true,
  // });

  const [updateCurProductComponentlist] = useUpdateCurProductComponentToggleMutation();

  return (
    <div
      className='absolute top-[226px] left-0 z-[2] rounded-[4px] bg-[#333] transition-all duration-300 border-[2px] border-AOI-green'
      style={{
        width: `${isLiveComponentFilterOpened ? '326px' : '32px'}`,
        height: `${isLiveComponentFilterOpened ? '600px' : '200px'}`,
      }}
      onMouseEnter={() => setIsLiveComponentFilterOpened(true)}
      onMouseLeave={() => setIsLiveComponentFilterOpened(false)}
    >
      {!isLiveComponentFilterOpened ? (
        <div className='flex justify-center items-center h-full flex-col gap-2'>
          <span
            className='font-source text-[14px] font-semibold text-AOI-green'
            style={{ writingMode: 'vertical-rl' }}
          >
            {translation('viewInspection.liveFilterComponent')}
          </span>
        </div>
      ) : (
        <div className='flex p-4 flex-col flex-1 self-stretch gap-2 h-full'>
          <div className='flex items-center gap-1'>
            <span className='font-source text-[14px] font-semibold'>
              {translation('viewInspection.selectDisabledComponentForCurrentProduct')}
            </span>
            <Tooltip title={
              <span className='font-source text-[12px] font-normal'>
                {translation('viewInspection.systemWillSkipDisabledComponent')}
              </span>
            }>
              <img src='/img/icn/icn_info_white.svg' className='w-3 h-3' alt='info' />
            </Tooltip>
          </div>
          <div className='h-[535px] overflow-y-auto w-full'>
            <div
              style={{
                display: 'grid',
                gridTemplateColumns: '140px 140px',
                gap: '8px',
              }}
            >
              {_.map(_.keys(currentFilterMap), (type) => (
                <div
                  key={type}
                  className='flex items-center gap-1'
                >
                  <Checkbox
                    size='small'
                    checked={_.get(currentFilterMap, type, false)}
                    onChange={(e) => {
                      // have to leave at least one component enabled
                      const tmp = {
                        ...currentFilterMap,
                        [type]: e.target.checked,
                      };
                      if (_.every(_.values(tmp), (v) => !v)) {
                        aoiAlert(translation('notification.error.pleaseLeaveAtLeastOneComponentEnabled'), ALERT_TYPES.COMMON_ERROR);
                        return;
                      }
                      const handleUpdate = async (currentFilterMap, checked, type) => {
                        const res = await updateCurProductComponentlist({
                          current_product_options: {
                            ...currentFilterMap,
                            [type]: checked,
                          },
                        });

                        if (res.error) {
                          handleRequestFailed('updateCurProductComponentlist', res.error);
                          return;
                        }

                        setCurrentFilterMap({
                          ...currentFilterMap,
                          [type]: checked,
                        });
                      };
                      handleUpdate(currentFilterMap, e.target.checked, type);
                    }}
                  />
                  <td
                    className='font-source text-[14px] font-normal overflow-hidden overflow-ellipsis'
                    title={type}
                  >
                    {type}
                  </td>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default LiveComponentFilter;