import { Button, Input, Select, Table, Tooltip, DatePicker } from 'antd';
import React, { Fragment, useEffect, useState } from 'react';
import { backendAutoGenTimeToDisplayString, backendTimestampToDisplayString, handleRequestFailed, toLocalISOString, translation } from '../../common/util';
import { useDeleteInspectionRecordByProductIdMutation, useLazyGetAllInspectionsQuery } from '../../services/session';
import _ from 'lodash';
import i18n from '../../i18n';
import { customZhCNDatePickerLocale } from '../../common/const';
import enUS from 'antd/es/date-picker/locale/en_US';
import enLocale from 'antd/es/locale/en_US';
import cnLocale from 'antd/es/locale/zh_CN';
import { DarkTable } from '../../common/darkModeComponents';


const { RangePicker } = DatePicker;

const AllInspections = (props) => {
  const {
    allProducts,
  } = props;

  const [displayedIpc, setDisplayedIpc] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 0,
    total: 0,
  });
  const [startTimeInInspection, setStartTimeInInspection] = useState(null);
  const [endTimeInInspection, setEndTimeInInspection] = useState(null);
  const [onlyFeedbackProvided, setOnlyFeedbackProvided] = useState(false);
  const [onlyDefectiveItems, setOnlyDefectiveItems] = useState(false);
  const [selectedGoldenProdId, setSelectedGoldenProdId] = useState(null);
  const [searchMode, setSearchMode] = useState('goldenProduct');
  const [displaySearchSN, setDisplaySearchSN] = useState('');
  const [getInspectionsQueryParam, setGetInspectionsQueryParam] = useState({
    page: 0,
    limit: 0,
  });
  const [searchSerialNumber, setSearchSerialNumber] = useState('');

  const [getInspections] = useLazyGetAllInspectionsQuery();
  const [deleteInspectionRecord] = useDeleteInspectionRecordByProductIdMutation();

  const cols = [
    {
      title: translation('worklist.serialNumber'),
      key: 'serial_no',
      render: (text, record) => {
        return (
          <span className='font-source text-[12px] font-normal'>
            {_.get(record, 'serial_no', '')}
          </span>
        );
      },
    },
    {
      title: translation('worklist.goldenProductName'),
      key: 'golden_product_name',
      render: (text, record) => {
        return (
          <span className='font-source text-[12px] font-normal'>
            {_.get(record, 'golden_product_name', '')}
          </span>
        );
      }
    },
    {
      title: translation('worklist.date'),
      key: 'date',
      render: (text, record) => {
        return (
          <span className='font-source text-[12px] font-normal'>
            {_.get(record, 'timestamp') ? backendAutoGenTimeToDisplayString(record.timestamp) : ''}
          </span>
        );
      },
    },
    {
      title: translation('worklist.totalComponent'),
      key: 'total_component',
      // dataIndex: 'total_roi_count',
      render: (text, record) => {
        return (
          <span className='font-source text-[12px] font-normal'>
            {_.get(record, 'total_roi_count', 0)}
          </span>
        );
      },
    },
    {
      title: translation('worklist.okngCounts'),
      key: 'okng_counts',
      render: (text, record) => {
        return (
          <div className='flex flex-1 items-center'>
            <div className='flex w-[56px] items-center gap-1'>
              <div className='flex flex-col items-center justify-center'>
                <img className='w-[12px] h-[12px]' src='/img/icn/icn_checkCircle_green.svg' alt='checkCircle' />
              </div>
              <span className='font-source text-[12px] font-normal'>
                {_.get(record, 'total_roi_count', 0) - _.get(record, 'defective_roi_count', 0)}
              </span>
            </div>
            <div className='flex w-[56px] items-center gap-1'>
              <div className='flex flex-col items-center justify-center'>
                <img className='w-[12px] h-[12px]' src='/img/icn/icn_warning_red.svg' alt='warning' />
              </div>
              <span className='font-source text-[12px] font-normal'>
                {_.get(record, 'defective_roi_count', 0)}
              </span>
            </div>
          </div>
        );
      },
    },
    {
      title: translation('worklist.passFail'),
      key: 'pass_fail',
      render: (text, record) => (
        <div className='flex gap-2 items-center'>
          { _.get(record, 'defective_roi_count', 0) > 0 ? 
            <Fragment>
              <div className='flex flex-col items-center justify-center'>
                <img className='w-[12px] h-[12px]' src='/img/icn/icn_warning_red.svg' alt='warning' />
              </div>
              <span className='font-source text-[12px] font-semibold text-[#EB5757]'>
                {translation('worklist.fail')}
              </span>
            </Fragment>
          :
          <Fragment>
            <div className='flex flex-col items-center justify-center'>
              <img className='w-[12px] h-[12px]' src='/img/icn/icn_checkCircle_green.svg' alt='check' />
            </div>
            <span className='font-source text-[12px] font-semibold text-[#27AE60]'>
              {translation('worklist.pass')}
            </span>
          </Fragment>
          }
        </div>
      ),
    },
    {
      title: translation('worklist.feedback'),
      key: 'feedback',
      render: (text, record) => (
        <>
          { record.feedback_provided ? <div className='flex items-center gap-2'>
              <img className='w-[16px] h-[16px]' src='/img/icn/icn_check_green.svg' alt='check' />
              <span className='font-source text-[12px] font-normal'>
                {translation('worklist.provided')}
              </span>
            </div> : <div className='flex items-center gap-2'>
              <span className='font-source text-[12px] font-normal'>
                {translation('worklist.notProvided')}
              </span>
          </div> }
        </>
      ),
    },
    {
      title: translation('worklist.actions'),
      key: 'actions',
      render: (text, record) => (
        <div className='flex items-center gap-2'>
          <Button
            type='text'
            onClick={() => {
              window.location.href = `/aoi/view-existed-inference/${_.get(record, 'golden_product_id')}/${_.get(record, 'product_id')}?step=0`;
            }}
          >
            <span className='font-source text-[12px] font-normal text-AOI-blue'>
              {translation('worklist.review')}
            </span>
          </Button>
          <Button
            type='text'
            onClick={() => {
              const deleteIpc = async (getInspectionsQueryParam, pagination) => {
                const res = await deleteInspectionRecord(_.get(record, 'product_id'));
                if (res.error) {
                  handleRequestFailed('deleteInspectionRecord', res.error);
                  return;
                }
                handleFilterUpdate({
                  ...getInspectionsQueryParam,
                  page: pagination.current - 1,
                  limit: pagination.pageSize,
                }, pagination);
              };
              deleteIpc(getInspectionsQueryParam, pagination);
            }}
          >
            <span className='font-source text-[12px] font-normal text-red'>
              {translation('common.delete')}
            </span>
          </Button>
        </div>
      ),
    },
  ];

  const handleFilterUpdate = async (query, pagination) => {
    const res = await getInspections(query);
    if (res.error) {
      handleRequestFailed('getInspections', res.error);
      setDisplayedIpc([]);
      return;
    }

    setDisplayedIpc(_.get(res, 'data.data', []));
    setPagination({
      ...pagination,
      total: Number(_.get(res, 'data.pageCount', 0)) * pagination.pageSize,
    });
  };

  useEffect(() => {
    if (pagination.pageSize === 0) return;

    const newQueryParam = {};

    if (searchMode === 'goldenProduct' && _.isInteger(selectedGoldenProdId)) {
      newQueryParam.golden_product_id = selectedGoldenProdId;
    }
    if (searchMode === 'sn' && !_.isEmpty(searchSerialNumber)) {
      newQueryParam.serial_no = searchSerialNumber;
    }

    if (startTimeInInspection) {
      // newQueryParam.start_datetime = startTimeInInspection.valueOf();
      // convert to UTC in YYYY-MM-DDTHH:mm:ss.sssZ format
      newQueryParam.start_datetime = toLocalISOString(new Date(startTimeInInspection));
    }
    if (endTimeInInspection) {
      // newQueryParam.end_datetime = endTimeInInspection.valueOf();
      // convert to UTC in YYYY-MM-DDTHH:mm:ss.sssZ format
      newQueryParam.end_datetime = toLocalISOString(new Date(endTimeInInspection));
    }
    
    if (_.isBoolean(onlyFeedbackProvided) && onlyFeedbackProvided) {
      newQueryParam.feedback = 1;
    }

    if (_.isBoolean(onlyDefectiveItems) && onlyDefectiveItems) {
      newQueryParam.defect = 1;
    }

    // newQueryParam.page = pagination.current - 1;
    // newQueryParam.limit = pagination.pageSize;

    newQueryParam.page = 0;
    newQueryParam.limit = pagination.pageSize;

    setPagination({
      ...pagination,
      current: 1,
    });

    setGetInspectionsQueryParam(newQueryParam);
    handleFilterUpdate(newQueryParam, {
      ...pagination,
      current: 1,
    });
  }, [
    searchMode,
    searchSerialNumber,
    startTimeInInspection,
    endTimeInInspection,
    onlyFeedbackProvided,
    onlyDefectiveItems,
    selectedGoldenProdId,
  ]);

  useEffect(() => {
    setDisplaySearchSN(searchSerialNumber);
    
    const tableTBodyHeight = window.innerHeight - 340;
    // setPageSize(Math.max(Math.floor(tableTBodyHeight / 55), 1));
    setPagination({
      ...pagination,
      pageSize: Math.max(Math.floor(tableTBodyHeight / 65), 1),
    });

    handleFilterUpdate({
      page: 0,
      limit: Math.max(Math.floor(tableTBodyHeight / 65), 1),
    }, {
      current: 1,
      pageSize: Math.max(Math.floor(tableTBodyHeight / 65), 1),
      total: 0,
    });

    const handleResize = () => {
      const tableTBodyHeight = window.innerHeight - 340;
      // setPageSize(Math.max(Math.floor(tableTBodyHeight / 55), 1));
      setPagination({
        ...pagination,
        pageSize: Math.max(Math.floor(tableTBodyHeight / 65), 1),
      });
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  return (
    <div className='flex flex-col gap-4 flex-1 self-stretch items-start'>
      {/* <div className='flex gap-8 self-stretch items-center py-2'>
        <div className='flex items-baseline gap-6'>
          <div className='flex items-center gap-2'>
            <span className='font-source text-[14px] font-normal'>
              {translation('worklist.totalProducts')}
            </span>
            <span className='font-source text-[14px] font-semibold'>
              {_.get(selectedSession, 'total_product_count', 0)}
            </span>
          </div>
          <div className='flex items-center gap-2'>
            <span className='font-source text-[14px] font-normal'>
              {translation('worklist.goodProducts')}
            </span>
            <span className='font-source text-[14px] font-semibold text-[#57F2C4]'>
              {_.get(selectedSession, 'total_product_count', 0)-_.get(selectedSession, 'defective_product_count', 0)}
            </span>
          </div>
          <div className='flex items-center gap-2'>
            <span className='font-source text-[14px] font-normal'>
              {translation('worklist.defectiveProducts')}
            </span>
            <span className='font-source text-[14px] font-semibold text-[#EB5757]'>
              {_.get(selectedSession, 'defective_product_count', 0)}
            </span>
          </div>
        </div>
      </div> */}
      <div className='flex py-2 gap-4 self-stretch items-center'>
        <div className='flex gap-1 items-center'>
          { searchMode === 'goldenProduct' ?
            <Select
              showSearch
              options={
                _.isEmpty(allProducts) ? []
                : _.map(allProducts, (product) => ({
                  label: <span className='font-source text-[12px] font-normal'>
                    {product.product_name}
                  </span>,
                  value: Number(product.product_id),
                }))
              }
              value={selectedGoldenProdId}
              onChange={(value) => setSelectedGoldenProdId(value)}
              placeholder={<span className='font-source text-[12px] font-normal'>{translation('worklist.filterByGoldenProduct')}</span>}
              style={{ width: '200px' }}
              popupMatchSelectWidth={false}
              allowClear
            />
          :
            <Input
              value={displaySearchSN}
              onChange={(e) => setDisplaySearchSN(e.target.value)}
              style={{ width: '200px' }}
              onBlur={(e) => setSearchSerialNumber(e.target.value)}
              onPressEnter={(e) => setSearchSerialNumber(e.target.value)}
            />
          }
          <Select
            options={[
              {
                value: 'goldenProduct',
                label: <span className='font-source text-[12px] font-normal'>{translation('worklist.filterByGoldenProduct')}</span>,
              },
              {
                value: 'sn',
                label: <span className='font-source text-[12px] font-normal'>{translation('worklist.filterBySerialNumber')}</span>,
              },
            ]}
            value={searchMode}
            onChange={(value) => setSearchMode(value)}
            style={{ width: '200px' }}
            popupMatchSelectWidth={false}
          />
        </div>
        <RangePicker
          locale={i18n.language === 'cn' ? customZhCNDatePickerLocale : enUS}
          showTime
          onCalendarChange={(value) => {
            setStartTimeInInspection(_.get(value, '0', null));
            setEndTimeInInspection(_.get(value, '1', null));
          }}
          value={[startTimeInInspection, endTimeInInspection]}
        />
        <Button
          type={onlyFeedbackProvided ? 'primary' : 'default'}
          onClick={() => setOnlyFeedbackProvided(!onlyFeedbackProvided)}
        >
          <span className={`font-source text-[12px] font-normal ${onlyFeedbackProvided ? 'text-[#333]' : 'text-white'}`}>
            {translation('worklist.feedbackProvided')}
          </span>
        </Button>
        <Button
          type={onlyDefectiveItems ? 'primary' : 'default'}
          onClick={() => setOnlyDefectiveItems(!onlyDefectiveItems)}
        >
          <span className={`font-source text-[12px] font-normal ${onlyDefectiveItems ? 'text-[#333]' : 'text-white'}`}>
            {translation('worklist.onlyDisplayDefectiveItems')}
          </span>
        </Button>
        <Tooltip
          title={translation('worklist.clearFilter')}
        >
          <div className='flex w-[32px] h-[32px] justify-center items-center cursor-pointer' onClick={() => {
            setStartTimeInInspection(null);
            setEndTimeInInspection(null);
            setOnlyFeedbackProvided(false);
            setOnlyDefectiveItems(false);
          }}>
            <img className='w-[16px] h-[16px]' src='/img/icn/icn_cancelFilter_blue.svg' alt='cancelFilter' />
          </div>
        </Tooltip>
        {/* <Tooltip
          title={translation('worklist.export')}
        >
          <div className='flex w-[32px] h-[32px] justify-center items-center cursor-pointer' onClick={() => {
            setIsExportModalOpened(true);
          }}>
            <img className='w-[16px] h-[16px]' src='/img/icn/icn_download_blue.svg' alt='filter' />
          </div>
        </Tooltip> */}
      </div>
      <DarkTable
        locale={i18n.language === 'cn' ? cnLocale.Table : enLocale.Table}
        style={{ width: '100%' }}
        columns={cols}
        dataSource={displayedIpc}
        pagination={{
          pageSize: pagination.pageSize,
          total: pagination.total,
          current: pagination.current,
          hideOnSinglePage: true,
          showSizeChanger: false,
          showQuickJumper: true,
        }}
        rowHoverable={false}
        onChange={(pagination) => {
          setPagination({
            ...pagination,
            current: pagination.current,
          });
          handleFilterUpdate({
            ...getInspectionsQueryParam,
            page: pagination.current - 1,
            limit: pagination.pageSize,
          }, pagination);
        }}
      />
    </div>
  );
};

export default AllInspections;