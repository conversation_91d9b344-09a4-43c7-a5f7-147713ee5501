import React, { Fragment, useEffect, useRef, useState } from 'react';
import { getLatestRetrainFinishTime, handleRequestFailed, translation } from '../../common/util';
import ChangeLanguage from '../modal/ChangeLanguage';
import { Badge, Button, Dropdown, Spin } from 'antd';
import styled from 'styled-components';
import { useDispatch, useSelector } from 'react-redux';
import EditSystemConfig from '../modal/EditSystemConfig';
import UpdateBackendHost from '../modal/UpdateBackendHost';
import _ from 'lodash';
import { retrainModelTaskPhaseType, serverEndpoint } from '../../common/const';
import { ALERT_TYPES, aoiAlert } from '../../common/alert';
import { setContainerTransparentLockEnabled, setContainerWindowLoadingLocked, setCurRunningIpcSessionIds, setCurTrainingTaskStartTime, setIsInspectionErrorQueueOpened, setIsTrainingRuning, setLatestRetrainFinishTimeByModelType, setUserType } from '../../actions/setting';
import { PoweroffOutlined, PlayCircleOutlined } from '@ant-design/icons';
import NewInspection from '../modal/NewInspection';
import { useRunInferencePipelineMutation, useStopInferenceMutation } from '../../services/product';
import DataStorageManage from '../modal/DataStorageManage';
import { BellOutlined } from '@ant-design/icons';
import SwitchUserType from '../modal/SwitchUserType';


const MainMenu = (props) => {
  const dispatch = useDispatch();

  const loopCheckRetrainStatus = useRef(null);

  const [isChangeLanguageModalOpened, setIsChangeLanguageModalOpened] = useState(false);
  const [isUpdateBackendHostModalOpened, setIsUpdateBackendHostModalOpened] = useState(false);
  const [isEditSystemConfigModalOpened, setIsEditSystemConfigModalOpened] = useState(false);
  const [isNewInspectionModalOpened, setIsNewInspectionModalOpened] = useState(false);
  const [isDataStorageManageModalOpened, setIsDataStorageManageModalOpened] = useState(false);
  const [isSwitchUserTypeModalOpened, setIsSwitchUserTypeModalOpened] = useState(false);
  const [appVersion, setAppVersion] = useState('');

  const [stopInference] = useStopInferenceMutation();

  const isInferenceRunning = useSelector(state => state.setting.isInferenceRunning);
  const isTrainingRuning = useSelector(state => state.setting.isTrainingRuning);
  const curTrainingTaskStartTime = useSelector(state => state.setting.curTrainingTaskStartTime);
  const isInspectionErrorQueueOpened = useSelector(state => state.setting.isInspectionErrorQueueOpened);
  const inspectionErrorQueue = useSelector(state => state.setting.inspectionErrorQueue);
  const userType = useSelector(state => state.setting.userType);

  const checkIfReadyForRetrain = (modelStatus) => {
    if (_.isEmpty(modelStatus)) return true;
    return _.isEmpty(_.filter(modelStatus, (taskStatus) => !_.includes([
      retrainModelTaskPhaseType.failure,
      retrainModelTaskPhaseType.complete,
      retrainModelTaskPhaseType.invalid
    ], taskStatus.phase)));
  };

  const initFetchTrainingStatusPolling = async () => {
    if (loopCheckRetrainStatus.current) {
      clearInterval(loopCheckRetrainStatus.current);
      loopCheckRetrainStatus.current = null;
    }

    loopCheckRetrainStatus.current = setInterval(async () => {
      let modelStatusRes;
      try {
        modelStatusRes = await fetch(`${serverEndpoint}/getModelUpdates`);
      } catch (error) {
        handleRequestFailed('getModelUpdates', error);
        clearInterval(loopCheckRetrainStatus.current);
        loopCheckRetrainStatus.current = null;
        dispatch(setContainerWindowLoadingLocked(false));
        return;
      }
      const modelStatus = await modelStatusRes.json();
      if (checkIfReadyForRetrain(modelStatus)) {
        const latestRetainFinishTimeByModelType = getLatestRetrainFinishTime(modelStatus);
        dispatch(setLatestRetrainFinishTimeByModelType(latestRetainFinishTimeByModelType));
        clearInterval(loopCheckRetrainStatus.current);
        loopCheckRetrainStatus.current = null;
        dispatch(setContainerWindowLoadingLocked(false));
        // search for the task with greatest schedule_id
        const latestTask = _.maxBy(modelStatus, (task) => task.schedule_id);
        if (_.includes([retrainModelTaskPhaseType.failure, retrainModelTaskPhaseType.invalid], _.get(latestTask, 'phase'))) {
          aoiAlert(translation('notification.error.retrainFailed'), ALERT_TYPES.COMMON_ERROR);
          return;
        }
        aoiAlert(translation('notification.success.retrainFinished'), ALERT_TYPES.COMMON_SUCCESS);
        // await sleep(500);
        // props.history.push('/aoi/home');

        dispatch(setIsTrainingRuning(false));
        dispatch(setCurTrainingTaskStartTime(null));

        return;
      }
    }, 1000);
  };

  const items = [
    {
      key: '0',
      label: <div className='w-full' onClick={() => setIsChangeLanguageModalOpened(true)}>
        <span className='font-source text-[12px] font-normal'>
          {translation('mainMenu.changeLanguage')}
        </span>
      </div>,
    },
    {
      key: '2',
      label: <div className='w-full' onClick={() => setIsUpdateBackendHostModalOpened(true)}>
        <span className='font-source text-[12px] font-normal'>
          {translation('mainMenu.updateBackendHost')}
          </span>
      </div>,
    },
    ...(userType === 'admin' ? [{
      key: '1',
      label: <div className='w-full' onClick={() => setIsEditSystemConfigModalOpened(true)}>
        <span className='font-source text-[12px] font-normal'>
          {translation('editSystemConfig.editSystemConfig')}
        </span>
      </div>,
    }] : []),
    {
      key: '3',
      label: <div className='w-full' onClick={() => {
          setIsDataStorageManageModalOpened(true);
        }}>
        <span className='font-source text-[12px] font-normal'>
          {translation('dataStorageManage.dataStorageManage')}
        </span>
      </div>,
    },
    {
      key: 'switch',
      label: <div className='w-full' onClick={() => {
        if (userType === 'admin') {
          dispatch(setUserType('operator'));
        } else {
          setIsSwitchUserTypeModalOpened(true);
        }
      }}>
        <span className='font-source text-[12px] font-normal'>
          {userType === 'admin' ? translation('mainMenu.swapToOperatorUser') : translation('mainMenu.swapToAdminUser')}
        </span>
      </div>,
    },
  ];

  useEffect(() => {
    if (!isTrainingRuning) {
      if (loopCheckRetrainStatus.current) {
        clearInterval(loopCheckRetrainStatus.current);
        loopCheckRetrainStatus.current = null;
      }
    } else {
      // init fetch training polling
      initFetchTrainingStatusPolling();
    }
  }, [isTrainingRuning]);

  useEffect(() => {
    // get version
    const initAppVersion = async () => {
      try {
        const res = await fetch('/config.json');
        const data = await res.json();
        setAppVersion(_.get(data, 'appVersion'));
      } catch (err) {
        console.error('Failed to get app version');
      }
    };

    initAppVersion();
  }, []);

  return (
    <Fragment>
      <ChangeLanguage
        isOpened={isChangeLanguageModalOpened}
        setIsOpened={setIsChangeLanguageModalOpened}
      />
      <UpdateBackendHost
        updatedRequired={false}
        isOpened={isUpdateBackendHostModalOpened}
        setIsOpened={setIsUpdateBackendHostModalOpened}
      />
      <EditSystemConfig
        isOpened={isEditSystemConfigModalOpened}
        setIsOpened={setIsEditSystemConfigModalOpened}
      />
      <NewInspection
        isOpened={isNewInspectionModalOpened}
        setIsOpened={() => setIsNewInspectionModalOpened(false)}
        handleRedirect={(url) => window.location.href = url}
      />
      <DataStorageManage
        isOpened={isDataStorageManageModalOpened}
        setIsOpened={setIsDataStorageManageModalOpened}
      />
      <SwitchUserType
        isOpened={isSwitchUserTypeModalOpened}
        setIsOpened={setIsSwitchUserTypeModalOpened}
      />
      <div
        className={`flex h-[64px] w-full pt-[2px] pr-[24px] pb-[2px] pl-[2px] items-center shrink-0 slef-stretch 
        border-t-[1px] border-[#28587B] bg-gray-1 shadow`}
        style={{ background: 'var(--nav-background, linear-gradient(0deg, rgba(0, 0, 0, 0.70) 0%, rgba(0, 0, 0, 0.70) 100%), #073B4C)' }}
      >
        <HomeButton
          className='flex w-[84px] py-3 flex-col justify-center items-center gap-[2px] self-stretch rounded-[6px] cursor-pointer'
          onClick={() => window.location.href = '/aoi/home'}
        >
          <img src='/img/icn/icn_home_blue.svg' className='w-6 h-6' alt='home' />
          <span className='font-source text-[12px] font-normal text-AOI-blue'>
            {translation('mainMenu.home')}
          </span>
        </HomeButton>
        <div className='flex px-6 items-center gap-6 flex-1 self-stretch'>
          <img src='/img/icn/icn_daoai_blackwhite.png' alt='logo' className='w-[100px] h-[24px]' />
          <div className='flex flex-col items-start justify-center gap-2 self-stretch'>
            <span className='font-inter text-[16px] font-medium'>
              {translation('mainMenu.daoaiAOISystem')}
            </span>
            <span className='font-source text-[12px] font-normal'>
              {translation('mainMenu.productVersion', { version: appVersion })}
            </span>
          </div>
        </div>
        <div className='flex justify-end items-center gap-6'>
          {/* <div className='flex items-center gap-1'>
            <span className='font-source text-[12px] font-normal'>
              {translation('mainMenu.lastInspection')}
            </span>
            <span className='font-source text-[12px] font-normal'>
              None
            </span>
          </div> */}
          { isTrainingRuning &&
            <div className='flex items-center gap-1'>
              <Spin
                size='small'
              />
              <span className='font-source text-[12px] font-normal'>
                {translation('mainMenu.trainingTaskIsRunning')}
              </span>
              { _.isNumber(curTrainingTaskStartTime) &&
                <span className='font-source text-[12px] font-normal'>
                  {translation('mainMenu.currentTrainingTaskStartTime', { time: new Date(curTrainingTaskStartTime).toLocaleString() })}
                </span>
              }
            </div>
          }
          <div className='flex items-center gap-1 px-2'>
            <span className='font-source text-[12px] font-normal whitespace-nowrap'>
              {translation('common.currentUserType')}{userType === 'admin' ? translation('mainMenu.admin') : translation('mainMenu.operator')}
            </span>
          </div>
          <div className='flex items-center gap-1 px-2'>
            <span className='font-source text-[12px] font-normal whitespace-nowrap'>
              {translation('mainMenu.inferenceStatus')}
            </span>
            {isInferenceRunning ?
              <div className='w-3 h-3 rounded-full' style={{ background: '#27AE60' }} />
            :
              <img src='/img/icn/icn_halfMoon_yellow.svg' className='w-3 h-3' alt='moon' />
            }
            <span className='font-source text-[12px] font-normal whitespace-nowrap'>
              {isInferenceRunning ? translation('mainMenu.inspectionRunning') : translation('mainMenu.inspectionIdle')}
            </span>
          </div>
          <div className='flex items-center gap-1 w-[150px] px-2'>
            <Button
              type='text'
              onClick={() => {
                const stop = async () => {
                  dispatch(setContainerWindowLoadingLocked(true));
                  const res = await stopInference();
                  if (res.error) {
                    handleRequestFailed('stopInference', res.error);
                    return;
                  }
                  dispatch(setCurRunningIpcSessionIds([]));
                  dispatch(setContainerWindowLoadingLocked(false));
                }

                if (isInferenceRunning) {
                  stop();
                } else {
                  setIsNewInspectionModalOpened(true);
                }
              }}
            >
              <div className='flex gap-2 items-center'>
                {isInferenceRunning ? <PoweroffOutlined style={{ color: '#EB5757' }} /> : <PlayCircleOutlined style={{ color: '#81F499' }} />}
                <span className='font-source text-[12px] font-normal' style={{ lineHeight: 'normal', color: isInferenceRunning ? '#EB5757' : '#81F499' }}>
                  {isInferenceRunning ? translation('mainMenu.stopInference') : translation('mainMenu.startInference')}
                </span>
              </div>
            </Button>
          </div>
          <Badge count={inspectionErrorQueue.length}>
            <Button
              shape='circle'
              type='text'
              onClick={() => {
                dispatch(setIsInspectionErrorQueueOpened(!isInspectionErrorQueueOpened));
              }}
            >
              <BellOutlined
                style={{ color: '#fff' }}
              />
            </Button>
          </Badge>
          <div className='flex items-center gap-1'>
            {/* <span className='font-source text-[12px] font-normal'>
              username placeholder
            </span> */}
            <Dropdown
              menu={{
                items
              }}
              placement='topLeft'
              trigger={['click']}
            >
              <img src='/img/icn/icn_setting_white.svg' className='w-4 h-4 cursor-pointer' style={{ fill: '#fff' }} alt='setting' />
            </Dropdown>
          </div>
        </div>
      </div>
    </Fragment>
  );
};

const HomeButton = styled.div`
  background: rgba(47, 128, 237, 0.10);
  &:hover {
    background: #073B4C;
    opacity: 0.7;
    transition: all 300ms;
    box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.25);
  }
`;

export default MainMenu;