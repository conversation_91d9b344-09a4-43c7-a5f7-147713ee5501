import React, { useEffect, useRef, useState } from 'react';
import { backendTimestampToDisplayString, debounce, translation } from '../../common/util';
import { HiddenCanvasDimensionCalcDiv, ThreeDDisplayWrapper } from '../../common/styledComponent/common';
import _ from 'lodash';
import { Button, Checkbox } from 'antd';
import HeightDiffViewer from '../common/viewer/HeightDiffViewer';


const HeightDiffStacked = (props) => {
  const {
    goldenCloudUri,
    ipcCloudUri,
    isIpcCloudVisible,
    isGoldenCloudVisible,
    pointCloudDisplayedView,
  } = props;
  
  const canvasRef = useRef(null);
  const canvasDimensionCalcDivRef = useRef(null);
  const viewerRef = useRef(null);

  const loadScene = async (ipcCloudUri, goldenCloudUri, viewer) => {
    viewer.loadCroppedGoldenAndIpcCloud({
      goldenCloudUri,
      ipcCloudUri,
    });
  };

  useEffect(() => {
    if (!viewerRef.current) return;
    viewerRef.current.updatePointCloudsVis(isGoldenCloudVisible, isIpcCloudVisible);
  }, [
    isGoldenCloudVisible,
    isIpcCloudVisible,
  ]);

  useEffect(() => {
    if (!viewerRef.current) return;
    viewerRef.current.updateCameraInZPlane(pointCloudDisplayedView);
  }, [pointCloudDisplayedView]);

  useEffect(() => {
    if (!viewerRef.current) return;
    loadScene(ipcCloudUri, goldenCloudUri, viewerRef.current);
  }, [
    goldenCloudUri,
    ipcCloudUri,
  ]);

  useEffect(() => {
    if (!canvasRef.current || !canvasDimensionCalcDivRef.current) return;

    const viewer = new HeightDiffViewer(
      canvasRef.current,
      canvasDimensionCalcDivRef.current.offsetHeight,
      canvasDimensionCalcDivRef.current.offsetWidth,
    );

    viewerRef.current = viewer;

    const updateCanvasDimension = () => {
      if (!viewer || !canvasDimensionCalcDivRef.current) return;
      viewerRef.current.updateSceneSize(canvasDimensionCalcDivRef.current.offsetWidth, canvasDimensionCalcDivRef.current.offsetHeight);
    };

    const debounceUpdateCanvasDimension = debounce(updateCanvasDimension, 300);

    window.addEventListener('resize', debounceUpdateCanvasDimension);

    if (!_.isEmpty(goldenCloudUri) && !_.isEmpty(ipcCloudUri)) {
      loadScene(ipcCloudUri, goldenCloudUri, viewer);
      viewer.updateCameraInZPlane(pointCloudDisplayedView);
    }

    return () => {
      if (viewer) {
        viewer.clearScene();
        viewer.destroy();
        viewerRef.current = null;
      }
      
      window.removeEventListener('resize', debounceUpdateCanvasDimension);
    };
  }, []);

  return (
    <div className='flex flex-col w-full h-full gap-2 pt-2'>
      <div className='relative w-full h-full'>
        <ThreeDDisplayWrapper>
          <canvas ref={canvasRef} />
        </ThreeDDisplayWrapper>
        <HiddenCanvasDimensionCalcDiv ref={canvasDimensionCalcDivRef} />
      </div>
    </div>
  );
};

export default HeightDiffStacked;