import React, { Fragment, useEffect, useRef, useState } from 'react';
import { translation } from '../../common/util';
import { Button, Checkbox, InputNumber, Select, Tooltip } from 'antd';
import _ from 'lodash';
import { featureType, inputRegularExpression } from '../../common/const';
import { useGetCustomFeaturesQuery } from '../../services/feature';
import { useSelector } from 'react-redux';
import { systemApi } from '../../services/system';
import { ALERT_TYPES, aoiAlert } from '../../common/alert';
import LineItems from './LineItems';


const NewFeatureModal = (props) => {
  const {
    productId,
    step,
    createFeaturePosition,
    AddFeatureModalDimension,
    systemMetadata,
    setCreateFeaturePosition,
    createFeaturePositionRef,
    handleCancel,
    handleAddFeature,
  } = props;

  const isCustomFeatureType = useRef(false);

  const [createFeatureSelectedType, setCreateFeatureSelectedType] = useState(null);
  const [createFeatureSensitivity, setCreateFeatureSensitivity] = useState(5);
  const [createFeatureHeightDifference, setCreateFeatureHeightDifference] = useState(3.0);
  const [isDefectDetectionEnabled, setIsDefectDetectionEnabled] = useState(true);
  const [isHeightDifferenceEnabled, setIsHeightDifferenceEnabled] = useState(false);
  const [availableFeatureTypes, setAvailableFeatureTypes] = useState([]);
  const [searchValue, setSearchValue] = useState('');
  const [lineItemParams, setLineItemParams] = useState({});
  const { data: customFeatures } = useGetCustomFeaturesQuery({ product_id: productId });

  const { data: systemMetadataData } = useSelector((state) => systemApi.endpoints.getSystemMetadata.select()(state));

  useEffect(() => {
    // setCreateFeatureSelectedType(
    //   _.get(systemMetadata, 'default_component_types[0]')
    // );
    // fetch available component types for this product
    setAvailableFeatureTypes([
      // ..._.map(_.get(systemMetadataData, 'default_component_types', []), f => ({ label: f, value: f })),
      ..._.map(customFeatures, (type) => ({ label: _.get(type, 'feature_scope') === 'global' ? _.get(type, 'feature_type'):  _.get(type, 'feature_type').substring(1), value: _.get(type, 'feature_type') })),
    ]);
  }, []);

  return (
    <div
      className='fixed z-[20] flex flex-col p-4 items-center gap-4 rounded-[4px] shadow'
      style={{
        background: 'linear-gradient(0deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.05) 100%), #131313bf',
        top: `${createFeaturePosition.top}px`,
        left: `${createFeaturePosition.left}px`,
        width: `${AddFeatureModalDimension.width}px`,
        height: `${AddFeatureModalDimension.height}px`,
      }}
    >
      <div className='flex flex-col gap-2 self-stretch items-start'>
        <div className='flex items-center gap-2 self-stretch'>
          <span className='font-source text-[14px] font-semibold'>
            {translation('productAnnotation.addComponent')}
          </span>
        </div>
        <div className='flex flex-col items-start gap-2 self-stretch'>
          <span className='font-source text-[12px] font-normal'>
            {translation('productAnnotation.type')}
          </span>
          {/* <Select
            size='small'
            style={{ width: '100%' }}
            options={_.map(_.get(systemMetadata, 'default_component_types', []), (f) => ({
              label: f,
              value: f,
            }))}
            value={createFeatureSelectedType}
            onChange={(value) => setCreateFeatureSelectedType(value)}
          /> */}
          <Select
            showSearch
            size='small'
            style={{ width: '100%' }}
            value={createFeatureSelectedType}
            onChange={(value) => {
              if (!_.startsWith(value, '_') && _.find(_.filter(customFeatures, f => f.feature_scope === 'global'), (f) => { return f.feature_type === value })) {
                isCustomFeatureType.current = false;
              } else {
                isCustomFeatureType.current = true;
              }
              setCreateFeatureSelectedType(value);
            }}
            onSearch={(value) => setSearchValue(value)}
            notFoundContent={
              !_.isEmpty(searchValue) ?
              <div className='flex items-center justify-between'>
                <span className='font-source text-[12px] font-norm0pal'>
                  {searchValue}
                </span>
                <Button
                  size='small'
                  onClick={() => {
                    const regExp = new RegExp(inputRegularExpression.customClass.className);
                    if (!regExp.test(searchValue)) {
                      aoiAlert(translation('notification.error.customClassNameCanOnlyContainLettersNumbersAndUnderscores'), ALERT_TYPES.COMMON_ERROR);
                      return;
                    }

                    const handle = async (searchValue, availableFeatureTypes, isCustomFeatureType) => {
                      setAvailableFeatureTypes([
                        ...availableFeatureTypes,
                        {
                          label: searchValue,
                          value: `_${searchValue}`,
                        },
                      ]);
                      setCreateFeatureSelectedType(`_${searchValue}`);
                      isCustomFeatureType.current = true;
                    };
                    handle(searchValue, availableFeatureTypes, isCustomFeatureType);
                  }}
                >
                  <span className='font-source text-[12px] font-semibold'>
                    {translation('common.add')}
                  </span>
                </Button>
              </div> : null
            }
            options={availableFeatureTypes}
          />
        </div>
        <div className='flex items-start flex-col gap-1 flex-1 w-full'>
          {createFeatureSelectedType !== featureType.product && (
            <LineItems
              lineItemParams={lineItemParams}
              setLineItemParams={setLineItemParams}
              systemMetadataData={systemMetadataData}
            />
            // <Fragment>
            //   <div className='flex gap-4 self-stretch items-center justify-between'>
            //     <div className='flex gap-2 items-center'>
            //       <Checkbox
            //         checked={isDefectDetectionEnabled}
            //         onChange={(e) => setIsDefectDetectionEnabled(e.target.checked)}
            //       />
            //       <div className='flex gap-1 items-center'>
            //         <span className='font-source text-[12px] font-normal whitespace-nowrap'>
            //           {translation('productAnnotation.AISensitivity')}
            //         </span>
            //         <Tooltip title={translation('productAnnotation.sensitivityDesc')}>
            //           <img src='/img/icn/icn_info_white.svg' className='w-[12px] h-[12px]' />
            //         </Tooltip>
            //       </div>
            //     </div>
            //     <InputNumber
            //       style={{ width: '100%' }}
            //       min={1}
            //       max={10}
            //       precision={0}
            //       size='small'
            //       controls={false}
            //       value={createFeatureSensitivity}
            //       onChange={(value) => setCreateFeatureSensitivity(value)}
                  
            //     />
            //   </div>
            //   <div className='flex gap-4 self-stretch items-center justify-between'>
            //     <div className='flex gap-1 items-center'>
            //       <Checkbox
            //         checked={isHeightDifferenceEnabled}
            //         onChange={(e) => setIsHeightDifferenceEnabled(e.target.checked)}
            //       />
            //       <span className='font-source text-[12px] font-normal whitespace-nowrap'>
            //         {translation('productAnnotation.heightDifference')}
            //       </span>
            //       <Tooltip title={translation('productAnnotation.heightDiffDesc')}>
            //         <img src='/img/icn/icn_info_white.svg' className='w-[12px] h-[12px]' />
            //       </Tooltip>
            //     </div>
            //     <InputNumber
            //       style={{ width: '100%' }}
            //       min={1}
            //       max={10}
            //       precision={1}
            //       size='small'
            //       controls={false}
            //       value={createFeatureHeightDifference}
            //       onChange={(value) => setCreateFeatureHeightDifference(value)}
            //     />
            //   </div>
            // </Fragment>
          )}
        </div>
      </div>
      <div className='flex gap-2 items-start self-stretch justify-between'>
        <Button
          size='small'
          style={{ width: '50%' }}
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            handleCancel();
            setCreateFeaturePosition(null);
            createFeaturePositionRef.current = null;
          }}
        >
          <span className='font-source text-[12px] font-semibold'>
          {translation('common.cancel')}
          </span>
        </Button>
        <Button
          size='small'
          style={{ width: '50%' }}
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            if (_.isEmpty(createFeatureSelectedType)) {
              aoiAlert(translation('notification.warning.selectAFeatureTypeFirst'), ALERT_TYPES.COMMON_WARNING);
              return;
            }
            if (createFeatureSelectedType === featureType.product) {
              handleAddFeature({
                featureParam: JSON.stringify({}),
                featureType: createFeatureSelectedType,
              });
              setCreateFeaturePosition(null);
              createFeaturePositionRef.current = null;
              return;
            }
            handleAddFeature({
              featureParam: JSON.stringify({
                "height_diff": {
                  "enabled": isHeightDifferenceEnabled,
                  "agent_name": "height_diff",
                  "agent_config": {
                      "std": createFeatureHeightDifference
                  }
                },
                "defect_detection": {
                  "enabled": isDefectDetectionEnabled,
                  "agent_name": "defect",
                  "agent_config": {
                      "sensitivity": createFeatureSensitivity
                  }
                }
              }),
              featureType: createFeatureSelectedType,
              featureScope: isCustomFeatureType.current ? 'product' : 'global',
              lineItemParams: lineItemParams,
            });
            setCreateFeaturePosition(null);
            createFeaturePositionRef.current = null;
          }}
        >
          <span className='font-source text-[12px] font-semibold'>
          {translation('common.add')}
          </span>
        </Button>
      </div>
    </div>
  );
};

export default NewFeatureModal;