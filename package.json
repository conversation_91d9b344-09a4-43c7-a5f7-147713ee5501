{"name": "aoi_web_client", "version": "0.1.0", "private": true, "dependencies": {"@reduxjs/toolkit": "^1.9.5", "antd": "^5.19.2", "connected-react-router": "^6.9.3", "cross-env": "^7.0.3", "fabric-with-erasing": "^1.0.1", "i18next": "^23.12.1", "i18next-browser-languagedetector": "^8.0.0", "jsoneditor": "^10.1.1", "lodash": "^4.17.21", "md5": "^2.3.0", "moment": "^2.30.1", "react": "^17.0.0", "react-dom": "17.0.0", "react-i18next": "^15.0.0", "react-redux": "^7.1.0", "react-router": "^5.3.4", "react-router-dom": "^6.11.2", "react-scripts": "5.0.1", "react-toastify": "^9.1.3", "redux": "^4.2.1", "redux-persist": "^6.0.0", "redux-persist-transform-filter": "^0.0.22", "redux-thunk": "^2.4.2", "styled-components": "^6.1.12", "three": "^0.167.0", "web-vitals": "^2.1.4", "yaml": "^2.7.0"}, "scripts": {"dev-start": "set PORT=3002 && react-scripts start", "start": "react-scripts start", "build": "cross-env GENERATE_SOURCEMAP=false react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "devDependencies": {"tailwindcss": "^3.4.6"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "overrides": {"webpack": "5.82.1"}}