import * as THREE from 'three';
import { TrackballControls } from 'three/examples/jsm/controls/TrackballControls';
import _ from 'lodash';


/**
  Init base viewer
  uses three.js webgl renderer, PerspectiveCamera, TrackballControls, and a default background color
  @param canvasRef: canvas reference
  @param elementHeight: height of the canvas
  @param elementWidth: width of the canvas
  @param getCanvasHeightWidth: function to get height and width of the canvas
  @param useCustomAnimate: boolean to use custom animation function
*/
export default class ViewGL {
  constructor(canvasRef, elementHeight, elementWidth, getCanvasHeightWidth, useCustomAnimate=false) {
    this.getHeightWidth = getCanvasHeightWidth;
    this.useCustomAnimate = useCustomAnimate;
    this.init(canvasRef, elementHeight, elementWidth);
    if (!useCustomAnimate) this.animate();
  };

  init(canvasRef, elementHeight, elementWidth) {
    this.canvasRef = canvasRef;

    this.renderer = new THREE.WebGLRenderer({
      canvas: canvasRef,
      antialias: false,
    });
    this.renderer.setPixelRatio(window.devicePixelRatio);
    this.renderer.setSize(elementWidth, elementHeight);

    this.elementWidth = elementWidth;
    this.elementHeight = elementHeight;

    const aspect = elementWidth / elementHeight;

    this.cameraPersp = new THREE.PerspectiveCamera(30, aspect, 0.01, 100000);
    this.currentCamera = this.cameraPersp;

    this.currentCamera.position.set(0, 0, 0);
    this.currentCamera.lookAt(0, 0, 0);
    this.currentCamera.up.set(0, -1, 0);

    this.scene = new THREE.Scene();
    this.scene.background = new THREE.Color( 'rgb(0, 0, 0)' );

    this.trackball = new TrackballControls(this.currentCamera, this.renderer.domElement);

    this.trackball.rotateSpeed = 1.8;
    this.trackball.zoomSpeed = 3.0;
    this.trackball.panSpeed = 0.2;

    this.trackball.staticMoving = true;

    // this.resetViewTrackballTarget = this.trackball.target.clone();

    // this.scene.add(new THREE.GridHelper(1000, 10, 0x888888, 0x444444));

    this.raycaster = new THREE.Raycaster();
    this.mouse = new THREE.Vector2();
    this.intersected = null;
    this.intersectedObjIds = [];

    this.isMouseInCanvas = false;

    canvasRef.addEventListener('mouseenter', () => {
      this.isMouseInCanvas = true;
    });
    canvasRef.addEventListener('mouseleave', () => {
      this.isMouseInCanvas = false;
    });

    canvasRef.addEventListener('mousemove', this.onMouseMove);

    // window.addEventListener('keydown', this.handleKeyDown.bind(this));
  };

  onMouseMove = (e) => {
		this.mouse.x = (e.offsetX / this.elementWidth) * 2 - 1;
		this.mouse.y = -(e.offsetY / this.elementHeight) * 2 + 1;
	};

  handleMouseEnter() {
    this.isMouseInCanvas = true;
  };

  handleMouseLeave() {
    this.isMouseInCanvas = false;
  };

  // handleKeyDown(e) {
  //   if (e.key === 'v') {
  //     if (!this.currentCamera || !this.trackball) return;
  //     let currentTarget;
  //     if (this.resetViewTrackballTarget) {
  //       currentTarget = this.resetViewTrackballTarget;
  //     } else {
  //       currentTarget = this.trackball.target.clone();
  //     }
  //     this.currentCamera.position.set(0, 0, 5000);
  //     this.currentCamera.lookAt(0, 0, 0);
  //     this.currentCamera.rotation.set(0, 0, 0);
  //     this.currentCamera.updateProjectionMatrix();
  //     this.trackball.reset();
  //     this.trackball.target.copy(currentTarget);
  //   }
  // }

  animate = () => {
    if (!this.renderer || !this.raycaster || !this.mouse) return;

    this.raycaster.setFromCamera(this.mouse, this.currentCamera);
    this.raycaster.params.Points.threshold = 0.5;

    this.intersects = this.raycaster.intersectObjects(
      _.filter(this.scene.children, (child) => _.includes(this.intersectedObjIds, child.id))
    );

    this.trackball.update();
    this.renderer.render(this.scene, this.currentCamera);
    requestAnimationFrame(this.animate);
  };

  updateSceneSize = (width, height) => {
    // console.log('resize', height, width);
    const aspect = width / height;
  
    this.cameraPersp.aspect = aspect;
    this.cameraPersp.updateProjectionMatrix();
  
    this.renderer.setSize(width, height);
    this.trackball.handleResize();

    this.elementWidth = width;
    this.elementHeight = height;
  };

  getFirstIntersectedPoint = () => {
    if (this.intersects && this.intersects.length > 0) {
			return this.intersects[0].point;
		} else {
			return {};
		}
  };

  getScene() {
    return this.scene;
  };

  getTrackballControls() {
    return this.trackball;
  };

  getCamera() {
    return this.currentCamera;
  };

  getRenderer() {
    return this.renderer;
  };

  // updateResetViewTrackballTarget(target) {
  //   this.resetViewTrackballTarget = target;
  // }

  destroy = () => {
    this.canvasRef.removeEventListener('mouseenter', this.handleMouseEnter);
    this.canvasRef.removeEventListener('mouseleave', this.handleMouseLeave);
    // disable animation
    if (!this.useCustomAnimate) {
      const reqId = requestAnimationFrame(this.animate);
      cancelAnimationFrame(reqId);
    }
    // release resources
    this.getHeightWidth = null;
    this.trackball.dispose();
    this.trackball = null;
    this.cameraPersp = null;
    this.scene = null;
    this.renderer.forceContextLoss();
    this.renderer.domElement = null;
    this.renderer.renderLists.dispose();
    this.renderer.dispose();
    this.renderer = null;
    // window.removeEventListener('keydown', this.handleKeyDown);
  };
}
