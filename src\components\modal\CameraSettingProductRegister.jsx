import { Button, ConfigProvider, Modal } from 'antd';
import React, { useState } from 'react';
import { handleRequestFailed, translation } from '../../common/util';
import _ from 'lodash';
import { ALERT_TYPES, aoiAlert } from '../../common/alert';
import { useRunDetectionPipelineMutation } from '../../services/product';


const CameraSettingProductRegister = (props) => {
  const {
    isOpened,
    setIsOpened,
    productInfo,
  } = props;

  const [registerProduct] = useRunDetectionPipelineMutation();

  const [isLoading, setIsLoading] = useState(false);

  return (
    <Modal
      open={isOpened}
      onCancel={() => {
        if (isLoading) return;
        setIsOpened(false)
      }}
      title={
        <span className='font-source text-[16px] font-semibold'>
          {translation('cameraPreview.productRegister')}
        </span>
      }
      footer={
        <div className='flex flex-col items-start gap-2 self-stretch'>
          <ConfigProvider
            theme={{
              components: {
                Button: {
                  defaultBg: '#57F2C4',
                  defaultColor: '#131313',
                  defaultHoverBg: '#57F2C4',
                  defaultHoverColor: '#131313',
                  defaultHoverBorderColor: '#57F2C4',
                }
              }
            }}
          >
            <Button
              loading={isLoading}
              style={{ width: '100%' }}
              onClick={async () => {
                if (!_.isEmpty(_.get(productInfo, 'inspectables'))) {
                  aoiAlert(translation('notification.error.productHasBeenRegistered'), ALERT_TYPES.COMMON_ERROR);
                  return;
                }

                setIsLoading(true);
                const res = await registerProduct({ product_id: _.get(productInfo, 'product_id'), variant: _.get(productInfo, 'product_name') });
                if (res.error) {
                  handleRequestFailed('registerProduct', res.error);
                  setIsLoading(false);
                  return;
                }
                setIsLoading(false);
                setIsOpened(false);
                window.location.href = `/aoi/edit-product/${_.get(productInfo, 'product_id')}?editStep=1`;
              }}
            >
              <span className='font-source text-[12px] font-normal'>
                {translation('common.continue')}
              </span>
            </Button>
          </ConfigProvider>
        </div>
      }
    >
      <div className='flex py-6'>
        <span className='font-source text-[14px] font-normal'>
          {translation('cameraPreview.registerDesc')}
        </span>
      </div>
    </Modal>
  );
};

export default CameraSettingProductRegister;