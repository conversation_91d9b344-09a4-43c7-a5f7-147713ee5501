import _ from 'lodash';
import * as THREE from 'three';


/**
 * Get object3D from scene by object id
 * @param {THREE.Scene} scene 
 * @param {Number} objectId 
 * @returns {THREE.Object3D | null}
 */
export const getObjectFromScene = (scene, objectId) => {
	for (const obj of scene.children) {
		if (obj.id === objectId) {
			return obj;
		}
	}
	return null;
};

/**
 * Dispose point cloud from scene
 * @param {THREE.Scene} scene 
 * @param {Number} cloudId 
 */
export const disposePointCloud = (scene, cloudId) => {
  const cloud = getObjectFromScene(scene, cloudId);
  if (_.isEmpty(cloud)) return;
  
  scene.remove(cloud);
	// dispose geometry
	if (cloud.geometry) cloud.geometry.dispose();
	// dispose material
	if (cloud.material) {
		if (Array.isArray(cloud.material)) {
			cloud.material.forEach((material) => {
				if (material.map) material.map.dispose();
				material.dispose();
			});
		} else {
			if (cloud.material.map) cloud.material.map.dispose();
			cloud.material.dispose();
		}
	}
};

/**
 * Get pMin and pMax from 2D fabric rect
 * @param {fabric.Rect} rect
 * @param {fabric.Image} scene 
 * @returns 
 */
export const getTwoDRectPminPmax = (rect, strokeWidth) => {
	const pMin = { x: _.round(rect.left + strokeWidth, 0), y: _.round(rect.top + strokeWidth, 0) };
	const pMax = { x: _.round(rect.left + rect.width * rect.scaleX - 1, 0), y: _.round(rect.top + rect.height * rect.scaleY - 1, 0) };

	// console.log('pMin', pMin);
	// console.log('pMax', pMax);
  return { pMin, pMax };
};

/**
 * Generate single sphere point
 * @param {Object} center { x, y, z }
 * @param {Number} radius
 * @param {Number} widthSegments
 * @param {Number} heightSegments
 * @param {Number} color hex color
 */
export const generateSingleSpherePoint = (
	center,
	radius = 5,
	widthSegments = 32,
	heightSegments = 16,
	color = 0xffff00
) => {
	const geo = new THREE.SphereGeometry(radius, widthSegments, heightSegments);
	const material = new THREE.MeshBasicMaterial({ color });
	const mesh = new THREE.Mesh(geo, material);
	mesh.position.set(center.x, center.y, center.z);
	return { sphere: mesh };
};