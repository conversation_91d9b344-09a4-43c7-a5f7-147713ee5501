import { Button, Input, Modal, Select } from 'antd';
import React, { useEffect, useState } from 'react';
import { translation } from '../../common/util';
import { PrimaryButtonConfigProvider } from '../../common/darkModeComponents';


const UpdateBackendHost = (props) => {
  const {
    isOpened,
    updatedRequired,
    setIsOpened,
  } = props;
  
  const [uriPrefix, setUriPrefix] = useState('http://');
  const [newHost, setNewHost] = useState('');

  useEffect(() => {
    const serverEndpoint = localStorage.getItem('serverEndpoint') || 'http://localhost:8000';
    const serverHost =  localStorage.getItem('serverEndpoint') || 'localhost:8000';
    setUriPrefix(serverEndpoint.split('://')[0] + '://');
    setNewHost(serverHost.split('://')[1]);
  }, []);

  return (
    <Modal
      open={isOpened}
      closable={!updatedRequired}
      title={
        <span className='font-source text-[16px] font-semibold'>
          {updatedRequired ? translation('updateBackendHost.currentHostDidNotResponsePleaseUpdate') : translation('updateBackendHost.title')}
        </span>
      }
      onCancel={() => {
        if (updatedRequired) return;
        setIsOpened(false);
      }}
      footer={<div className='flex p-2 items-start gap-2 self-stretch justify-center'>
        <PrimaryButtonConfigProvider>
          <Button
            style={{ width: '100%' }}
            onClick={() => {
              localStorage.setItem('serverEndpoint', `${uriPrefix}${newHost}`);
              localStorage.setItem('serverHost', newHost);
              window.location.reload();
            }}
          >
            <span className='font-source text-[12px] font-normal'>
              {translation('updateBackendHost.saveAndRefresh')}
            </span>
          </Button>
        </PrimaryButtonConfigProvider>
      </div>}
    >
      <div className='flex flex-col items-start gap-8 self-stretch px-4 py-6'>
        <div className='flex items-center gap-2 self-stretch'>
          <span className='font-source text-[14px] font-normal whitespace-nowrap'>
            {translation('updateBackendHost.currentHost')}
          </span>
          <div className='flex gap-1 items-center w-full'>
            <Select
              size='small'
              options={[
                {
                  label: 'http://',
                  value: 'http://',
                },
                {
                  label: 'https://',
                  value: 'https://',
                }
              ]}
              value={uriPrefix}
              popupMatchSelectWidth={false}
              onChange={(value) => setUriPrefix(value)}
              style={{ width: '120px' }}
            />
            <Input
              size='small'
              value={newHost}
              onChange={(e) => setNewHost(e.target.value)}
              style={{ width: '100%' }}
            />
          </div>
        </div>
      </div>
    </Modal>
  )
};

export default UpdateBackendHost;