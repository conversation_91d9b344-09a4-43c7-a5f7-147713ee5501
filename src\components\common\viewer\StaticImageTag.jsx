import _ from 'lodash';
import React, { useEffect, useState } from 'react';
import { serverEndpoint } from '../../../common/const';


const StaticImageTag = (props) => {
  const {
    dataUri,
    className,
    alt,
    style,
  } = props;

  const [imageSrc, setImageSrc] = useState('');

  useEffect(() => {
    if (_.isEmpty(dataUri)) return;

    const fetchData = async () => {
      const res = await fetch(`${serverEndpoint}/data?data_uri=${dataUri}`);
      const blob = await res.blob();
      const reader = new FileReader();
      const src = await new Promise((resolve) => {
        reader.onload = () => resolve(reader.result);
        reader.readAsDataURL(blob);
      });
      setImageSrc(src);
    };

    fetchData(dataUri);
  }, [dataUri]);

  return (
    <img
      src={imageSrc}
      className={className}
      alt={alt}
      style={style}
    />
  );
};

export default StaticImageTag;