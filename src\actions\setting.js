import { createSlice } from '@reduxjs/toolkit';
import moment from 'moment';
import { maxFisMessageCount } from '../common/const';


const initialState = {
  isMainMenuOpened: true,
  isContainerWindowLoadingLocked: false, // remember to unlock when action is failed or done
  isContainerTransparentLockEnabled: false, // remember to unlock when action is failed or done
  isStayAtLeatestEnabled: false,
  isInferenceRunning: false,
  productVariationDefineModalDisabled: false,
  productPassRates: 95,
  viewInspectionViewMode: 'single',
  allowedNGAmount: 1,
  curRunningIpcSessionIds: null,
  isGoldenComponentChecklistOpened: false,
  isTrainingRuning: false,
  curTrainingTaskStartTime: null,
  isComponentFilterVisible: false,
  todayIpcTotalCount: 0,
  todayIpcGoodCount: 0,
  todayTotalManualChangeOffset: 0,
  todayGoodManualChangeOffset: 0,
  // init reset at 00:00 and 2 days ago
  dailyIpcCountResetTime: moment().startOf('day').subtract(2, 'days').toDate().getTime(),
  // init to today
  prevResetIpcCountDate: new Date(),
  latestRetrainFinishTimeByModelType: {}, // model type to timestamp
  isInspectionErrorQueueOpened: false,
  inspectionErrorQueue: [],
  isViewLiveFullScreenEnabled: false,
  userType: 'operator',
  inferenceStatusResponse: null,
};

const setting = createSlice({
  name: 'setting',
  initialState,
  reducers: {
    setMainMenuOpened(state, action) {
      state.isMainMenuOpened = action.payload;
    },
    setContainerWindowLoadingLocked(state, action) {
      state.isContainerWindowLoadingLocked = action.payload;
    },
    setStayAtLeatestEnabled(state, action) {
      state.isStayAtLeatestEnabled = action.payload;
    },
    setIsInferenceRunning(state, action) {
      state.isInferenceRunning = action.payload;
    },
    setProductVariationDefineModalDisabled(state, action) {
      state.productVariationDefineModalDisabled = action.payload;
    },
    setProductPassRates(state, action) {
      state.productPassRates = action.payload;
    },
    setViewInspectionViewMode(state, action) {
      state.viewInspectionViewMode = action.payload;
    },
    setContainerTransparentLockEnabled(state, action) {
      state.isContainerTransparentLockEnabled = action.payload;
    },
    setAllowedNGAmount(state, action) {
      state.allowedNGAmount = action.payload;
    },
    setCurRunningIpcSessionIds(state, action) {
      state.curRunningIpcSessionIds = action.payload;
    },
    setIsGoldenComponentChecklistOpened(state, action) {
      state.isGoldenComponentChecklistOpened = action.payload;
    },
    setIsTrainingRuning(state, action) {
      state.isTrainingRuning = action.payload;
    },
    setCurTrainingTaskStartTime(state, action) {
      state.curTrainingTaskStartTime = action.payload;
    },
    setIsComponentFilterVisible(state, action) {
      state.isComponentFilterVisible = action.payload;
    },
    setTodayIpcTotalCount(state, action) {
      state.todayIpcTotalCount = action.payload;
    },
    setTodayIpcGoodCount(state, action) {
      state.todayIpcGoodCount = action.payload;
    },
    setTodayTotalManualChangeOffset(state, action) {
      state.todayTotalManualChangeOffset = action.payload;
    },
    setTodayGoodManualChangeOffset(state, action) {
      state.todayGoodManualChangeOffset = action.payload;
    },
    setDailyIpcCountResetTime(state, action) {
      state.dailyIpcCountResetTime = action.payload;
    },
    setPrevResetIpcCountDate(state, action) {
      state.prevResetIpcCountDate = action.payload;
    },
    setLatestRetrainFinishTimeByModelType(state, action) {
      state.latestRetrainFinishTimeByModelType = action.payload;
    },
    setIsInspectionErrorQueueOpened(state, action) {
      state.isInspectionErrorQueueOpened = action.payload;
    },
    pushMsgToInspectionErrorQueue(state, action) {
      const newMessages = action.payload;
      const currentMessages = state.inspectionErrorQueue;
      const combinedMessages = [...newMessages, ...currentMessages];

      // Keep only the latest maxFisMessageCount messages
      if (combinedMessages.length > maxFisMessageCount) {
        state.inspectionErrorQueue = combinedMessages.slice(0, maxFisMessageCount);
      } else {
        state.inspectionErrorQueue = combinedMessages;
      }
    },
    setInspectionErrorQueue(state, action) {
      state.inspectionErrorQueue = action.payload;
    },
    setIsViewLiveFullScreenEnabled(state, action) {
      state.isViewLiveFullScreenEnabled = action.payload;
    },
    setUserType(state, action) {
      state.userType = action.payload;
    },
    setInferenceStatusResponse(state, action) {
      state.inferenceStatusResponse = action.payload;
    },
  },
});

export const {
  setMainMenuOpened,
  setContainerWindowLoadingLocked,
  setStayAtLeatestEnabled,
  setIsInferenceRunning,
  setProductVariationDefineModalDisabled,
  setProductPassRates,
  setViewInspectionViewMode,
  setContainerTransparentLockEnabled,
  setAllowedNGAmount,
  setCurRunningIpcSessionIds,
  setIsGoldenComponentChecklistOpened,
  setIsTrainingRuning,
  setCurTrainingTaskStartTime,
  setIsComponentFilterVisible,
  setTodayIpcTotalCount,
  setTodayIpcGoodCount,
  setTodayTotalManualChangeOffset,
  setTodayGoodManualChangeOffset,
  setDailyIpcCountResetTime,
  setPrevResetIpcCountDate,
  setLatestRetrainFinishTimeByModelType,
  setIsInspectionErrorQueueOpened,
  pushMsgToInspectionErrorQueue,
  setInspectionErrorQueue,
  setIsViewLiveFullScreenEnabled,
  setUserType,
  setInferenceStatusResponse,
} = setting.actions;
export default setting.reducer;