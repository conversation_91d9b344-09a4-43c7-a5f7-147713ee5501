import { Button } from 'antd';
import _ from 'lodash';
import React, { Fragment, useEffect, useRef, useState } from 'react';
import { ALERT_TYPES, aoiAlert } from '../../common/alert';
import { feedbackMaskRequiredAgent } from '../../common/const';
import { getColorByStr, handleRequestFailed, sleep, translation } from '../../common/util';
import { useAnnotateFeatureMutation } from '../../services/session';
import InferenceDetailScene from '../Scene/InferenceDetail';
import { useSelector } from 'react-redux';


const InferenceAnnotation = (props) => {
  const {
    selectedFeatureId,
    setSelectedFeatureId,
    selectedLineItem,
    dataUri,
    step,
    ipcProductId,
    goldenProductId,
    sessionStepInfo,
    setSelectedLineItem,
    fetchFeatures,
    isDrawMaskEnabled,
    setIsDrawMaskEnabled,
    curDisplayOptionsBrightness,
    setCurDisplayOptionsBrightness,
    curDisplayOptionsContrast,
    setCurDisplayOptionsContrast,
    curDisplayOptionsSaturation,
    setCurDisplayOptionsSaturation,
    isSharpnessEnabled,
    setIsSharpnessEnabled,
    goldenProdFeatures,
    curSession,
    curIpc,
    drawMaskViewerRef,
    goldenProductInfo,
    redirectToFullView,
    viewInspectionViewMode,
    isLive,
    isGoldenComponentChecklistOpened,
    isFullScreen,
  } = props;

  // const [annotateFeature] = useAnnotateFeatureMutation();
  // const { data: features } = useGetAllFeaturesByProductIdAndStepQuery({ product_id: goldenProductId, step });

  const [selectedFeature, setSelectedFeature] = useState(null);
  const [featureTypeColor, setFeatureTypeColor] = useState('#000000');
  const [curSessionStepInfo, setCurSessionStepInfo] = useState(null);

  const predictionSectionRef = useRef(null);
  const canvasRef = useRef();
  const viewerRef = useRef();
  // const drawMaskViewerRef = useRef();
  const drawMaskCanvasRef = useRef();
  const prevSelectedFeatureId = useRef(null);
  const prevSelectedLineItem = useRef(null);
  const viewInspectionViewModeRef = useRef(null);
  const isGoldenComponentChecklistOpenedRef = useRef(null);

  const isComponentFilterVisible = useSelector((state) => state.setting.isComponentFilterVisible);

  useEffect(() => {
    const f = _.find(goldenProdFeatures, (f) => String(f.feature_id) === String(selectedFeatureId));
    setSelectedFeature(f);
    setFeatureTypeColor(getColorByStr(_.get(f, 'feature_type', '')));
    const curSessionStepInfo = _.find(sessionStepInfo, (item) => item.feature_id === selectedFeatureId && item.detail === _.split(selectedLineItem, '-')[1]);
    setCurSessionStepInfo(curSessionStepInfo);
  }, [step, goldenProdFeatures, selectedFeatureId, sessionStepInfo, selectedLineItem]);

  useEffect(() => {
    if (isFullScreen) return;

    const handleFeatureIdChange = async (selectedFeatureId) => {
      if (!viewerRef.current || !canvasRef.current || !drawMaskViewerRef.current) return;
      const { width, height } = viewerRef.current.getSceneSize();
      const { width: maskWidth, height: maskHeight } = drawMaskViewerRef.current.getSceneSize();
      if (_.isNull(selectedFeatureId) && !_.isNull(prevSelectedFeatureId.current)) {
        viewerRef.current.updateSceneSize(width + 458 + 8, height);
        drawMaskViewerRef.current.updateSceneSize(maskWidth + 458 + 8, maskHeight);
      } else if (!_.isNull(selectedFeatureId) && _.isNull(prevSelectedFeatureId.current)) {
        viewerRef.current.updateSceneSize(width - 458 - 8, height);
        drawMaskViewerRef.current.updateSceneSize(maskWidth - 458 - 8, maskHeight);
      }
      prevSelectedFeatureId.current = selectedFeatureId;
    };
    handleFeatureIdChange(selectedFeatureId);
  }, [selectedFeatureId]);

  useEffect(() => {
    if (!isLive || isFullScreen) return;

    const handleInspectionViewModeChange = async (viewInspectionViewMode, isComponentFilterVisible) => {
      if (!viewerRef.current || !canvasRef.current || !drawMaskViewerRef.current) return;
      if (_.isNull(viewInspectionViewModeRef.current)) {
        viewInspectionViewModeRef.current = viewInspectionViewMode;
        return;
      }
      const { width, height } = viewerRef.current.getSceneSize();
      const { width: maskWidth, height: maskHeight } = drawMaskViewerRef.current.getSceneSize();
      // if (viewInspectionViewModeRef.current === 'review' && viewInspectionViewMode === 'single') {
      //   viewerRef.current.updateSceneSize(width + 280, height);
      //   drawMaskViewerRef.current.updateSceneSize(maskWidth + 280, maskHeight);
      // } else if (viewInspectionViewModeRef.current === 'single' && viewInspectionViewMode === 'review') {
      //   viewerRef.current.updateSceneSize(width - 280, height);
      //   drawMaskViewerRef.current.updateSceneSize(maskWidth - 280, maskHeight);
      // }
      // if (viewInspectionViewMode === 'review' && isComponentFilterVisible) {
      //   viewerRef.current.updateSceneSize(width - 280, height);
      //   drawMaskViewerRef.current.updateSceneSize(maskWidth - 280, maskHeight);
      // } else {
      //   viewerRef.current.updateSceneSize(width + 280, height);
      //   drawMaskViewerRef.current.updateSceneSize(maskWidth + 280, maskHeight);
      // }
      viewInspectionViewModeRef.current = viewInspectionViewMode;
    };

    handleInspectionViewModeChange(viewInspectionViewMode, isComponentFilterVisible);
  }, [viewInspectionViewMode, isComponentFilterVisible]);

  useEffect(() => {
    if (!isLive || isFullScreen) return;

    const handleGoldenComponentChecklistOpenedChange = async (isGoldenComponentChecklistOpened) => {
      if (!viewerRef.current || !canvasRef.current || !drawMaskViewerRef.current) return;
      if (_.isNull(isGoldenComponentChecklistOpenedRef.current)) {
        isGoldenComponentChecklistOpenedRef.current = isGoldenComponentChecklistOpened;
        return;
      }

      const { width, height } = viewerRef.current.getSceneSize();
      const { width: maskWidth, height: maskHeight } = drawMaskViewerRef.current.getSceneSize();
      if (isGoldenComponentChecklistOpenedRef.current && !isGoldenComponentChecklistOpened) {
        viewerRef.current.updateSceneSize(width + 368 + 8, height);
        drawMaskViewerRef.current.updateSceneSize(maskWidth + 368 + 8, maskHeight);
      } else if (!isGoldenComponentChecklistOpenedRef.current && isGoldenComponentChecklistOpened) {
        viewerRef.current.updateSceneSize(width - 368 - 8, height);
        drawMaskViewerRef.current.updateSceneSize(maskWidth - 368 - 8, maskHeight);
      }
      isGoldenComponentChecklistOpenedRef.current = isGoldenComponentChecklistOpened
    };

    handleGoldenComponentChecklistOpenedChange(isGoldenComponentChecklistOpened);
  }, [isGoldenComponentChecklistOpened]);

  // useEffect(() => {
  //   const handleLineItemChange = async (selectedLineItem) => {
  //     if (!viewerRef.current || !canvasRef.current || !drawMaskViewerRef.current) return;
  //     await sleep(300); // ensure the dom has updated
  //     const { width, height } = viewerRef.current.getSceneSize();
  //     const { width: maskWidth, height: maskHeight } = drawMaskViewerRef.current.getSceneSize();
  //     if (_.isEmpty(selectedLineItem) && !_.isEmpty(prevSelectedLineItem.current)) {
  //       viewerRef.current.updateSceneSize(width + 204 , height);
  //       drawMaskViewerRef.current.updateSceneSize(maskWidth + 204, maskHeight);
  //     } else if (!_.isEmpty(selectedLineItem) && _.isEmpty(prevSelectedLineItem.current)) {
  //       viewerRef.current.updateSceneSize(width - 204, height);
  //       drawMaskViewerRef.current.updateSceneSize(maskWidth - 204, maskHeight);
  //     }
  //     prevSelectedLineItem.current = selectedLineItem;
  //   };
  //   handleLineItemChange(selectedLineItem);
  // }, [selectedLineItem]);

  return (
    <Fragment>
      {/* prediction & feedback */}
      {/* 20241029 prediction & feedback has been moved to reference comparison */}
      {/* {!_.isEmpty(selectedLineItem) &&
      <div className='flex w-[242px] py-2 px-3 flex-col gap-1 items-start self-stretch bg-[#131313]'>
        {!_.isNull(selectedFeatureId) && !_.isEmpty(selectedLineItem) && _.find(sessionStepInfo, (item) => item.feature_id === selectedFeatureId && item.detail === _.split(selectedLineItem, '-')[1]) && (
          <Fragment>
            <div className='relative w-full' ref={predictionSectionRef}>
              <div className={`relative w-full ${!isDrawMaskEnabled ? 'z-[20]' : 'z-[2]'}`}>
                <div
                  className='flex py-2 flex-col justify-center items-start gap-1 self-stretch'
                >
                  <span className='font-source text-[14px] font-semibold'>
                    {translation('viewInspection.AIPrediction')}
                  </span>
                  <div className='w-full h-[1px] bg-[#333]' />
                  <div className='flex py-2 flex-col items-start gap-1 self-stretch'>
                    <div
                      className='flex items-center gap-1 self-stretch py-1 px-2 rounded-[4px]'
                      style={{ background: 'rgba(255, 255, 255, 0.12)' }}
                    >
                      <div className='h-[14px] w-[14px] rounded-[50%]' style={{ backgroundColor: featureTypeColor }} />
                      <span className='font-source text-[14px] font-normal'>
                        {_.get(selectedFeature, 'feature_scope') === 'global' ? _.get(selectedFeature, 'feature_type') : _.get(selectedFeature, 'feature_type').substring(1)}
                      </span>
                    </div>
                    <div className='flex items-center gap-2 self-stretch py-0.5 px-4'>
                      <div className='flex w-[14px] h-[14px] justify-centet items-start'>
                        <img className='w-[9px] h-2 shrink' src='/img/icn/icn_turn_gray.svg' alt='turn' />
                      </div>
                      <div
                        className='h-[14px] w-[14px] rounded-[50%]'
                        style={{ backgroundColor: _.find(sessionStepInfo, (item) => item.feature_id === selectedFeatureId && item.detail === _.split(selectedLineItem, '-')[1]).pass ? '#57F2C4' : '#EB5757' }}
                      />
                      <span className='font-source text-[12px] font-normal'>
                        {!curSessionStepInfo?.pass ?
                          // translation(`viewInspection.lineItemDefectDisplay.${_.get(selectedFeature, 'feature_type')}.${_.get(curSessionStepInfo, 'detail')}`) :
                          translation('viewInspection.lineItemBadResult') :
                          translation('viewInspection.lineItemGoodResult')
                        }
                      </span>
                    </div>
                  </div>
                </div>
                <div className='flex flex-col gap-1 py-2 items-start justify-center self-stretch'>
                  <span className='font-source text-[14px] font-semibold'>
                    {translation('viewInspection.userFeedback')}
                  </span>
                  <div className='flex p-1 flex-col gap-1 items-start self-stretch rounded-[4px] border-gray-1 border-[1px]'>
                    <div
                      className='flex py-1.5 px-1 items-center gap-1 self-stretch cursor-pointer justify-between'
                      onClick={async () => {
                        if (_.isEmpty(selectedFeature)) return;
                        const curSessionStepInfo = _.find(sessionStepInfo, (item) => item.feature_id === selectedFeatureId && item.detail === _.split(selectedLineItem, '-')[1]);
                        // if (_.get(curSessionStepInfo, 'pass')) {
                        //   aoiAlert(translation('notification.warning.aiPredictionHasAGoodResult'), ALERT_TYPES.COMMON_WARNING);
                        //   return;
                        // }
                        if ((_.get(curSessionStepInfo, 'feedback.correct') === true && _.get(curSessionStepInfo, 'pass')) || (_.get(curSessionStepInfo, 'feedback.correct') === false && !_.get(curSessionStepInfo, 'pass'))) {
                          aoiAlert(translation('notification.warning.correctFeedbackProvided'), ALERT_TYPES.COMMON_WARNING);
                          return;
                        }
                        setIsDrawMaskEnabled(false);
                        const res = await annotateFeature({
                          variant: _.get(selectedFeature, 'variant'),
                          product_id: Number(ipcProductId),
                          step: Number(step),
                          feature_id: Number(selectedFeatureId),
                          line_item_name: _.get(curSessionStepInfo, 'detail'),
                          is_inference_correct: _.get(curSessionStepInfo, 'pass') === true,
                        });
                        if (_.get(res, 'error')) {
                          handleRequestFailed('annotateFeature', _.get(res, 'error'));
                          return;
                        }
                        aoiAlert(translation('notification.success.inferenceAnnotation'), ALERT_TYPES.COMMON_SUCCESS);
                        fetchFeatures();
                      }}
                    >
                      <div className='flex items-center gap-1 self-stretch'>
                        <img src='/img/icn/icn_circleFilled_green.svg' alt='circle' />
                        <span className='font-source text-[12px] font-normal'>
                          {translation(`viewInspection.annotateFeedbackOptions.${_.get(curSessionStepInfo, 'detail')}.good`)}
                        </span>
                      </div>
                      {!_.isEmpty(_.get(curSessionStepInfo, 'feedback')) && (_.get(curSessionStepInfo, 'feedback.correct') ? _.get(curSessionStepInfo, 'pass') : !_.get(curSessionStepInfo, 'pass')) &&
                        <span className='font-source text-[12px] font-normal text-[#BDBDBD] italic'>
                          {translation('viewInspection.selected')}
                        </span>
                      }
                    </div>
                    <div
                      className='flex py-1.5 px-1 items-center gap-1 self-stretch cursor-pointer justify-between'
                      onClick={async () => {
                        if (_.isEmpty(selectedFeature)) return;
                        const curSessionStepInfo = _.find(sessionStepInfo, (item) => item.feature_id === selectedFeatureId && item.detail === _.split(selectedLineItem, '-')[1]);
                        if ((_.get(curSessionStepInfo, 'feedback.correct') === false && _.get(curSessionStepInfo, 'pass')) || (_.get(curSessionStepInfo, 'feedback.correct') === true && !_.get(curSessionStepInfo, 'pass'))) {
                          aoiAlert(translation('notification.warning.defectFeedbackProvided'), ALERT_TYPES.COMMON_WARNING);
                          return;
                        }
                        if (_.includes(feedbackMaskRequiredAgent, _.get(curSessionStepInfo, 'detail'))) {
                          aoiAlert(translation('notification.info.maskReqiredForFeedback'), ALERT_TYPES.COMMON_INFO);
                          setIsDrawMaskEnabled(true);
                          return;
                        }
                        const res = await annotateFeature({
                          variant: _.get(selectedFeature, 'variant'),
                          product_id: Number(ipcProductId),
                          step: Number(step),
                          feature_id: Number(selectedFeatureId),
                          line_item_name: _.get(curSessionStepInfo, 'detail'),
                          is_inference_correct: _.get(curSessionStepInfo, 'pass') === false,
                        });
                        if (_.get(res, 'error')) {
                          handleRequestFailed('annotateFeature', _.get(res, 'error'));
                          return;
                        }
                        fetchFeatures();
                        aoiAlert(translation('notification.success.inferenceAnnotation'), ALERT_TYPES.COMMON_SUCCESS);
                      }}
                    >
                      <div className='flex items-center gap-1 self-stretch'>
                        <img src='/img/icn/icn_circleFilled_red.svg' alt='circle' />
                        <span className='font-source text-[12px] font-normal'>
                          {translation(`viewInspection.annotateFeedbackOptions.${_.get(curSessionStepInfo, 'detail')}.notGood`)}
                        </span>
                      </div>
                      {!_.isEmpty(_.get(curSessionStepInfo, 'feedback')) && (_.get(curSessionStepInfo, 'feedback.correct') ? !_.get(curSessionStepInfo, 'pass') : _.get(curSessionStepInfo, 'pass')) &&
                        <span className='font-source text-[12px] font-normal text-[#BDBDBD] italic'>
                          {translation('viewInspection.selected')}
                        </span>
                      }
                    </div>
                  </div>
                </div>
              </div>
            </div>
            {_.includes(feedbackMaskRequiredAgent, _.get(_.split(selectedLineItem, '-'), '1')) && (
              <div className='flex flex-col gap-2 items-start self-stretch'>
                {!_.isEmpty(_.get(curSessionStepInfo, 'feedback')) && !_.isEmpty(_.get(curSessionStepInfo, 'feedback.defect_mask_uri')) && 
                  <div className='flex items-center gap-2 self-stretch'>
                    <span className='font-source text-[12px] font-normal whitespace-nowrap'>
                      {translation('viewInspection.editPrevMask')}
                    </span>
                    <Button
                      size='small'
                      disabled={isDrawMaskEnabled}
                      style={{ width: '100%' }}
                      onClick={() => {
                        setIsDrawMaskEnabled(true);
                      }}
                    >
                      <div className='flex items-center gap-1'>
                        <img src='/img/icn/icn_pencil_blue.svg' alt='pencil' className='w-3 h-3' />
                        <span className='font-source text-[12px] font-normal text-AOI-blue'>
                          {translation('viewInspection.edit')}
                        </span>
                      </div>
                    </Button>
                  </div>
                }
                {isDrawMaskEnabled &&
                  <div className='flex items-center gap-2 self-stretch flex-col'>
                    <Button
                      size='small'
                      style={{ width: '100%' }}
                      onClick={async () => {
                        if (_.isEmpty(selectedFeature)) return;
                        const curSessionStepInfo = _.find(sessionStepInfo, (item) => item.feature_id === selectedFeatureId && item.detail === _.split(selectedLineItem, '-')[1]);
                        if (drawMaskViewerRef.current) {
                          const drawnMaskBase64 = drawMaskViewerRef.current.outputDrawnMaskAsBase64();
                          if (_.isEmpty(drawnMaskBase64)) {
                            aoiAlert(translation('notification.warning.emptyMask'), ALERT_TYPES.COMMON_WARNING);
                            return;
                          }
                          // console.log('drawnMaskBase64', drawnMaskBase64);
                          // console.log('height', _.get(selectedFeature, 'roi.points.1.y') - _.get(selectedFeature, 'roi.points.0.y'),);
                          // console.log('width', _.get(selectedFeature, 'roi.points.1.x') - _.get(selectedFeature, 'roi.points.0.x'),);
                      
                          const res = await annotateFeature({
                            variant: _.get(selectedFeature, 'variant'),
                            product_id: Number(ipcProductId),
                            step: Number(step),
                            feature_id: Number(selectedFeatureId),
                            is_inference_correct: _.get(curSessionStepInfo, 'pass') === false,
                            line_item_name: _.get(curSessionStepInfo, 'detail'),
                            mask_data: drawnMaskBase64,
                            mask_height: _.get(selectedFeature, 'roi.points.1.y') - _.get(selectedFeature, 'roi.points.0.y'),
                            mask_width: _.get(selectedFeature, 'roi.points.1.x') - _.get(selectedFeature, 'roi.points.0.x'),
                          });
                          if (_.get(res, 'error')) {
                            handleRequestFailed('annotateFeature', _.get(res, 'error'));
                            return;
                          }
                          aoiAlert(translation('notification.success.inferenceAnnotation'), ALERT_TYPES.COMMON_SUCCESS);
                        }
                        setIsDrawMaskEnabled(false);
                        fetchFeatures();
                      }}
                      type='primary'
                    >
                      <span className='font-source text-[12px] font-semibold text-gray-1'>
                        {translation('viewInspection.submitAsFeedback')}
                      </span>
                    </Button>
                    <Button
                      size='small'
                      style={{ width: '100%' }}
                      onClick={() => {
                        setIsDrawMaskEnabled(false);
                      }}
                    >
                      <span className='font-source text-[12px] font-semibold'>
                        {translation('common.cancel')}
                      </span>
                    </Button>
                  </div>
                }
              </div>
            )}
          </Fragment>
        )}
      </div>
      } */}
      <div
        className={`flex flex-col items-start ${isFullScreen ? 'w-full h-full' : 'flex-1 p-2 gap-2'} self-stretch rounded-[2px]`}
        style={{ background: 'rgba(255, 255, 255, 0.10)' }}
      >
        {!isFullScreen &&
          <div className='flex items-center justify-between w-full'>
            <div className='flex items-center gap-2 self-stretch'>
              {/* <img src='/img/icn/icn_customGridLayout_white.svg' alt='grid' className='w-[16px] h-[11px] fill-[#fff]' /> */}
              <span className='font-source text-[12px] font-normal'>
                {/* {translation('viewBoards.fullProduct', { productName: _.get(productInfo, 'product_name') })} */}
                {_.get(goldenProductInfo, 'product_name')}
              </span>
            </div>
            {!isFullScreen &&
              <div className='flex justify-center items-center gap-[3px]'>
                <div
                  className={`duration-300 hover:ease-in hover:bg-gray-1 cursor-pointer flex w-[32px] h-[32px] py-1 px-0.5 flex-col justify-center items-center rounded-[2px] gap-2.5 bg-gray-1`}
                >
                  <img src='/img/icn/icn_carouselLayout_white.svg' className='w-[16.8px] h-[14px] fill-white' alt='carousel' />
                </div>
                <div
                  className={`duration-300 hover:ease-in hover:bg-gray-1 cursor-pointer flex w-[32px] h-[32px] py-1 px-0.5 flex-col justify-center items-center rounded-[2px] gap-2.5`}
                  onClick={() => redirectToFullView()}
                >
                  <img src='/img/icn/icn_gridLayout_white.svg' className='w-[14px] h-[14px] fill-white' alt='grid' />
                </div>
              </div>
            }
          </div>
        }
        {/* scene */}
        <InferenceDetailScene
          dataUri={dataUri}
          step={step}
          ipcProductId={ipcProductId}
          goldenProductId={goldenProductId}
          sessionStepInfo={sessionStepInfo}
          setSelectedFeatureId={setSelectedFeatureId}
          selectedFeatureId={selectedFeatureId}
          setSelectedLineItem={setSelectedLineItem}
          isDrawMaskEnabled={isDrawMaskEnabled}
          canvasRef={canvasRef}
          viewerRef={viewerRef}
          drawMaskViewerRef={drawMaskViewerRef}
          drawMaskCanvasRef={drawMaskCanvasRef}
          curSessionStepInfo={curSessionStepInfo}
          curDisplayOptionsBrightness={curDisplayOptionsBrightness}
          setCurDisplayOptionsBrightness={setCurDisplayOptionsBrightness}
          curDisplayOptionsContrast={curDisplayOptionsContrast}
          setCurDisplayOptionsContrast={setCurDisplayOptionsContrast}
          curDisplayOptionsSaturation={curDisplayOptionsSaturation}
          setCurDisplayOptionsSaturation={setCurDisplayOptionsSaturation}
          isSharpnessEnabled={isSharpnessEnabled}
          setIsSharpnessEnabled={setIsSharpnessEnabled}
          goldenProdFeatures={goldenProdFeatures}
          curSession={curSession}
          curIpc={curIpc}
          isFullScreen={isFullScreen}
          selectedLineItem={selectedLineItem}
          isLive={isLive}
        />
      </div>
    </Fragment>
  );
};

export default InferenceAnnotation;