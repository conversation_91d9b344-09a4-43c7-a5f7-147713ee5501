import { LoadingOutlined } from '@ant-design/icons';
import { Spin, Tooltip } from 'antd';
import _ from 'lodash';
import React, { Fragment, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import styled from 'styled-components';
import { serverEndpoint } from '../../common/const';
import { backendAutoGenTimeToDisplayString, backendTimestampToDisplayString, translation } from '../../common/util';
import { systemApi } from '../../services/system';
import MultiViewGridSelection from '../common/MultiViewGridSelection';


const LiveInspectionCard = (props) => {
  const {
    inspection,
    handleRedirect,
    goldenProductName,
  } = props;

  const { data: systemMetadata } = useSelector((state) => systemApi.endpoints.getSystemMetadata.select()(state));

  useEffect(() => {
    return () => {
      // console.log('component unmounted');
    };
  }, []);

  return (
    <Container
      className='flex p-2 justify-between items-center self-stretch rounded-[4px] border-gray-1 cursor-pointer h-[136px] border-[1px]'
      onClick={() => handleRedirect(`/aoi/view-inference/${_.get(inspection, 'golden_product_id')}/${_.get(inspection, 'product_id')}?step=0`)}
    >
      <div className='flex items-center gap-8 flex-1 self-stretch'>
        { _.get(inspection, 'status') !== 0 ? (
          // finished
          <div className='flex items-center gap-2.5 self-stretch'>
            <div
              className='flex items-center gap-1 self-stretch py-6 px-4 rounded-[2px]'
              style={{ background: 'linear-gradient(0deg, #57F2C4 0%, #57F2C4 100%), var(--AOI-green, #81F499)' }}
            >
              <div className='flex w-3 h-3 justify-center items-center gap-2.5'>
                <img src='/img/icn/icn_checkerFlag_black.svg' className='w-[8.8px] h-[10px] shrink' alt='icn_checkerFlag_black' />
              </div>
            </div>
          </div>
        ) : (
          // in progress
          <div className='flex items-center gap-2.5 self-stretch'>
            <div className='flex items-center gap-1 self-stretch py-6 px-4 rounded-[2px] bg-AOI-blue'>
              <Spin indicator={
                <LoadingOutlined style={{ fontSize: 12, color: '#333' }} spin />
              } />
            </div>
          </div>
        )}
        <div className='flex items-center gap-2'>
          <div className='flex p-1 flex-col items-center justify-center gap-1 rounded-[2px] bg-[#131313]'>
            <MultiViewGridSelection
              layout={_.get(systemMetadata, 'inspection_view_layout')}
              selectedViewId={null}
              onSelectView={null}
              defectSteps={ _.get(inspection, 'status') !== 0 ? _.get(inspection, 'failed_count_map') : null}
              rowHeight={80}
            />
            <span className='font-source text-[10px] font-normal'>
              {_.get(inspection, 'product_name' || 'unknown')}
            </span>
          </div>
          {/* {_.get(inspection, 'defect_count') > 0 && _.isEmpty(defectsMap) && (
            <div className='flex items-center gap-2 self-stretch'>
              <span className='font-source text-[12px] font-normal'>
                {translation('liveDashboard.loadingDefectsThumbnail')}
              </span>
              <Spin />
            </div>
          )} */}
          {_.map(_.take(_.get(inspection, 'defects', []), 4), (d) => {
            return <SingleCroppedThumbnail d={d} />;
          })}
          {_.get(inspection, 'defect_count') > 4 && (
            <div className='flex w-[82px] h-[109px] py-1 px-6 justify-center items-center gap-1 rounded-[4px] bg-gray-1'>
              <span className='font-source text-[12px] font-normal whitespace-nowrap'>
                {translation('liveDashboard.more', { count: _.get(inspection, 'defect_count') - 4 })}
              </span>
            </div>
          )}
        </div>
      </div>
      <div className='flex w-[445px] py-2 flex-col items-start gap-1 rounded-[4px] border-gray-2 border-[1px]'>
        <div className='flex px-2 items-center gap-2 self-stretch'>
          <div className='flex w-[102px] h-[15px] items-center gap-2'>
            <div className='flex flex-col justify-center items-center gap-2.5'>
              <span className='font-source text-[12px] font-semibold'>
                {translation('liveDashboard.productName')}
              </span>
            </div>
          </div>
          <div className='flex items-center gap-1'>
            <span className='font-source text-[12px] font-normal'>
              {goldenProductName}
            </span>
          </div>
        </div>
        <div className='flex px-2 items-center gap-2 self-stretch'>
          <div className='flex w-[102px] h-[15px] items-center gap-2'>
            <div className='flex flex-col justify-center items-center gap-2.5'>
              <span className='font-source text-[12px] font-semibold'>
                {translation('liveDashboard.date')}
              </span>
            </div>
          </div>
          <div className='flex items-center gap-1'>
            <span className='font-source text-[12px] font-normal'>
              {backendAutoGenTimeToDisplayString(_.get(inspection, 'timestamp'))}
            </span>
          </div>
        </div>
        <div className='flex px-2 items-center gap-2 self-stretch'>
          <div className='flex w-[102px] h-[15px] items-center gap-2'>
            <div className='flex flex-col justify-center items-center gap-2.5'>
              <span className='font-source text-[12px] font-semibold'>
                {translation('liveDashboard.defects')}
              </span>
            </div>
          </div>
          <div className='flex items-center content-center gap-1 flex-1 flex-wrap'>
            {!_.isEmpty(_.get(inspection, 'defect_type_map')) ? (
              <Fragment>
                <span className='font-source text-[12px] font-normal'>
                  <div className='flex w-3 h-3 flex-col items-center justify-center'>
                    <img src='/img/icn/icn_warning_red.svg' className='w-3 h-[10.5px] shrink' alt='icn_warning_red' />
                  </div>
                </span>
                {_.map(_.keys(_.get(inspection, 'defect_type_map')), (defectType, index) => {
                  if (index === 2) {
                    return <Fragment>
                      <span className='font-source text-[12px] font-normal'>
                        |
                      </span>
                      <span className='font-source text-[12px] font-normal text-gray-3 italic'>
                        {translation('liveDashboard.more', { count: _.keys(_.get(inspection, 'defect_type_map')).length - 2 })}
                      </span>
                    </Fragment>
                  }
                  if (index === 0) {
                    return <Fragment>
                      <span className='font-source text-[12px] font-normal'>
                        {`${translation(`viewInspection.lineItem.${defectType}`)} (${_.get(inspection, `defect_type_map.${defectType}`)})`}
                      </span>
                    </Fragment>
                  }
                  if (index === 1) {
                    return <Fragment>
                      <span className='font-source text-[12px] font-normal'>
                      <span className='font-source text-[12px] font-normal'>
                        |
                      </span>
                        {`${translation(`viewInspection.lineItem.${defectType}`)} (${_.get(inspection, `defect_type_map.${defectType}`)})`}
                      </span>
                    </Fragment>
                  }
                  return null
                })}
              </Fragment>
            ) : (
              <span className='font-source text-[12px] font-normal'>
                {translation('liveDashboard.na')}  
              </span>
            )}
          </div>
        </div>
        <div className='flex px-2 items-center gap-2 self-stretch'>
          <div className='flex w-[102px] h-[15px] items-center gap-2'>
            <div className='flex flex-col justify-center items-center gap-2.5'>
              <span className='font-source text-[12px] font-semibold'>
                {translation('liveDashboard.defectFeedback')}
              </span>
            </div>
          </div>
          <div className='flex items-center gap-1'>
            <span className='font-source text-[12px] font-normal'>
              {translation('liveDashboard.na')}
            </span>
          </div>
        </div>
      </div>
    </Container>
  );
};

const SingleCroppedThumbnail = (props) => {
  const { d } = props;

  const [curUrl, setCurUrl] = useState(null);

  useEffect(() => {
    if (_.isEmpty(d)) return;

    const initUrl = async (d) => {
      const res = await fetch(`${serverEndpoint}/data?data_uri=${_.get(d, 'color_map_uri')}`);
      const blob = await res.blob();
      const reader = new FileReader();
      reader.readAsDataURL(blob);
      const curUrl = await new Promise((resolve) => {
        reader.onloadend = () => {
          resolve(reader.result);
        };
      });
      setCurUrl(curUrl);
    };

    initUrl(d);
  }, [d]);  

  return (
    <Tooltip title={<div className='flex flex-col gap-1 justify-start'>
      <span className='font-source text-[12px] font-normal'>
        {`${translation('liveDashboard.componentName')} ${_.get(d, 'feature_type', '')}`}
      </span>
      <div className='flex items-center gap-1'>
        <span className='font-source text-[12px] font-normal'>
          {`${translation('liveDashboard.failedInspectionItems')} ${_.map(_.get(d, 'checklist', []), (agentName) => {
            return translation(`viewInspection.lineItem.${agentName}`);
          })}`}
        </span>
      </div>
    </div>}>
      <div className='flex flex-col justify-center items-center self-stretch'>
        <img
          src={curUrl}
          className='object-contain bg-[#333]'
          style={{ width: '82px', height: '82px' }}
          alt='defect-thumbnail'
        />
        <div className='flex justify-between py-1 px-2 items-center self-stretch bg-[#EB5757]'>
          <span className='font-source text-[10px] font-normal'>
            {translation('liveDashboard.defect')}
          </span>
          <img className='w-[14px] h-[14px]' src='/img/icn/icn_compareArrow_white.svg' alt='icn_compareArrow_white' />
        </div>
      </div>
    </Tooltip>
  );
};

const Container = styled.div`
  background: rgba(255, 255, 255, 0.05);
  &:hover {
    background: rgba(255, 255, 255, 0.10);
    shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.25);
    transition: all 300ms;
  }
`;

export default LiveInspectionCard;