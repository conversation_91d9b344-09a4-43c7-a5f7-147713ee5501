import CalendarLocale from 'rc-picker/lib/locale/zh_CN';


export const serverEndpoint = localStorage.getItem('serverEndpoint') || 'http://localhost:8000';
export const serverHost =  localStorage.getItem('serverHost') || 'localhost:8000';

/* eslint-disable no-useless-escape */
const commonStrRegex = /^[a-zA-Z0-9\-\_\(\)\{\}\[\]]+$/; // accepts a-z, A-Z, 0-9, -, _, (, ), {, }, [, ]
const allowChineseRegex = /^[a-zA-Z0-9\-\(\)\{\}\[\]\u4e00-\u9fa5]+$/; // accepts a-z, A-Z, 0-9, -, (, ), {, }, [, ], general chinese characters
const allowChineseRegexNDotNSpace = /^[a-zA-Z0-9_ .\-\(\)\{\}\[\]\u4e00-\u9fa5]+$/; // accepts a-z, A-Z, 0-9, -, (, ), {, }, [, ], _ general chinese characters, space, dot
const alphaNumericUnderScoreRegex = /^[a-zA-Z0-9_]+$/; // accepts a-z, A-Z, 0-9, _

export const inputRegularExpression = {
  addProduct: {
    productName: allowChineseRegexNDotNSpace,
  },
  newProductVariant: {
    variantName: allowChineseRegexNDotNSpace,
  },
  customClass: {
    className: allowChineseRegex,
  },
};

export const defaultNewCameraSettingFrame = {
  exposure_stop: 0,
  brightness: 0,
  gain: 0,
  gamma: 0
};

export const defaultAlgorithmParams = {
  projector_pattern: 'PhasePattern',

  // color space
  color_space: 0,
  //sampling
  sampling: 0,
  // outlier
  enable_outlier: true,
  outlier: 10,
  enable_face_normal: false,
  face_normal_angle: 0,
  enable_cluster_filter: true,
  cluster_neighbor_distance: 25,
  cluster_filter_strength: 1,
  //smooting
  enable_gaussian: true,
  gaussian_strength: 1,
  enable_median: false,
  median_kernel_size: 3,
  enable_smooth: false,
  smooth_granularity: 'OneHundredth',
  //contrast
  saturation: false,
  enable_intensity: true,
  intensity: 10,
  enable_phase_quality: true,
  phase_quality_threshold: 0.05,
  //correction
  enable_fill_hole: false,
  hole_size: 1,
  depth_diff: 1,
  enable_contrast_distortion: false,
  contrast_distortion_treatment: 'Correct',
  contrast_distortion_strength: 0,
  // not in use
  coord_system: 0,
  pointcloud_resolution: 0,
  // color balance
  color_balance_red: 0.33333333333333333333333333,
  color_balance_green: 0.33333333333333333333333333,
  color_balance_blue: 0.33333333333333333333333333,
  // roi
  enable_roi: false,
  roi_box_center_x: 0,
  roi_box_center_y: 0,
  roi_box_center_z: 1,
  roi_box_dimension_x: 200,
  roi_box_dimension_y: 200,
  roi_box_dimension_z: 200,
  roi_box_rotation_angle_z: 0,
};

export const featureType = {
  screw: 'screw',
  product: 'product',
};

export const feedbackMaskRequiredAgent = ['anomaly_detection', 'defect_detection'];

export const retrainModelType = {
  defectModel: 'DEFECT_MODEL',
  heightModel: 'HEIGHT_MODEL',
};

export const retrainModelTaskPhaseType = {
  pending: 'PENDING_PHASE',
  setUpdate: 'SET_UPDATE_PHASE',
  modelUpdate: 'MODEL_UPDATE_PHASE',
  complete: 'COMPLETE_PHASE',
  failure: 'FAILURE_PHASE',
  invalid: 'INVALID_PHASE',
};

export const calibrationBoardDetail = {
  '9x11-24': {
    rows: 9,
    columns: 11,
    squareSize: 24,
  },
  '13x23-40': {
    rows: 13,
    columns: 23,
    squareSize: 40,
  },
  '5x7-36': {
    rows: 5,
    columns: 7,
    squareSize: 36,
  },
  '13x21-30': {
    rows: 13,
    columns: 21,
    squareSize: 30,
  },
  '9x15-24': {
    rows: 9,
    columns: 15,
    squareSize: 24,
  },
  '9x15-5': {
    rows: 9,
    columns: 15,
    squareSize: 5,
  },
};

export const heightDiff = 'height_diff';
export const threeDLineItems = [heightDiff];
export const defectDetection = 'defect_detection';
export const useUserThreshold = 'use_user_threshold';
export const userDefinedThreshold = 'user_defined_threshold';
export const aiDeviationThreshold = 'ai_deviation_threshold';
export const paixianToolAgent = 'line_detection';
export const paixianOutputLineStroke = 2;

export const baseBboxStrokeWidth = 12;

export const defineProductBboxBoundaryLengthLimit = 5;

export const customZhCNDatePickerLocale = {
  lang: {
    placeholder: '请选择日期',
    yearPlaceholder: '请选择年份',
    quarterPlaceholder: '请选择季度',
    monthPlaceholder: '请选择月份',
    weekPlaceholder: '请选择周',
    rangePlaceholder: ['开始日期', '结束日期'],
    rangeYearPlaceholder: ['开始年份', '结束年份'],
    rangeMonthPlaceholder: ['开始月份', '结束月份'],
    rangeQuarterPlaceholder: ['开始季度', '结束季度'],
    rangeWeekPlaceholder: ['开始周', '结束周'],
    "shortMonths": [
      "1月",
      "2月",
      "3月",
      "4月",
      "5月",
      "6月",
      "7月",
      "8月",
      "9月",
      "10月",
      "11月",
      "12月"
    ],
    "shortWeekDays": [
      "日",
      "一",
      "二",
      "三",
      "四",
      "五",
      "六"
    ],
    ...CalendarLocale,
  },
  timePickerLocale: {
    placeholder: '请选择时间',
  },
  "dateFormat": "YYYY-MM-DD",
  "dateTimeFormat": "YYYY-MM-DD HH:mm:ss",
  "weekFormat": "YYYY-wo",
  "monthFormat": "YYYY-MM",
};

export const maxFisMessageCount = 50;