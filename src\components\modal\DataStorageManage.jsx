import { Button, Input, InputNumber, Modal } from 'antd';
import React, { Fragment, useEffect, useState } from 'react';
import { handleRequestFailed, translation } from '../../common/util';
import { PrimaryButtonConfigProvider } from '../../common/darkModeComponents';
import { useLazyGetDataArchiveConfigQuery, usePutDataArchiveConfigMutation } from '../../services/system';
import _ from 'lodash';
import { ALERT_TYPES, aoiAlert } from '../../common/alert';
import { useDispatch } from 'react-redux';
import { setContainerWindowLoadingLocked } from '../../actions/setting';
import ArchiveDataComfirmation from './ArchiveDataComfirmation';


const DataStorageManage = (props) => {
  const {
    isOpened,
    setIsOpened,
  } = props;

  const dispatch = useDispatch();

  const [dataArchiveDays, setDataArchiveDays] = useState(1);
  const [dataArchivePath, setDataArchivePath] = useState('');
  const [isArchiveDataConfirmationOpened, setIsArchiveDataConfirmationOpened] = useState(false);

  const [getDataArchiveConfig] = useLazyGetDataArchiveConfigQuery();
  const [putDataArchiveConfig] = usePutDataArchiveConfigMutation();

  const handleSubmit = async (dataArchiveDays, dataArchivePath) => {
    if (!_.isInteger(dataArchiveDays) || _.isEmpty(dataArchivePath)) {
      aoiAlert(translation('notification.error.pleaseEnterAValidValue'), ALERT_TYPES.COMMON_ERROR);
      return;
    }

    dispatch(setContainerWindowLoadingLocked(true));

    const res = await putDataArchiveConfig({
      data_archive_days: dataArchiveDays,
      data_archive_path: dataArchivePath,
    });

    dispatch(setContainerWindowLoadingLocked(false));

    if (res.error) {
      handleRequestFailed('putDataArchiveConfig', res.error);
      console.error('Failed to put data archive config');
      return;
    }

    aoiAlert(translation('notification.success.dataStorageSettingUpdated'), ALERT_TYPES.COMMON_SUCCESS);
    setIsOpened(false);
  };

  useEffect(() => {
    if (!isOpened) return;

    const fetchData = async () => {
      const res = await getDataArchiveConfig();
      if (res.error) {
        handleRequestFailed('getDataArchiveConfig', res.error);
        console.error('Failed to get data archive config');
        return;
      }

      setDataArchiveDays(_.get(res, 'data.data_archive_days', 1));
      setDataArchivePath(_.get(res, 'data.data_archive_path', ''));
    };

    fetchData();
  }, [isOpened]);

  return (
    <Fragment>
      <Modal
        open={isOpened}
        onCancel={() => setIsOpened(false)}
        title={
          <span className='font-source text-[16px] font-semibold'>
            {translation('dataStorageManage.dataStorageManage')}
          </span>
        }
        footer={
          <div className='flex p-4 gap-2 slef-stretch items-start justify-center'>
            <Button
              style={{ width: '50%' }}
              onClick={() => setIsOpened(false)}
            >
              <span className='font-source text-[12px] font-normal'>
                {translation('common.cancel')}
              </span>
            </Button>
            <PrimaryButtonConfigProvider>
              <Button
                style={{ width: '50%' }}
                onClick={() => {
                  handleSubmit(dataArchiveDays, dataArchivePath);
                }}
              >
                <span className='font-source text-[12px] font-normal'>
                  {translation('common.save')}
                </span>
              </Button>
            </PrimaryButtonConfigProvider>
          </div>
        }
      >
        <div className='flex py-6 px-4 flex-col items-start gap-4 self-stretch'>
          <div className='flex flex-col gap-1 self-stretch'>
            <span className='font-source text-[14px] font-normal'>
              {translation('dataStorageManage.scanAndCleanUpCaptured')}
            </span>
            <span className='font-source text-[12px] italic font-normal'>
              {translation('dataStorageManage.notedArchivedImages')}
            </span>
          </div>
          <div className='flex flex-col gap-2 self-stretch'>
            <div className='flex gap-2 items-center self-stretch justify-between'>
              <span className='font-source text-[12px] font-normal'>
                {translation('dataStorageManage.dataRetentionPeriod')}
              </span>
              <InputNumber
                controls={false}
                style={{ width: '50%' }}
                min={1}
                step={1}
                value={dataArchiveDays}
                onChange={(value) => setDataArchiveDays(value)}
              />
            </div>
            <div className='flex gap-2 items-center self-stretch justify-between'>
              <span className='font-source text-[12px] font-normal'>
                {translation('dataStorageManage.dataStoragePath')}
              </span>
              <Input
                style={{ width: '50%' }}
                value={dataArchivePath}
                onChange={(e) => setDataArchivePath(e.target.value)}
              />
            </div>
            <div className='flex gap-2 items-center self-stretch justify-between'>
              <span className='font-source text-[12px] font-normal'>
                {translation('dataStorageManage.archiveAllData')}
              </span>
              <Button
                style={{ width: '50%' }}
                onClick={() => {
                  setIsOpened(false);
                  setIsArchiveDataConfirmationOpened(true);
                }}
              >
                <span className='font-source text-[12px] font-normal'>
                  {translation('dataStorageManage.execute')}
                </span>
              </Button>
            </div>
          </div>
        </div>
      </Modal>
      <ArchiveDataComfirmation
        isOpened={isArchiveDataConfirmationOpened}
        setIsOpened={setIsArchiveDataConfirmationOpened}
      />
    </Fragment>
  )
};

export default DataStorageManage;