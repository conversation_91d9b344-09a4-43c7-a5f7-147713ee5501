import React, { useEffect, useRef } from 'react';
import { HiddenCanvasDimensionCalcDiv, ThreeDDisplayWrapper } from '../../common/styledComponent/common';
import _ from 'lodash';
import { backendAutoGenTimeToDisplayString, backendTimestampToDisplayString, debounce, loadAndDecodePoints, translation } from '../../common/util';
import InferenceCompareViewer from '../common/viewer/InferenceCompareViewer';
import { serverEndpoint } from '../../common/const';


const HeightDiffComparison = (props) => {
  const {
    goldenCloudUri,
    ipcCloudUri,
    inspectionInfo,
    goldenProduct,
    isIpcCloudVisible,
    isGoldenCloudVisible,
    pointCloudDisplayedView,
  } = props;

  const goldenCanvasDimensionCalcDivRef = useRef(null);
  const ipcCanvasDimensionCalcDivRef = useRef(null);
  const goldenCanvasRef = useRef(null);
  const ipcCanvasRef = useRef(null);
  const goldenViewerRef = useRef(null);
  const ipcViewerRef = useRef(null);
  const isSyncing = useRef(false);

  const syncGoldenViewerCam = (camera, trackball) => {
    if (!ipcViewerRef.current || !goldenViewerRef.current || isSyncing.current) return;
    isSyncing.current = true;
    goldenViewerRef.current.updateCameraPose(camera, trackball);
    isSyncing.current = false;
  };

  const syncIpcViewerCam = (camera, trackball) => {
    if (!ipcViewerRef.current || !goldenViewerRef.current || isSyncing.current) return;
    isSyncing.current = true;
    ipcViewerRef.current.updateCameraPose(camera, trackball);
    isSyncing.current = false;
  };

  const loadScene = async (uri, viewer, isGolden) => {
    if (!uri || !viewer) return;

    const { positions, colors } = await loadAndDecodePoints(`${serverEndpoint}/data?data_uri=${uri}`);

    viewer.clearScene();

    viewer.loadScene({
      positions,
      colors,
      isGolden,
    });
  };

  useEffect(() => {
    if (!ipcViewerRef.current || !goldenViewerRef.current) return;
    ipcViewerRef.current.updateCameraInZPlane(pointCloudDisplayedView);
    goldenViewerRef.current.updateCameraInZPlane(pointCloudDisplayedView);
  }, [pointCloudDisplayedView]);

  useEffect(() => {
    if (!ipcViewerRef.current || !goldenViewerRef.current) return;
    ipcViewerRef.current.updateCloudVisibility(isIpcCloudVisible);
    goldenViewerRef.current.updateCloudVisibility(isGoldenCloudVisible);
  }, [
    isGoldenCloudVisible,
    isIpcCloudVisible,
  ]);

  useEffect(() => {
    if (!_.isEmpty(goldenCloudUri) && goldenViewerRef.current) {
      loadScene(goldenCloudUri, goldenViewerRef.current, true);
      goldenViewerRef.current.updateCameraInZPlane(pointCloudDisplayedView);
    }
  }, [goldenCloudUri]);

  useEffect(() => {
    if (!_.isEmpty(ipcCloudUri) && ipcViewerRef.current) {
      loadScene(ipcCloudUri, ipcViewerRef.current, false);
      ipcViewerRef.current.updateCameraInZPlane(pointCloudDisplayedView);
    }
  }, [ipcCloudUri]);

  useEffect(() => {
    if (!goldenCanvasRef.current || !goldenCanvasDimensionCalcDivRef.current || !ipcCanvasRef.current || !ipcCanvasDimensionCalcDivRef.current) return;

    const goldenViewer = new InferenceCompareViewer(
      goldenCanvasRef.current,
      goldenCanvasDimensionCalcDivRef.current.offsetHeight,
      goldenCanvasDimensionCalcDivRef.current.offsetWidth,
      (c, t) => syncIpcViewerCam(c, t),
    );

    const ipcViewer = new InferenceCompareViewer(
      ipcCanvasRef.current,
      ipcCanvasDimensionCalcDivRef.current.offsetHeight,
      ipcCanvasDimensionCalcDivRef.current.offsetWidth,
      (c, t) => syncGoldenViewerCam(c, t),
    );

    goldenViewerRef.current = goldenViewer;
    ipcViewerRef.current = ipcViewer;

    const updateCanvasDimension = () => {
      if (!goldenViewer || !ipcViewer || !goldenCanvasDimensionCalcDivRef.current || !ipcCanvasDimensionCalcDivRef.current) return;
      goldenViewer.updateSceneSize(goldenCanvasDimensionCalcDivRef.current.offsetWidth, goldenCanvasDimensionCalcDivRef.current.offsetHeight);
      ipcViewer.updateSceneSize(ipcCanvasDimensionCalcDivRef.current.offsetWidth, ipcCanvasDimensionCalcDivRef.current.offsetHeight);
    };

    const debounceUpdateCanvasDimension = debounce(updateCanvasDimension, 300);

    window.addEventListener('resize', debounceUpdateCanvasDimension);

    if (!_.isEmpty(goldenCloudUri)) {
      loadScene(goldenCloudUri, goldenViewer, true);
      goldenViewer.updateCameraInZPlane(pointCloudDisplayedView);
    }

    if (!_.isEmpty(ipcCloudUri)) {
      loadScene(ipcCloudUri, ipcViewer, false);
      ipcViewer.updateCameraInZPlane(pointCloudDisplayedView);
    }

    return () => {
      if (goldenViewer) {
        goldenViewer.clearScene();
        goldenViewer.destroy();
        goldenViewerRef.current = null;
      }

      if (ipcViewer) {
        ipcViewer.clearScene();
        ipcViewer.destroy();
        ipcViewerRef.current = null;
      }
      
      window.removeEventListener('resize', debounceUpdateCanvasDimension);
    };
  }, []);

  return (
    <div className='flex w-full h-full gap-2'>
      <div className='flex flex-col gap-2 w-[50%] h-full'>
        <div className='flex py-1 justify-between items-center self-stretch'>
          <span className='font-source text-[12px] font-semibold'>
            {translation('viewInspection.sample')}
          </span>
          <div className='flex py-1 px-2 gap-1 justify-center items-center'>
            <div className='flex w-[14px] h-[14px] p-[1px] justify-center items-center gap-2.5'>
              <img src='/img/icn/icn_calendar_white.svg' className='w-2.5 h-2.5 shrink' alt='calendar' />
            </div>
            <span className='font-source text-[10px] font-normal'>
              {_.get(inspectionInfo, 'timestamp', '') ? backendAutoGenTimeToDisplayString(_.get(inspectionInfo, 'timestamp', '')) : ''}
            </span>
          </div>
        </div>
        <div className='relative w-full h-full'>
          <ThreeDDisplayWrapper>
            <canvas ref={ipcCanvasRef} />
          </ThreeDDisplayWrapper>
          <HiddenCanvasDimensionCalcDiv ref={ipcCanvasDimensionCalcDivRef} />
        </div>
      </div>
      <div className='flex flex-col gap-2 w-[50%] h-full'>
        <div className='flex py-1 justify-between items-center self-stretch'>
          <span className='font-source text-[12px] font-semibold'>
            {translation('viewInspection.golden')}
          </span>
          <div className='flex py-1 px-2 gap-1 justify-center items-center'>
            <div className='flex w-[14px] h-[14px] p-[1px] justify-center items-center gap-2.5'>
              <img src='/img/icn/icn_calendar_white.svg' className='w-2.5 h-2.5 shrink' alt='calendar' />
            </div>
            <span className='font-source text-[10px] font-normal'>
              {backendAutoGenTimeToDisplayString(_.get(goldenProduct, 'last_modified', ''))}
            </span>
          </div>
        </div>
        <div className='relative w-full h-full'>
          <ThreeDDisplayWrapper>
            <canvas ref={goldenCanvasRef} />
          </ThreeDDisplayWrapper>
          <HiddenCanvasDimensionCalcDiv ref={goldenCanvasDimensionCalcDivRef} />
        </div>
      </div>
      {/* <div className='relative w-full h-full'>
        <ThreeDDisplayWrapper>
          <canvas ref={canvasRef} />
        </ThreeDDisplayWrapper>
        <HiddenCanvasDimensionCalcDiv ref={canvasDimensionCalcDivRef} />
      </div> */}
    </div>
  )
};

export default HeightDiffComparison;