import React, { useState } from 'react';
import { generateFeatureTypeDisplayText, handleRequestFailed, translation } from '../../common/util';
import _ from 'lodash';
import { useUpdateCurProductComponentToggleMutation } from '../../services/product';
import { Button, Checkbox, Select } from 'antd';
import { ALERT_TYPES, aoiAlert } from '../../common/alert';


const FilterComponentCol = (props) => {
  const {
    currentFilterMap,
    setCurrentFilterMap,
  } = props;

  const [componentNameFilter, setComponentNameFilter] = useState([]);

  const [updateCurProductComponentlist] = useUpdateCurProductComponentToggleMutation();

  return (
    <div
      className='flex w-[272px] flex-col items-start gap-1 self-stretch bg-[#ffffff0d]'
      style={{ height: 'calc(100vh - 198px)' }}
    >
      <div className='flex h-[32px] flex-col justify-center items-center self-stretch'>
        <div className='flex gap-2 justify-center items-center p-2 flex-1 self-stretch bg-[#ffffff0a]'>
          <img src='/img/icn/icn_filter_white.svg' alt='filter' className='w-[10.6px] h-4' />
          <span className='font-source text-[12px] font-semibold'>
            {translation('viewInspection.filterComponents')}
          </span>
        </div>
      </div>
      <div className='flex flex-1 flex-col items-start self-stretch'>
        <div className='flex p-4 flex-col flex-1 self-stretch'>
          <div className='py-2 flex flex-col items-start gap-2 self-stretch flex-1'>
            <span className='font-source text-[12px] font-normal'>
              {translation('viewInspection.systemWillSkipDisabledComponent')}
            </span>
            <span className='font-source text-[12px] font-normal'>
              {translation('viewInspection.atLeastOneComponentHas')}
            </span>
            <Select
              placeholder={translation('viewInspection.searchComponent')}
              allowClear
              showSearch
              options={_.map(_.keys(currentFilterMap), (key) => ({ label: generateFeatureTypeDisplayText(key), value: key }))}
              mode='tags'
              value={componentNameFilter}
              onChange={(value) => {
                setComponentNameFilter(value);
              }}
              style={{ width: '100%' }}
            />
            <div className='flex px-2 items-center gap-2'>
              <Checkbox
                checked={_.every(_.values(currentFilterMap), (v) => v)}
                onChange={(e) => {
                  if (!e.target.checked) {
                    aoiAlert(translation('notification.error.pleaseLeaveAtLeastOneComponentEnabled'), ALERT_TYPES.COMMON_ERROR);
                    return;
                  }
                  const handleUpdate = async (currentFilterMap, checked) => {
                    const res = await updateCurProductComponentlist({
                      current_product_options: _.mapValues(currentFilterMap, () => checked),
                    });

                    if (res.error) {
                      handleRequestFailed('updateCurProductComponentlist', res.error);
                      return;
                    }

                    setCurrentFilterMap(_.mapValues(currentFilterMap, () => checked));

                    aoiAlert(translation('notification.success.componentFilterUpdated'), ALERT_TYPES.COMMON_SUCCESS);
                  };
                  handleUpdate(currentFilterMap, e.target.checked);
                }}
              />
              <span className='font-source text-[12px] font-normal'>
                {translation('viewInspection.inspectAllComponents', { count: _.keys(currentFilterMap).length })}
              </span>
            </div>
            <div className='w-full h-[1px] bg-[#333]' />
            <div
              className='flex items-start gap-2 self-stretch flex-col overflow-y-auto'
              style={{ height: 'calc(100vh - 431px)' }}
            >
              {_.map(_.filter(_.keys(currentFilterMap), t => !_.isEmpty(componentNameFilter) ? _.includes(componentNameFilter, t) : true), (type) => (
                <div className='flex px-2 items-center gap-2' key={type}>
                  <Checkbox
                    checked={_.get(currentFilterMap, type, false)}
                    onChange={(e) => {
                      // have to leave at least one component enabled
                      const tmp = {
                        ...currentFilterMap,
                        [type]: e.target.checked,
                      };
                      if (_.every(_.values(tmp), (v) => !v)) {
                        aoiAlert(translation('notification.error.pleaseLeaveAtLeastOneComponentEnabled'), ALERT_TYPES.COMMON_ERROR);
                        return;
                      }
                      const handleUpdate = async (currentFilterMap, checked, type) => {
                        const res = await updateCurProductComponentlist({
                          current_product_options: {
                            ...currentFilterMap,
                            [type]: checked,
                          },
                        });

                        if (res.error) {
                          handleRequestFailed('updateCurProductComponentlist', res.error);
                          return;
                        }

                        setCurrentFilterMap({
                          ...currentFilterMap,
                          [type]: checked,
                        });

                        aoiAlert(translation('notification.success.componentFilterUpdated'), ALERT_TYPES.COMMON_SUCCESS);
                      };
                      handleUpdate(currentFilterMap, e.target.checked, type);
                    }}
                  />
                  <span className='font-source text-[12px] font-normal'>
                    {generateFeatureTypeDisplayText(type)}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
        {/* <div className='flex p-4 gap-2 self-stretch'>
          <Button>
            {translation('common.save')}
          </Button>
        </div> */}
      </div>
    </div>
  );
};

export default FilterComponentCol;