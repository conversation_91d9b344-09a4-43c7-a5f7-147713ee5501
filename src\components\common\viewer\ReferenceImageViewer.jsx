import { fabric } from 'fabric-with-erasing';
import _ from 'lodash';
import React from 'react';
import TwoDBase<PERSON>i<PERSON> from './TwoDBaseViewer';
import { debounce } from '../../../common/util';
import { paixianToolAgent } from '../../../common/const';

export default class ReferenceImageViewer extends TwoDBaseViwer {
  constructor(props) {
    super(props, props.displayCanvasRef);
    this.displayCanvasRef = props.displayCanvasRef;
    this.scene = null;
    this.fabricCanvas = null;
    this.sceneOriginalWidth = 0;
    this.sceneOriginalHeight = 0;
    this.containerRef = React.createRef();
    this.handleWindowResize = React.createRef();
    this.selectedParsedErrorDetail = props.selectedParsedErrorDetail;
    this.selectedDetail = props.selectedDetail;
    this.paixianOutputLine = null;
  }

  componentDidMount() {
    if (!this.displayCanvasRef.current) return;

    const fabricCanvas = new fabric.Canvas(this.displayCanvasRef.current, {
      uniformScaling: false,
    });
    this.fabricCanvas = fabricCanvas;
    fabricCanvas.skipOffscreen = true;
    fabricCanvas.contextContainer.imageSmoothingEnabled = true;

    let filterBackend = null;
    try {
      filterBackend = new fabric.WebglFilterBackend();
    } catch (e) {
      filterBackend = new fabric.Canvas2dFilterBackend();
    }
    fabricCanvas.filterBackend = filterBackend;
    if (fabric.isWebglSupported()) {
      console.log('WebGL is supported, increase texture size to 65536');
      fabric.textureSize = 65536;
    }

    const updateCanvasSize = () => {
      if (!this.containerRef.current || !this.fabricCanvas) return;
      const canvasWidth = this.containerRef.current.offsetWidth;
      const canvasHeight = this.containerRef.current.offsetHeight;
      this.fabricCanvas.setWidth(canvasWidth);
      this.fabricCanvas.setHeight(canvasHeight);
      this.fabricCanvas.renderAll();
    };

    this.handleWindowResize.current = debounce(updateCanvasSize, 300);
    window.addEventListener('resize', this.handleWindowResize.current);

    if (!_.isEmpty(this.props.src)) {
      this.loadScene(
        this.props.src,
        this.containerRef.current.offsetWidth,
        this.containerRef.current.offsetHeight,
      );
    }

    this.loadPaiXianLineToolOverlay(this.props.selectedParsedErrorDetail, this.scene, this.props.selectedDetail);
  }

  componentWillUnmount() {
    if (this.handleWindowResize.current)
      window.removeEventListener('resize', this.handleWindowResize.current);
  }

  componentDidUpdate(prevProps) {
    if (prevProps.src !== this.props.src) {
      this.loadScene(
        this.props.src,
        this.containerRef.current.offsetWidth - 2,
        this.containerRef.current.offsetHeight - 2,
      );
    }
    
    if (prevProps.curDisplayOptionsBrightness !== this.props.curDisplayOptionsBrightness) this.updateSceneBrightness(this.props.curDisplayOptionsBrightness);
    if (prevProps.curDisplayOptionsContrast !== this.props.curDisplayOptionsContrast) this.updateSceneContrast(this.props.curDisplayOptionsContrast);
    if (prevProps.curDisplayOptionsSaturation !== this.props.curDisplayOptionsSaturation) this.updateSceneSaturation(this.props.curDisplayOptionsSaturation);
    // if (prevProps.isSharpnessEnabled !== this.props.isSharpnessEnabled) this.updateSceneSharpness(this.props.isSharpnessEnabled);

    if (prevProps.selectedParsedErrorDetail !== this.props.selectedParsedErrorDetail || prevProps.selectedDetail !== this.props.selectedDetail) {
      this.loadPaiXianLineToolOverlay(this.props.selectedParsedErrorDetail, this.scene, this.props.selectedDetail);
    }

    if (this.fabricCanvas) this.fabricCanvas.renderAll();
  }

  loadPaiXianLineToolOverlay = (selectedParsedErrorDetail, scene, selectedDetail) => {
    if (!selectedParsedErrorDetail || !scene) return;

    if (selectedDetail !== paixianToolAgent) return;

    if (this.paixianOutputLine) {
      this.fabricCanvas.remove(this.paixianOutputLine);
      this.paixianOutputLine = null;
    }

    // we expect tow points in the selectedParsedErrorDetail in the original scene coordinate system
    // const p0 = _.get(selectedParsedErrorDetail, 'points[0]');
    // const p1 = _.get(selectedParsedErrorDetail, 'points[1]');

    // TODO: remove test points
    const p0 = { x: 0, y: 0 };
    const p1 = { x: 50, y: 50 };

    const line = new fabric.Line([p0.x, p0.y, p1.x, p1.y], {
      stroke: 'red',
      strokeWidth: 2,
      selectable: false,
      evented: false,
    });

    this.fabricCanvas.add(line);
    this.paixianOutputLine = line;
  };

  loadScene = (dataUrl, canvasWidth, canvasHeight) => {
    if (!this.fabricCanvas || !this.displayCanvasRef.current) return;

    // remove previous scene
    if (this.scene) {
      this.fabricCanvas.remove(this.scene);
      this.scene = null;
    }

    fabric.Image.fromURL(dataUrl, (img) => {
      img.set({ selectable: false, evented: false });
      this.sceneOriginalWidth = img.width;
      this.sceneOriginalHeight = img.height;
      const { fabricCanvas } = this;
      fabricCanvas.setWidth(canvasWidth);
      fabricCanvas.setHeight(canvasHeight);

      if (this.scene) {
        this.scene.moveTo(0);
      }

      this.scene = img;

      this.scene.filters = [
        new fabric.Image.filters.Brightness({ brightness: _.isNumber(this.props.curDisplayOptionsBrightness) ? (this.props.curDisplayOptionsBrightness - 50) / 50 : 0 }),
        new fabric.Image.filters.Contrast({ contrast: _.isNumber(this.props.curDisplayOptionsContrast) ? (this.props.curDisplayOptionsContrast - 50) / 50 : 0 }),
        new fabric.Image.filters.Saturation({ saturation: _.isNumber(this.props.curDisplayOptionsSaturation) ? (this.props.curDisplayOptionsSaturation - 50) / 50 : 0 }),
      ];

      // if (this.props.isSharpnessEnabled) {
      //   this.scene.filters.push(
      //     new fabric.Image.filters.Convolute({
      //       matrix: [0, -1, 0, -1, 5, -1, 0, -1, 0],
      //     })
      //   );
      // }

      this.scene.filters.push(
        new fabric.Image.filters.Convolute({
          matrix: [0, -1, 0, -1, 5, -1, 0, -1, 0],
        })
      );

      this.scene.applyFilters();

      const cropImageEdges = (fabricImage, x) => {
        const origImg = fabricImage.getElement();
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
      
        canvas.width = origImg.width - 2 * x;
        canvas.height = origImg.height - 2 * x;
      
        ctx.drawImage(
          origImg,
          x, x,                            // source start x,y
          canvas.width, canvas.height,    // source width/height
          0, 0,                            // dest start x,y
          canvas.width, canvas.height     // dest width/height
        );
      
        fabric.Image.fromURL(canvas.toDataURL(), (newImg) => {
          newImg.set({
            left: fabricImage.left + x,
            top: fabricImage.top + x,
            evented: false,
            selectable: false,
          });

          fabricCanvas.add(newImg);
          this.scene = newImg;
        });
      }

      cropImageEdges(this.scene, 2);

      this.updateZIndex();

      this.resetView();
    });
  };

  updateZIndex = () => {
    if (this.scene) {
      this.scene.moveTo(1);
    }
    if (this.paixianOutputLine) {
      this.paixianOutputLine.moveTo(2);
    }

    this.fabricCanvas.requestRenderAll();
  };

  resetView = () => {
    if (!this.fabricCanvas || !this.scene) return;
    const canvasWidth = this.fabricCanvas.getWidth();
    const canvasHeight = this.fabricCanvas.getHeight();
    const sceneWidth = this.scene.width * this.scene.scaleX;
    const sceneHeight = this.scene.height * this.scene.scaleY;
    const scaleX = canvasWidth / sceneWidth;
    const scaleY = canvasHeight / sceneHeight;
    const zoom = Math.min(scaleX, scaleY);

    const panX = (canvasWidth - sceneWidth * zoom) / 2;
    const panY = (canvasHeight - sceneHeight * zoom) / 2;

    this.fabricCanvas.viewportTransform = [zoom, 0, 0, zoom, panX, panY];
    this.fabricCanvas.requestRenderAll();
  };

  updateSceneBrightness = (brightness) => {
    if (this.scene) {
      if (_.get(this.scene, 'filters[0]')) {
        this.scene.filters[0].brightness = (brightness - 50) / 50;
        this.scene.applyFilters();
      } else {
        this.scene.filters = [new fabric.Image.filters.Brightness({ brightness: (brightness - 50) / 50 })];
        this.scene.applyFilters();
      }
      this.fabricCanvas.renderAll();
    }
  };

  updateSceneContrast = (contrast) => {
    if (this.scene) {
      if (_.get(this.scene, 'filters[1]')) {
        this.scene.filters[1].contrast = (contrast - 50) / 50;
        this.scene.applyFilters();
      } else {
        this.scene.filters = [new fabric.Image.filters.Brightness({ brightness: 0 }), new fabric.Image.filters.Contrast({ contrast: (contrast - 50) / 50 })];
        this.scene.applyFilters();
      }
      this.fabricCanvas.renderAll();
    }
  };

  updateSceneSaturation = (saturation) => {
    if (this.scene) {
      if (_.get(this.scene, 'filters[2]')) {
        this.scene.filters[2].saturation = (saturation - 50) / 50;
        this.scene.applyFilters();
      } else {
        this.scene.filters = [new fabric.Image.filters.Brightness({ brightness: 0 }), new fabric.Image.filters.Contrast({ contrast: 0 }), new fabric.Image.filters.Saturation({ saturation: (saturation - 50) / 50 })];
        this.scene.applyFilters();
      }
      this.fabricCanvas.renderAll();
    }
  };

  updateSceneSharpness = (enabled) => {
    if (this.scene) {
      this.scene.filters = this.scene.filters.filter((filter) => !(filter instanceof fabric.Image.filters.Convolute));
      if (enabled) {
        this.scene.filters.push(
          new fabric.Image.filters.Convolute({
            matrix: [0, -1, 0, -1, 5, -1, 0, -1, 0],
          })
        );
      }
      this.scene.applyFilters();
      this.fabricCanvas.renderAll();
    }
  };

  render() {
    return (
      <div className='w-full h-full overflow-hidden' ref={this.containerRef}>
        {super.render()}
      </div>
    );
  }
}
