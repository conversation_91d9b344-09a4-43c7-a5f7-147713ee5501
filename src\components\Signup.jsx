import React, { useState } from 'react';
import { translation } from '../common/util';
import { DarkButton, DarkInput } from '../common/darkModeComponents';
import { ALERT_TYPES, aoiAlert } from '../common/alert';
import { useSignupMutation } from '../services/auth';
import { useDispatch } from 'react-redux';


const Signup = (props) => {
  const dispatch = useDispatch();

  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [comfirmPassword, setComfirmPassword] = useState('');

  const [userSignup, { isLoading,  }] = useSignupMutation();

  const handleSignupSubmit = async (e) => {
    e.preventDefault();

    if (password !== comfirmPassword) {
      aoiAlert(translation('notification.error.passwordMismatch'), ALERT_TYPES.COMMON_ERROR);
      return;
    }

    const res = await userSignup({ email, password });
    // console.log(res);
  };

  return (
    <div className='inline-flex flex-col items-center gap-[230px] w-full h-full justify-center'>
      <div className='flex p-4 flex-col items-center gap-8'>
        <div className='flex w-[436px] py-12 px-6 flex-col items-center gap-8 rounded-[10px] bg-gray-1 shadow'>
          <div className='flex flex-col gap-4 items-center'>
            <img src='' alt='logo' className='w-16 h-16 p-[11px] flex justify-center items-center' />
            <div className='flex flex-col gap-2 items-center self-stretch'>
              <span className='text-white text-[32px] font-normal font-rubik'>
                {translation('signup.createAnAccount')}
              </span>
              <div className='flex gap-2 px-6 justify-center items-center'>
                <span className='font-source text-[12px] font-normal'>
                  {translation('signup.alreadyHaveAnAccount')}
                </span>
                <span
                  className='font-source text-[12px] font-semibold cursor-pointer text-AOI-blue'
                  onClick={() => {
                    props.history.push('/login');
                  }}
                >
                  {translation('login.login')}
                </span>
              </div>
            </div>
          </div>
          <div className='flex flex-col items-center gap-8 self-stretch'>
            <div className='flex flex-col gap-2 slef-stretch items-center w-full'>
              <DarkInput
                style={{ width: '100%' }}
                placeholder={translation('login.email')}
                value={email}
                onChange={(e) => setEmail(e.target.value)}
              />
              <DarkInput
                style={{ width: '100%' }}
                placeholder={translation('login.password')}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                type='password'
              />
              <DarkInput
                style={{ width: '100%' }}
                placeholder={translation('signup.reenterPassword')}
                value={comfirmPassword}
                onChange={(e) => setComfirmPassword(e.target.value)}
                type='password'
              />
            </div>
            <DarkButton
              style={{ width: '100%' }}
              onClick={(e) => handleSignupSubmit(e)}
              loading={isLoading}
            >
              {translation('signup.signup')}
            </DarkButton>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Signup;