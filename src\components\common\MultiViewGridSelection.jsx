import _ from 'lodash';
import React, { useEffect, useRef, useState } from 'react';


const MultiViewGridSelection = (props) => {
  const {
    layout, // array of cols for each row ex. [2, 3, 4] for 3 rows with 2, 3, and 4 cols respectively
    selectedViewId, // topleft view id would be 0 and incrementing to the right and down
    onSelectView, // callback function to select a view
    rowHeight: definedRowHeight, // height of each row
    width,
    height,
    defectSteps,
  } = props;

  // const layout = [2, 3, 4];

  const containerRef = useRef(null);

  const [gridRows, setGridRows] = useState([]);
  const [rowHeight, setEowHeight] = useState(0);

  const getCurrentViewId = (row, col) => {
    // sum of all cols in previous rows + current col
    return _.sum(_.slice(layout, 0, row)) + col;
  };

  useEffect(() => {
    if (_.isEmpty(layout)) return;
    // generate grid rows
    const result = [];
    for (let r = 0; r < layout.length; r++) {
      const curR = []; // good views: 'g', defect views: 'ng'
      const colN = layout[r];
      _.times(colN, (c) => {
        curR.push(_.includes(_.keys(defectSteps), String(getCurrentViewId(r, c))) ? 'ng' : 'g');
      });
      result.push(curR);
    }
    setGridRows(result);
    setEowHeight(containerRef.current.offsetHeight / layout?.length);
  }, [layout, defectSteps]);

  return (
    <div
      style={{
        width: _.isNumber(width) ? `${width}px` : '84px',
        height: _.isNumber(height) ? `${height}px` : '84px',
        display: 'flex',
        flexDirection: 'column',
        gap: '2px',
        borderRadius: '2px',
        background: '#131313',
        padding: '2px',
        border: `${!_.isInteger(selectedViewId) ? '1px solid #56CCF2' : '1px solid #000'}`,
        transition: 'border 0.3s',
      }}
      ref={containerRef}
    >
      {_.map(gridRows, (cols, row) => (
        <div
          style={{
            display: 'grid',
            gridTemplateColumns: `repeat(${cols.length}, 1fr)`,
            height: _.isNumber(definedRowHeight) ? definedRowHeight : rowHeight,
            gap: '2px',
          }}
          key={row}
        >
          {_.map(cols, (col, c) => (
            <div
              key={c}
              style={{
                width: '100%',
                height: '100%',
                backgroundColor: col === 'g' ? '#333' : '#EB5757',
              }}
              className={`transition-all duration-300 ease-in-out
                ${selectedViewId === getCurrentViewId(row, c) ? 'border-[1px] border-AOI-blue' : 'border-[1px] border-[#4F4F4F]'} 
                ${_.isFunction(onSelectView) && 'cursor-pointer hover:border-AOI-blue'}`}
              onClick={() => {
                if (_.isFunction(onSelectView)) onSelectView(getCurrentViewId(row, c));
              }}
            />
          ))}
        </div>
      ))}
    </div>
  );
};

export default MultiViewGridSelection;