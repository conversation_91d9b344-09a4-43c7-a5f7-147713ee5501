import { Button, Checkbox, Dropdown, InputNumber, Select, Tooltip } from 'antd';
import _ from 'lodash';
import React, { Fragment, useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { CustomCollapse } from '../../common/darkModeComponents';
import { convertBackendTimestampToMoment, getColorByStr, handleRequestFailed, sleep, translation } from '../../common/util';
import { useGetCustomFeaturesQuery, useUpdateFeatureByProductIdAndStepMutation } from '../../services/feature';
import { productApi, useGetProductByIdQuery } from '../../services/product';
import { systemApi } from '../../services/system';
import ProductAnnotation from '../Scene/ProductAnnotation';
import { featureType, inputRegularExpression } from '../../common/const';
import { setContainerWindowLoadingLocked } from '../../actions/setting';
import { setSelectedFeatureIdInManageProduct } from '../../actions/product';
import ProductVariationCapture from '../modal/ProductVariationCapture';
import { ALERT_TYPES, aoiAlert } from '../../common/alert';
import ProductVariationDefineDescription from '../modal/ProductVariationDefineDescription';
import IgnoreMaskPreview from './IgnoreMaskPreview';
import LineItems from './LineItems';


const GoldenProductInferenceStep = (props) => {
  const dispatch = useDispatch();
  const {
    productId,
    stepNumber,
    handleStepRedirect,
    initSelectedFeatureId,
    features,
    productInfo,
    refetchProductById,
    selectedVariation,
    setSelectedVariation,
    setIsDeleteVarintModalOpened,
    handleFullProductRedirect,
    viewRef,
    handleGetAllStepsFeatures,
    shouldAutoScrollToFeatureRef,
  } = props;

  // const viewRef = useRef();
  const canvasRef = useRef();
  const ignoreMaskViewerRef = useRef();
  const ignoreMaskViewerCanvasRef = useRef();
  const ignoreMaskContainerRef = useRef();

  const [selectedFeatureParamJson, setSelectedFeatureParamJson] = useState(null);
  const [isVariationModalOpened, setIsVariationModalOpened] = useState(false);
  const [availableFeatureTypes, setAvailableFeatureTypes] = useState([]);
  const [searchValue, setSearchValue] = useState('');
  const [isProductVariationDefineDescModalOpened, setIsProductVariationDefineDescModalOpened] = useState(false);
  const [isEditingIgnoreMask, setIsEditingIgnoreMask] = useState(false);
  const [drawingMode, setDrawingMode] = useState('pan'); // pencil, eraser, pan
  const [ignoreMaskPreviewReload, setIgnoreMaskPreviewReload] = useState(0);
  const [ignoreMaskContainerHeight, setIgnoreMaskContainerHeight] = useState(0);
  const [ignoreMaskHeight, setIgnoreMaskHeight] = useState(0);

  const selectedFeatureId = useSelector((state) => state.product.selectedfeatureIdInManageProduct);
  const { data: systemMetadata } = useSelector((state) => systemApi.endpoints.getSystemMetadata.select()(state));
  // const { data: productInfo } = useSelector((state) => productApi.endpoints.getProductById.select(productId)(state));
  const productVariationDefineModalDisabled = useSelector((state) => state.setting.productVariationDefineModalDisabled);
  const isInferenceRunning = useSelector((state) => state.setting.isInferenceRunning);

  const [updateFeature] = useUpdateFeatureByProductIdAndStepMutation();
  const { data: customFeatures, refetch: refetchCustomFeatures } = useGetCustomFeaturesQuery({ product_id: productId });
  // const { data: productInfo, refetch: refetchProductById } = useGetProductByIdQuery(productId);

  useEffect(() => {
    setIsEditingIgnoreMask(false);
    if (_.isNull(selectedFeatureId)) return;
    const curFeature = _.find(features, { feature_id: selectedFeatureId });
    if (!curFeature) return;
    try {
      setSelectedFeatureParamJson(JSON.parse(_.get(curFeature, 'feature_param')));
    } catch (e) {
      console.error('error parsing feature_param', e);
    }
  }, [selectedFeatureId, features]);

  useEffect(() => {
    setAvailableFeatureTypes([
      // ..._.map(_.get(systemMetadata, 'default_component_types', []), (type) => ({ label: type, value: type })),
      ..._.map(customFeatures, (type) => ({ label: _.get(type, 'feature_scope') === 'global' ? _.get(type, 'feature_type') : _.get(type, 'feature_type').substring(1), value: _.get(type, 'feature_type') })),
    ]);
  }, [customFeatures, systemMetadata]);

  useEffect(() => {
    const run = async (selectedFeatureId) => {
      await sleep(330);

      if (_.isInteger(Number(selectedFeatureId)) && ignoreMaskContainerRef.current) {
        setIgnoreMaskContainerHeight(ignoreMaskContainerRef.current.clientHeight);
        setIgnoreMaskHeight(430);
      } else {
        setIgnoreMaskContainerHeight(0);
        setIgnoreMaskHeight(0);
      }
    };

    run(selectedFeatureId);
  }, [selectedFeatureId]);

  if (_.isEmpty(productInfo)) return null;

  return (
    <Fragment>
      <ProductVariationCapture
        isOpened={isVariationModalOpened}
        setIsOpened={setIsVariationModalOpened}
        handleVariationRegisterSuccess={async (newVariant) => {
          await refetchProductById();
          await sleep(1500); // product info update will update the scene and we need to wait for it to finish before update vairant
          if (!productVariationDefineModalDisabled) setIsProductVariationDefineDescModalOpened(true);
          setSelectedVariation(newVariant);
        }}
        productInfo={productInfo}
        productId={productId}
      />
      <ProductVariationDefineDescription
        isOpened={isProductVariationDefineDescModalOpened}
        setIsOpened={setIsProductVariationDefineDescModalOpened}
      />
      <div
        className='flex p-2 flex-col items-start gap-2 flex-1 self-stretch rounded-[2px]'
        style={{ background: 'rgba(255, 255, 255, 0.10)' }}
      >
        <div className='flex items-center gap-2 self-stretch rounded-[4px] justify-between'>
          <span className='font-source text-[12px] font-normal'>
            {`${_.get(productInfo, 'product_name')} > ${translation('productAnnotation.imageListItemTitle', { index: Number(stepNumber) + 1 })} > ${selectedVariation}`}
          </span>
          <div className='flex justify-center items-center gap-[3px]'>
            <div
              className={`duration-300 hover:ease-in hover:bg-gray-1 cursor-pointer flex w-[32px] h-[32px] py-1 px-0.5 flex-col justify-center items-center rounded-[2px] gap-2.5 bg-gray-1`}
            >
              <img src='/img/icn/icn_carouselLayout_white.svg' className='w-[16.8px] h-[14px] fill-white' alt='carousel' />
            </div>
            <div
              className={`duration-300 hover:ease-in hover:bg-gray-1 cursor-pointer flex w-[32px] h-[32px] py-1 px-0.5 flex-col justify-center items-center rounded-[2px] gap-2.5`}
              onClick={() => {
                handleFullProductRedirect();
              }}
            >
              <img src='/img/icn/icn_gridLayout_white.svg' className='w-[14px] h-[14px] fill-white' alt='grid' />
            </div>
          </div>
        </div>
        <div className='flex items-start flex-1 gap-[2px] self-stretch'>
          <div
            className={`flex flex-col bg-[#131313] ${!_.isNull(selectedFeatureId) ? 'py-2 px-3 items-start gap-4' : 'py-2 px-3 items-center justify-center gap-[10px]'}`}
            style={{ width: '322px', height: '100%' }}
          >
            { !_.isNull(selectedFeatureId) ? (
              <div
                className='flex flex-col py-2 px-3 items-start gap-4 w-full'
                style={{ height: 'calc(100vh - 258px)', overflow: 'auto' }}
              >
                <div className='flex py-1 items-start gap-2 self-stretch'>
                  <span className='font-source text-[12px] font-semibold'>
                    {translation('productAnnotation.selectedComponent')}
                  </span>
                </div>
                <div className='flex gap-2 items-start flex-col self-stretch'>
                  <span className='font-source text-[12px] font-normal'>
                    {translation('productAnnotation.type')}
                  </span>
                  <Select
                    showSearch
                    style={{ width: '100%' }}
                    value={_.get(_.find(features, { feature_id: selectedFeatureId }), 'feature_type')}
                    options={availableFeatureTypes}
                    onChange={async (value) => {
                      const curFeature = _.cloneDeep(_.find(features, { feature_id: selectedFeatureId }));
                      curFeature.feature_type = value;
                      curFeature.feature_scope = _.find(_.filter(customFeatures, f => _.get(f, 'feature_scope') === 'global'), (f) => { return f.feature_type === value }) && !_.startsWith(value, '_') ? 'global' : 'product';
                      const res = await updateFeature(curFeature);
                      if (_.get(res, 'error')) {
                        handleRequestFailed('updateFeature', _.get(res, 'error'));
                        return;
                      }
                      aoiAlert(translation('notification.success.featureUpdated'), ALERT_TYPES.COMMON_SUCCESS);
                      return;
                    }}
                    onSearch={(value) => setSearchValue(value)}
                    notFoundContent={
                      !_.isEmpty(searchValue) ?
                      <div className='flex items-center justify-between'>
                        <span className='font-source text-[12px] font-normal'>
                          {searchValue}
                        </span>
                        <Button
                          size='small'
                          onClick={() => {
                            const handle = async (searchValue, selectedFeatureId, features) => {
                              const regExp = new RegExp(inputRegularExpression.customClass.className);
                              if (!regExp.test(searchValue)) {
                                aoiAlert(translation('notification.error.customClassNameCanOnlyContainLettersNumbersAndUnderscores'), ALERT_TYPES.COMMON_ERROR);
                                return;
                              }

                              dispatch(setContainerWindowLoadingLocked(true));
                              const curFeature = _.cloneDeep(_.find(features, { feature_id: selectedFeatureId }));
                              curFeature.feature_type = `_${searchValue}`;
                              curFeature.feature_scope = 'product';
                              const res = await updateFeature(curFeature);
                              if (_.get(res, 'error')) {
                                handleRequestFailed('updateFeature', _.get(res, 'error'));
                                dispatch(setContainerWindowLoadingLocked(false));
                                return;
                              }
                              refetchCustomFeatures();
                              aoiAlert(translation('notification.success.featureUpdated'), ALERT_TYPES.COMMON_SUCCESS);
                              dispatch(setContainerWindowLoadingLocked(false));
                              return;
                            };
                            handle(searchValue, selectedFeatureId, features);
                          }}
                        >
                          <span className='font-source text-[12px] font-semibold'>
                            {translation('common.addAndUpdate')}
                          </span>
                        </Button>
                      </div> : null
                    }
                  />
                </div>
                { _.find(features, { feature_id: selectedFeatureId }) &&
                  _.get(_.find(features, { feature_id: selectedFeatureId }), 'feature_type') !== featureType.product &&
                    <LineItems
                      lineItems={_.get(_.find(features, { feature_id: selectedFeatureId }), 'line_item_params')}
                      systemMetadata={systemMetadata}
                      feature={_.find(features, { feature_id: selectedFeatureId })}
                      handleGetAllStepsFeatures={handleGetAllStepsFeatures}
                      productId={productId}
                      step={stepNumber}
                    />
                }
                {/* <div className='flex w-full h-[1px] bg-[#333]' /> */}
                <div
                  className='flex flex-col gap-4 self-stretch'
                >
                  <div className='flex flex-col gap-1 self-stretch'>
                    <div className='flex gap-1 items-center'>
                      <span className='font-source text-[12px] font-normal'>
                        {translation('productAnnotation.ignoreMask')}
                      </span>
                      <Tooltip title={
                        <span className='font-source text-[12px] font-normal'>
                          {translation('productAnnotation.ignoreMaskDesc')}
                        </span>
                      }>
                        <div className='flex w-4 h-4 justify-center items-center'>
                          <img src='/img/icn/icn_info_white.svg' className='w-[12px] h-[12px]' alt='info' />
                        </div>
                      </Tooltip>
                    </div>
                    {/* <div className='flex p-1 items-center gap-2 self-stretch'>
                      <Checkbox
                        disabled
                        checked={!_.isEmpty(_.get(_.find(features, { feature_id: selectedFeatureId }), 'mask_uri'))}
                      />
                      <span className='font-source text-[12px] font-normal'>
                        {translation('productAnnotation.enableIgnoreMaskForThisComponent')}
                      </span>
                    </div> */}
                    <div className='flex items-center gap-2 self-stretch'>
                      {isEditingIgnoreMask ?
                        <div className='flex flex-col gap-1 self-stretch w-full'>
                          <div className='flex items-center gap-2 self-stretch'>
                            <img src='/img/icn/icn_warning_red.svg' className='w-[12px] h-[12px]' alt='info' />
                            <span
                              className='font-source text-[12px] font-normal italic'
                              style={{
                                lineHeight: 'normal',
                              }}
                            >
                              {translation('productAnnotation.pleaseRetrainTheModelAfterEditingIgnoreMask')}
                            </span>
                          </div>
                          <div className='flex items-center gap-2 self-stretch'>
                            <Button
                              style={{ width: '50%' }}
                              onClick={() => {
                                if (!ignoreMaskViewerRef.current) return;
                                const drawnMaskBase64 = ignoreMaskViewerRef.current.outputDrawnMaskAsBase64();

                                // if (_.isEmpty(drawnMaskBase64)) {
                                //   aoiAlert(translation('notification.warning.emptyMask'), ALERT_TYPES.COMMON_WARNING);
                                //   return;
                                // }

                                // console.log('drawnMaskBase64', drawnMaskBase64);
                                const curFeature = _.cloneDeep(_.find(features, { feature_id: selectedFeatureId }));
                                curFeature.mask_data = drawnMaskBase64;
                                curFeature.mask_width = _.get(curFeature, 'roi.points.1.x') - _.get(curFeature, 'roi.points.0.x') + 1;
                                curFeature.mask_height = _.get(curFeature, 'roi.points.1.y') - _.get(curFeature, 'roi.points.0.y') + 1;
                                // mask_height: _.get(selectedFeature, 'roi.points.1.y') - _.get(selectedFeature, 'roi.points.0.y'),
                                // mask_width: _.get(selectedFeature, 'roi.points.1.x') - _.get(selectedFeature, 'roi.points.0.x'),
                                const run = async (curFeature, ignoreMaskViewerRef) => {
                                  const res = await updateFeature(curFeature);
                                  if (_.get(res, 'error')) {
                                    handleRequestFailed('updateFeature', _.get(res, 'error'));
                                    return;
                                  }
                                  aoiAlert(translation('notification.success.featureUpdated'), ALERT_TYPES.COMMON_SUCCESS);
                                  // wait for the feature to be updated before reload in scene
                                  await sleep(300);
                                  ignoreMaskViewerRef.current.handleChangeMode('pan');
                                  setDrawingMode('pan');
                                  ignoreMaskViewerRef.current.clearScene();
                                  ignoreMaskViewerRef.current.loadCroppedFrame(curFeature);
                                  setIgnoreMaskPreviewReload(ignoreMaskPreviewReload + 1);
                                };
                                
                                run(curFeature, ignoreMaskViewerRef);
                              }}
                            >
                              <span className='font-source text-[12px] font-normal'>
                                {translation('common.save')}
                              </span>
                            </Button>
                            <Button
                              style={{ width: '50%' }}
                              onClick={() => {
                                if (!ignoreMaskViewerRef.current) return;
                                const curFeature = _.cloneDeep(_.find(features, { feature_id: selectedFeatureId }));
                                ignoreMaskViewerRef.current.handleChangeMode('pan');
                                setDrawingMode('pan');
                                ignoreMaskViewerRef.current.clearScene();
                                ignoreMaskViewerRef.current.loadCroppedFrame(curFeature);
                                setIsEditingIgnoreMask(false);
                              }}
                            >
                              <span className='font-source text-[12px] font-normal'>
                                {translation('common.cancel')}
                              </span>
                            </Button>
                          </div>
                        </div> :
                        <Button
                          style={{ width: '100%' }}
                          onClick={() => setIsEditingIgnoreMask(true)}
                        >
                          <span className='font-source text-[12px] font-normal'>
                            {translation('common.editMask')}
                          </span>
                        </Button>
                      }
                    </div>
                  </div>
                  <div
                    className='flex gap-1 self-stretch flex-col'
                    style={{ height: '260px' }}
                  >
                    <span className='font-source text-[12px] font-normal'>
                      {translation('productAnnotation.currentIgnoreMaskPreview')}
                    </span>
                    <IgnoreMaskPreview
                      feature={_.find(features, { feature_id: selectedFeatureId })}
                      ignoreMaskPreviewReload={ignoreMaskPreviewReload}
                    />
                  </div>
                </div>
              </div>
            ) : (
              <span className='font-source text-[12px] font-normal italic'>
                {translation('productAnnotation.noComponentSelected')}
              </span>
            ) }
          </div>
          <div className='flex flex-col items-start gap-2 self-stretch flex-1'>
            <ProductAnnotation
              productInfo={productInfo}
              productId={productId}
              step={stepNumber}
              features={features}
              initSelectedFeatureId={initSelectedFeatureId}
              selectedFeatureId={selectedFeatureId}
              selectedVariation={selectedVariation}
              viewRef={viewRef}
              canvasRef={canvasRef}
              handleGetAllStepsFeatures={handleGetAllStepsFeatures}
              isEditingIgnoreMask={isEditingIgnoreMask}
              ignoreMaskViewerRef={ignoreMaskViewerRef}
              ignoreMaskViewerCanvasRef={ignoreMaskViewerCanvasRef}
              drawingMode={drawingMode}
              setDrawingMode={setDrawingMode}
              shouldAutoScrollToFeatureRef={shouldAutoScrollToFeatureRef}
            />
          </div>
        </div>
      </div>
    </Fragment>
  );
};

export default GoldenProductInferenceStep;