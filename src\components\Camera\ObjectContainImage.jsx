import _ from 'lodash';
import React, { useEffect, useRef, useState } from 'react';
import { serverEndpoint } from '../../common/const';
import { handleRequestFailed } from '../../common/util';

// for displaying product detection/inference result
const ObjectContainImage = (props) => {
  const {
    cameraId,
    onClick,
    isFullView,
    // cameraPreviewImageData,
    uri,
  } = props;

  const [cameraPreviewImageData, setCameraPreviewImageData] = useState(null);
  const [dimension, setDimensions] = useState({ width: 0, height: 0 });
  const containerRef = useRef();

  useEffect(() => {
    if (!uri) return;
    const fetchAndDecode2dData = async (imgUri) => {
      let dataRes;
      try {
        dataRes = await fetch(`${serverEndpoint}/data?data_uri=${imgUri}`);
      } catch (error) {
        handleRequestFailed('fetchImageData', error);
        return;
      }

      const blob = await dataRes.blob();
      const curImage = URL.createObjectURL(blob);
      setCameraPreviewImageData(curImage);
    };

    fetchAndDecode2dData(uri);
  }, [uri]);

  useEffect(() => {
    if (!containerRef.current) return;
    setDimensions({
      width: containerRef.current.clientWidth,
      height: containerRef.current.clientHeight
    });
  }, []);

  return (
    <div
      className={`${isFullView && 'cursor-pointer'} w-full h-full rounded-[2px] border-[1px] border-gray-2`}
      ref={containerRef}
      onClick={() => onClick()}
    >
      <img
        src={cameraPreviewImageData}
        className='object-contain'
        style={{ width: `${dimension.width}px`, height: `${dimension.height}px` }}
        alt=''
      />
    </div>
  );
};

export default ObjectContainImage;