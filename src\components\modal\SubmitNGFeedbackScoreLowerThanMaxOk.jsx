import { Button, Modal } from 'antd';
import React from 'react';
import { handleRequestFailed, translation } from '../../common/util';
import { PrimaryButtonConfigProvider } from '../../common/darkModeComponents';
import { useAnnotateFeatureMutation } from '../../services/session';
import _ from 'lodash';
import { ALERT_TYPES, aoiAlert } from '../../common/alert';


const SubmitNGFeedbackScoreLowerThanMaxOk = (props) => {
  const {
    isOpened,
    setIsOpened,
    ngConfirmPayload,
    callbackRef,
  } = props;

  const [annotateFeature] = useAnnotateFeatureMutation();

  return (
    <Modal
      open={isOpened}
      onCancel={() => setIsOpened(false)}
      title={
        <span className='font-source text-[16px] font-semibold'>
          {translation('common.pleaseConfirm')}
        </span>
      }
      footer={
        <div className='flex p-4 gap-2 slef-stretch items-start justify-center'>
          <Button
            style={{ width: '50%' }}
            onClick={() => setIsOpened(false)}
          >
            <span className='font-source text-[12px] font-normal'>
              {translation('common.cancel')}
            </span>
          </Button>
          <PrimaryButtonConfigProvider>
            <Button
              style={{ width: '50%' }}
              onClick={() => {
                const run = async (ngConfirmPayload, callbackRef) => {
                  if (!ngConfirmPayload) return;

                  const res = await annotateFeature(ngConfirmPayload);

                  if (res.error) {
                    handleRequestFailed('annotateFeature', _.get(res, 'error'));
                    return;
                  }

                  if (callbackRef.current) callbackRef.current();
                  aoiAlert(translation('notification.success.inferenceAnnotation'), ALERT_TYPES.COMMON_SUCCESS);
                  setIsOpened(false);
                };

                run(ngConfirmPayload, callbackRef);
              }}
            >
              <span className='font-source text-[12px] font-normal'>
                {translation('common.save')}
              </span>
            </Button>
          </PrimaryButtonConfigProvider>
        </div>
      }
    >
      <div className='flex py-6 px-4 flex-col items-start gap-8 self-stretch'>
        <div className='flex flex-col items-start gap-4 self-stretch'>
          <span className='font-source text-[16px] font-semibold'>
            {translation('ngFeedbackConfirmation.pleaseConfirmTheFollowingToProceed')}
          </span>
          <div className='flex flex-col gap-1 self-stretch'>
            <span className='font-source text-[14px] font-normal'>
              {translation('ngFeedbackConfirmation.ngIsReal')}
            </span>
            <span className='font-source text-[14px] font-normal'>
              {translation('ngFeedbackConfirmation.increaseOfOverKill')}
            </span>
          </div>
        </div>
      </div>
    </Modal>
  )
};

export default SubmitNGFeedbackScoreLowerThanMaxOk;