import { createSlice } from '@reduxjs/toolkit';


// NOTE: this is not rtk query, this is a normal slice
const initialState = {
  selectedGoldenProductStep: null, // int or null for full product view
  selectedProductIdInManageProduct: null,
  selectedfeatureIdInManageProduct: null,
};

const product = createSlice({
  name: 'product',
  initialState,
  reducers: {
    setSelectedGoldenProductStep(state, action) {
      state.selectedGoldenProductStep = action.payload;
    },
    setSelectedProductIdInManageProduct(state, action) {
      state.selectedProductIdInManageProduct = action.payload;
    },
    setSelectedFeatureIdInManageProduct(state, action) {
      state.selectedfeatureIdInManageProduct = action.payload;
    },
  },
});

export const {
  setSelectedGoldenProductStep,
  setSelectedProductIdInManageProduct,
  setSelectedFeatureIdInManageProduct,
} = product.actions;
export default product.reducer;