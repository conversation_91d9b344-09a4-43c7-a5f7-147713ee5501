import { createApi } from '@reduxjs/toolkit/query/react';
import { baseQuery } from './api';


export const sessionApi = createApi({
  reducerPath: 'sessionApi',
  baseQuery,
  tagTypes: ['Session'],
  endpoints: (build) => ({
    getAllInspections: build.query({
      query: (query) => ({
        url: '/allInspections',
        method: 'GET',
        params: query,
      }),
      transformResponse: (response, meta) => {
        // include page count
        const headers = meta.response.headers;
        
        return {
          data: response,
          pageCount: headers.get('page-count'),
        };
      },
      providesTags: ['Session'],
    }),
    getSessionInfo: build.query({
      // get session info by ipc_session_id
      query: (ipc_session_id) => ({
        url: '/session',
        method: 'GET',
        params: { ipc_session_id: ipc_session_id },
      }),
    }),
    getInspectionInfo: build.query({
      // get inspection info by product id
      query: (product_id) => ({
        url: '/inspection',
        method: 'GET',
        params: { product_id },
      }),
    }),
    getSessionStepInfo: build.query({
      query: (query) => ({
        url: '/sessionStepInfo',
        method: 'GET',
        params: query,
      }),
    }),
    annotateFeature: build.mutation({
      query: (body) => ({
        url: `/annotate`,
        method: 'PUT',
        body,
      }),
    }),
    submitGoodFeedbackForAll: build.mutation({
      query: (body) => ({
        url: '/quickAnnotate',
        method: 'PUT',
        body,
      }),
    }),
    replaySession: build.mutation({
      query: (body) => ({
        url: '/replaySession',
        method: 'POST',
        body,
      }),
    }),
    getAllSessions: build.query({
      query: (query) => ({
        url: '/allSessions',
        method: 'GET',
        params: query,
      }),
      transformResponse: (response, meta) => {
        // include page count
        const headers = meta.response.headers;
        
        return {
          data: response,
          pageCount: headers.get('page-count'),
        };
      },
      providesTags: ['Session'],
    }),
    getNeighborInspections: build.query({
      query: (query) => ({
        url: '/locateInspection',
        method: 'GET',
        params: query,
      }),
    }),
    deleteInspectionRecordByProductId: build.mutation({
      query: (product_id) => ({
        url: '/inspection',
        method: 'DELETE',
        params: { product_id },
      }),
    }),
    cancelAnnotation: build.mutation({
      query: (body) => ({
        url: '/annotate',
        method: 'DELETE',
        body,
      }),
    }),
  }),
});

export const {
  useGetAllInspectionsQuery,
  useLazyGetAllInspectionsQuery,
  useGetSessionInfoQuery,
  useLazyGetSessionInfoQuery,
  useGetSessionStepInfoQuery,
  useLazyGetSessionStepInfoQuery,
  useAnnotateFeatureMutation,
  useSubmitGoodFeedbackForAllMutation,
  useReplaySessionMutation,
  useLazyGetNeighborInspectionsQuery,
  useGetAllSessionsQuery,
  useLazyGetAllSessionsQuery,
  useGetInspectionInfoQuery,
  useLazyGetInspectionInfoQuery,
  useDeleteInspectionRecordByProductIdMutation,
  useCancelAnnotationMutation,
} = sessionApi;