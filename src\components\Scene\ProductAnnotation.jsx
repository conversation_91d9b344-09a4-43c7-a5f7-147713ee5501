import { ColorPicker, Dropdown, Input, InputNumber, Slider, Switch, Tooltip } from 'antd';
import _ from 'lodash';
import React, { Fragment, useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { setSelectedFeatureIdInManageProduct } from '../../actions/product';
import { setContainerWindowLoadingLocked } from '../../actions/setting';
import { ALERT_TYPES, aoiAlert } from '../../common/alert';
import { serverEndpoint } from '../../common/const';
import { debounce, handleRequestFailed, loadAndDecodePoints, sleep, translation } from '../../common/util';
import { useAddFeatureByProductIdAndStepMutation, useGetCustomFeaturesQuery, useRemoveFeatureByProductIdAndStepMutation, useUpdateFeatureByProductIdAndStepMutation } from '../../services/feature';
import { systemApi } from '../../services/system';
import ProductAnnotationViewer from '../common/viewer/ProductAnnotationViewer';
import ProductThreeDViewer from '../common/viewer/ProductThreeDViewer';
import RemoveFeatureConfirmation from '../modal/RemoveFeatureConfirmation';
import NewFeatureModal from './NewFeatureModal';
import ProductIgnoreMaskViewer from '../common/viewer/ProductIgnoreMaskViewer';


const AddFeatureModalDimension = {
  width: 322,
  height: 380,
};

const ProductAnnotation = (props) => {
  const {
    features,
    productId,
    step,
    initSelectedFeatureId,
    selectedFeatureId,
    selectedVariation,
    productInfo,
    viewRef,
    canvasRef,
    handleGetAllStepsFeatures,
    isEditingIgnoreMask,
    ignoreMaskViewerRef,
    ignoreMaskViewerCanvasRef,
    drawingMode,
    setDrawingMode,
    shouldAutoScrollToFeatureRef,
  } = props;

  const dispatch = useDispatch();

  const [componentsVisible, setComponentsVisible] = useState(true);
  const [displayMode, setDisplayMode] = useState('image'); // 'image' or 'pointcloud'
  // const displayMode = 'image';
  const [toolType, setToolType] = useState('select'); // 'select' 'drawBbox' 'erase'
  const [threeDViewer, setThreeDViewer] = useState(null);
  const [isRemoveFeatureConfirmationOpened, setIsRemoveFeatureConfirmationOpened] = useState(false);
  const [createFeaturePosition, setCreateFeaturePosition] = useState(null); // ex. { top: 0, left: 0 }
  const [curDisplayOptionsBrightness, setCurDisplayOptionsBrightness] = useState(50);
  const [curDisplayOptionsContrast, setCurDisplayOptionsContrast] = useState(50);
  const [curDisplayOptionsSaturation, setCurDisplayOptionsSaturation] = useState(50);
  const [isSharpnessEnabled, setIsSharpnessEnabled] = useState(false);

  const [pencilStrokeWidth, setPencilStrokeWidth] = useState(10);
  const [pencilStrokeColor, setPencilStrokeColor] = useState('#F4E76E');

  // const viewRef = useRef();
  // const canvasRef = useRef();
  const threeDCanvasRef = useRef();
  const curContainerRef = useRef();
  const curUpdateCanvasDimensionFunc = useRef();
  const twoDSceneInitialized = useRef(false);
  const threeDSceneInitialized = useRef(false);
  const curRemoveFeatureInfo = useRef(null);
  const curAddFeatureInfo = useRef(null); // { pMin, pMax }
  const newFeatureResizeHandlerRef = useRef(null);
  const curAddFeatureModelPosition = useRef(null);
  const createFeaturePositionRef = useRef(null);

  const [updateFeature] = useUpdateFeatureByProductIdAndStepMutation();
  const [removeFeature] = useRemoveFeatureByProductIdAndStepMutation();
  const [addFeature] = useAddFeatureByProductIdAndStepMutation();
  const { refetch: refetchCustomFeatures } = useGetCustomFeaturesQuery({ product_id: productId });

  const { data: systemMetadata } = useSelector((state) => systemApi.endpoints.getSystemMetadata.select()(state));

  const handleFeatureUpdate = async (body, systemMetadata, productId) => {
    dispatch(setContainerWindowLoadingLocked(true));
    const res = await updateFeature(body);
    if (res.error) {
      handleRequestFailed('updateFeature', res.error);
    } else {
      aoiAlert(translation('notification.success.featureUpdated'), ALERT_TYPES.COMMON_SUCCESS);
    }
    await sleep(500);
    handleGetAllStepsFeatures(systemMetadata, productId);
    dispatch(setContainerWindowLoadingLocked(false));
  };

  const handleRectClickInRemoveMode = ({ product_id, step, feature_id, variant }) => {
    if (!viewRef.current) return;
    curRemoveFeatureInfo.current = { product_id, step, feature_id, variant };
    setIsRemoveFeatureConfirmationOpened(true);
  };

  const handleRemoveFeature = async () => {
    if (_.isEmpty(curRemoveFeatureInfo.current) || !viewRef.current) return;
    dispatch(setContainerWindowLoadingLocked(true));
    viewRef.current.removeFeatureFromScene({
      feature_id: curRemoveFeatureInfo.current.feature_id,
      step: curRemoveFeatureInfo.current.step,
      product_id: curRemoveFeatureInfo.current.product_id,
    });
    const res = await removeFeature(curRemoveFeatureInfo.current);
    if (res.error) {
      handleRequestFailed('removeFeature', res.error);
    } else {
      aoiAlert(translation('notification.success.featureRemoved'), ALERT_TYPES.COMMON_SUCCESS);
      await sleep(500);
      handleGetAllStepsFeatures(systemMetadata, productId);
    }
    curRemoveFeatureInfo.current = null;
    dispatch(setSelectedFeatureIdInManageProduct(null));
    dispatch(setContainerWindowLoadingLocked(false));
  };

  const handleAddFeature = async (body) => {
    const {
      mouseX,
      mouseY,
      pMin,
      pMax,
    } = body; // mouse position relative to canvas
    // get canvas's top left corner position relative to window
    const canvasRect = canvasRef.current.getBoundingClientRect();
    setCreateFeaturePosition({
      top: _.min([mouseY + canvasRect.top, window.innerHeight - AddFeatureModalDimension.height]),
      left: _.min([mouseX + canvasRect.left, window.innerWidth - AddFeatureModalDimension.width]),
    });
    createFeaturePositionRef.current = { top: mouseY + canvasRect.top, left: mouseX + canvasRect.left };
    curAddFeatureInfo.current = { pMin, pMax };
    curAddFeatureModelPosition.current = { mouseX, mouseY };
  };

  useEffect(() => {
    if (!curContainerRef.current) return;
    if (displayMode !== 'pointcloud' || !threeDCanvasRef.current) {
      // dispose threeDViewer
      if (threeDViewer && curUpdateCanvasDimensionFunc.current) {
        console.log('dispose product three d viewer');
        threeDViewer.clearScene();
        threeDViewer.destroy();
        setThreeDViewer(null);
        window.removeEventListener('resize', curUpdateCanvasDimensionFunc.current);
      }
    }

    if (displayMode === 'image' && viewRef.current && canvasRef.current && !twoDSceneInitialized.current) {
      // const fetchAndLoadImage = async (productInfo, selectedVariation, features, componentsVisible, viewRef) => {
      //   if (_.isEmpty(selectedVariation) || _.isEmpty(_.find(_.get(productInfo, 'inspectables'), v => v.variant === selectedVariation))) return;
      //   const selectedInspectable = _.find(_.get(productInfo, 'inspectables'), v => v.variant === selectedVariation);
      //   if (_.isEmpty(_.get(selectedInspectable, 'color_map_uri'))) return;
      //   dispatch(setContainerWindowLoadingLocked(true));
      //   const tmp = await fetch(`${serverEndpoint}/data?data_uri=${_.get(selectedInspectable, 'color_map_uri')}`);
      //   const blob = await tmp.blob();
      //   const reader = new FileReader();
      //   reader.readAsDataURL(blob);
      //   const dataurl = await new Promise((resolve) => {
      //     reader.onload = (event) => resolve(event.target.result);
      //   });
      //   await viewRef.current.loadScene(
      //     dataurl,
      //     initSelectedFeatureId,
      //     () => dispatch(setContainerWindowLoadingLocked(false)),
      //   );
      //   if (!_.isEmpty(features)) {
      //     viewRef.current.loadFeatures(
      //       features,
      //       selectedVariation,
      //       componentsVisible,
      //     );
      //   }
      //   twoDSceneInitialized.current = true;
      //   threeDSceneInitialized.current = false;
      //   if (initSelectedFeatureId) dispatch(setSelectedFeatureIdInManageProduct(Number(initSelectedFeatureId)));
      // };
      // fetchAndLoadImage(productInfo, selectedVariation, features, componentsVisible, viewRef);
      // return;
    }

    if (displayMode === 'pointcloud' && threeDCanvasRef.current && !threeDSceneInitialized.current) {
      // init threeDViewer
      const viewer = new ProductThreeDViewer(
        threeDCanvasRef.current,
        curContainerRef.current.offsetHeight,
        curContainerRef.current.offsetWidth,
        () => ({ width: curContainerRef.current.offsetWidth, height: curContainerRef.current.offsetHeight }),
      );

      setThreeDViewer(viewer);

      const updateCanvasDimension = () => {
        if (!viewer || !curContainerRef.current) return;
        return { width: curContainerRef.current.offsetWidth, height: curContainerRef.current.offsetHeight };
      };

      curUpdateCanvasDimensionFunc.current = debounce(updateCanvasDimension, 300);

      window.addEventListener('resize', curUpdateCanvasDimensionFunc.current);

      const fetchAndLoadCloud = async (productInfo, selectedVariation) => {
        if (_.isEmpty(selectedVariation) || _.isEmpty(_.find(_.get(productInfo, 'inspectables'), v => v.variant === selectedVariation))) return;
        const selectedInspectable = _.find(_.get(productInfo, 'inspectables'), v => v.variant === selectedVariation);
        if (_.isEmpty(_.get(selectedInspectable, 'point_cloud_uri'))) return;
        dispatch(setContainerWindowLoadingLocked(true));
        // console.log(Date.now().valueOf(), 'start download point cloud');
        const pointNColor = await loadAndDecodePoints(`${serverEndpoint}/data?data_uri=${_.get(selectedInspectable, 'point_cloud_uri')}`);
        // console.log(Date.now().valueOf(), 'end download point cloud');
        viewer.loadScene(pointNColor);
        dispatch(setContainerWindowLoadingLocked(false));
      };
      fetchAndLoadCloud(productInfo, selectedVariation);

      threeDSceneInitialized.current = true;
      twoDSceneInitialized.current = false;
    }
  }, [
    displayMode,
    threeDViewer,
    features,
    selectedVariation,
    productInfo,
  ]);

  useEffect(() => {
    if (!twoDSceneInitialized.current) return;
    if (displayMode === 'image' && viewRef.current && canvasRef.current && !_.isEmpty(features) && !_.isEmpty(selectedVariation)) {
      // viewRef.current.loadFeatures(features, selectedVariation);
      // make sure the window lock is released
      dispatch(setContainerWindowLoadingLocked(false));
    }
  }, [features]);

  useEffect(() => {
    // refetch frame
    if (displayMode === 'image' && viewRef.current && canvasRef.current && twoDSceneInitialized.current && !_.isEmpty(productInfo) && !_.isEmpty(selectedVariation)) {
      // const fetchAndLoadImage = async (productInfo, selectedVariation, features, componentsVisible) => {
      //   if (_.isEmpty(_.get(productInfo, 'inspectables'))) return;
      //   if (_.isEmpty(_.find(_.get(productInfo, 'inspectables'), v => v.variant === selectedVariation))) return;
      //   const imageFrameUri = _.get(_.find(_.get(productInfo, 'inspectables'), v => v.variant === selectedVariation), 'color_map_uri');
      //   const tmp = await fetch(`${serverEndpoint}/data?data_uri=${imageFrameUri}`);
      //   const blob = await tmp.blob();
      //   const reader = new FileReader();
      //   reader.readAsDataURL(blob);
      //   const dataUrl = await new Promise((resolve) => {
      //     reader.onload = (event) => resolve(event.target.result);
      //   });
      //   viewRef.current.loadScene(
      //     dataUrl,
      //     features,
      //     null,
      //     null,
      //     selectedVariation,
      //     componentsVisible,
      //   );
      // };
      // fetchAndLoadImage(productInfo, selectedVariation, features, componentsVisible);
    }
  }, [
    productInfo,
    selectedVariation,
  ]);

  useEffect(() => {
    newFeatureResizeHandlerRef.current = () => {
      if (!canvasRef.current || !curAddFeatureModelPosition.current || !createFeaturePositionRef.current) return;
      const canvasRect = canvasRef.current.getBoundingClientRect();
      setCreateFeaturePosition({
        top: _.min([curAddFeatureModelPosition.current.mouseY + canvasRect.top, window.innerHeight - AddFeatureModalDimension.height]),
        left: _.min([curAddFeatureModelPosition.current.mouseX + canvasRect.left, window.innerWidth - AddFeatureModalDimension.width]),
      });
      createFeaturePositionRef.current = { top: curAddFeatureModelPosition.current.mouseY + canvasRect.top, left: curAddFeatureModelPosition.current.mouseX + canvasRect.left };
    };

    window.addEventListener('resize', newFeatureResizeHandlerRef.current);

    return () => {
      if (newFeatureResizeHandlerRef.current) window.removeEventListener('resize', newFeatureResizeHandlerRef.current);
    }
  }, []);

  return (
    <Fragment>
      <RemoveFeatureConfirmation
        isOpened={isRemoveFeatureConfirmationOpened}
        setIsOpened={setIsRemoveFeatureConfirmationOpened}
        handleRemoveFeature={handleRemoveFeature}
      />
      <div className='relative w-[100%] h-[100%] bg-[#000000]'>
        {/* add features modal starts */}
      { !_.isEmpty(createFeaturePosition) &&
          <div className='absolute top-0 left-0 w-full h-full z-[19] bg-black bg-opacity-50'>
            <NewFeatureModal
              productId={productId}
              step={step}
              setCreateFeaturePosition={setCreateFeaturePosition}
              createFeaturePositionRef={createFeaturePositionRef}
              createFeaturePosition={createFeaturePosition}
              AddFeatureModalDimension={AddFeatureModalDimension}
              systemMetadata={systemMetadata}
              handleAddFeature={({ featureType, featureParam, featureScope, lineItemParams }) => {
                const addFeatureAsync = async ({ featureType, featureParam, featureScope, selectedVariation, lineItemParams }) => {
                  if (_.isEmpty(selectedVariation)) {
                    aoiAlert(translation('notification.error.selectAVariantionFirst'), ALERT_TYPES.COMMON_ERROR);
                    return;
                  }
                  if (!_.isEmpty(curAddFeatureInfo.current)) {
                    // check if the roi is valid
                    if (_.isEmpty(curAddFeatureInfo.current.pMin) || _.isEmpty(curAddFeatureInfo.current.pMax)) {
                      viewRef.current.unlockScene();
                      viewRef.current.removeCurRect();
                      dispatch(setContainerWindowLoadingLocked(false));
                      return;
                    }
                    // check if pmin and pmax are valid
                    if (curAddFeatureInfo.current.pMin.x >= curAddFeatureInfo.current.pMax.x || curAddFeatureInfo.current.pMin.y >= curAddFeatureInfo.current.pMax.y) {
                      viewRef.current.unlockScene();
                      viewRef.current.removeCurRect();
                      dispatch(setContainerWindowLoadingLocked(false));
                      return;
                    }
                    dispatch(setContainerWindowLoadingLocked(true));
                    const res = await addFeature({
                      product_id: Number(productId),
                      step: Number(step),
                      roi: {
                        type: 'aabb',
                        points: [
                          curAddFeatureInfo.current.pMin,
                          curAddFeatureInfo.current.pMax,
                        ]
                      },
                      feature_type: featureType,
                      // feature_param: featureParam,
                      feature_scope: featureScope,
                      variant: selectedVariation,
                      line_item_params: lineItemParams,
                    });
                    if (res.error) {
                      handleRequestFailed('addFeature', res.error);
                    } else {
                      await sleep(500);
                      aoiAlert(translation('notification.success.featureAdded'), ALERT_TYPES.COMMON_SUCCESS);
                      refetchCustomFeatures();
                      handleGetAllStepsFeatures(systemMetadata, productId);
                    }
                    if (viewRef.current) {
                      viewRef.current.unlockScene();
                      viewRef.current.removeCurRect();
                      viewRef.current.setMode('select');
                      setToolType('select');
                    }
                    dispatch(setContainerWindowLoadingLocked(false));
                  }
                  curAddFeatureInfo.current = null;
                };
                addFeatureAsync({ featureType, featureParam, featureScope, selectedVariation, lineItemParams });
              }}
              handleCancel={() => {
                if (viewRef.current) {
                  viewRef.current.unlockScene();
                  viewRef.current.removeCurRect();
                }
                curAddFeatureInfo.current = null;
              }}
            />
          </div>
        }
        {/* add features modal ends */}
        {/* floating header buttons start */}
        <div className='absolute top-4 left-4 z-[10]'>
          <div className='flex items-center gap-2 self-stretch'>
            <div className='flex px-2 py-1 gap-2 items-center rounded-[4px] shadow-[0px 2px 4px rgba(0, 0, 0, 0.1)]' style={{ background: 'rgba(0, 0, 0, 0.70)' }}>
              <Switch
                size='small'
                checked={componentsVisible}
                onChange={() => {
                  if (componentsVisible && viewRef.current) {
                    // hide rects in viewer
                    viewRef.current.toggleRectsVisibility(false);
                  } else if (!componentsVisible && viewRef.current) {
                    // show rects in viewer
                    viewRef.current.toggleRectsVisibility(true);
                  }
                  setComponentsVisible(!componentsVisible)
                }}
              />
              <span className='font-source text-[12px] font-normal'>
                {
                  componentsVisible ? translation('productAnnotation.hideComponent', { componentCount: features ? String(features.length) : '0' })
                  :
                  translation('productAnnotation.showComponent', { componentCount: features ? String(features.length) : '0' })
                }
              </span>
            </div>
            { !_.isEmpty(_.get(_.find(_.get(productInfo, 'inspectables', []), i => i.variant === selectedVariation && i.step === Number(step)), 'point_cloud_uri')) &&
              <div className='flex px-2 py-1 gap-2 items-center rounded-[4px] shadow-[0px 2px 4px rgba(0, 0, 0, 0.1)]' style={{ background: 'rgba(0, 0, 0, 0.70)' }}>
                <Switch
                  size='small'
                  checked={displayMode === 'pointcloud'}
                  onChange={() => setDisplayMode(displayMode === 'pointcloud' ? 'image' : 'pointcloud')}
                />
                <span className='font-source text-[12px] font-normal'>
                  {translation('productAnnotation.showPointcloud')}
                  {/* {displayMode === 'pointcloud' ? translation('productAnnotation.showImage') : translation('productAnnotation.showPointcloud')} */}
                </span>
              </div>
            }
          </div>
        </div>
        {/* floating header buttons end */}
        {/* scene display starts */}
        <div className={`w-full h-full absolute top-0 left-0 z-[${isEditingIgnoreMask ? '2' : '3'}] bg-[#000]`}>
          <div className='relative w-full h-full' ref={curContainerRef}>
            { displayMode === 'image' &&
              // <div className={`absolute w-full h-full ${displayMode === 'image' ? 'z-[2]' : 'z-[1]'}`}>
              <div className={`absolute w-full h-full`}>
                <ProductAnnotationViewer
                  displayCanvasRef={canvasRef}
                  ref={viewRef}
                  componentsVisible={componentsVisible}
                  handleSelectFeature={(featureId) => {
                    dispatch(setSelectedFeatureIdInManageProduct(featureId))
                    shouldAutoScrollToFeatureRef.current = true;
                  }}
                  handleModifyFeature={(payload) => handleFeatureUpdate(payload, systemMetadata, productId)}
                  handleRectClickInRemoveMode={({ product_id, feature_id, step, variant }) => handleRectClickInRemoveMode({ product_id, feature_id, step, variant })}
                  handleAddFeature={handleAddFeature}
                  curDisplayOptionsBrightness={curDisplayOptionsBrightness}
                  curDisplayOptionsContrast={curDisplayOptionsContrast}
                  curDisplayOptionsSaturation={curDisplayOptionsSaturation}
                  selectedFeatureId={selectedFeatureId}
                  isSharpnessEnabled={isSharpnessEnabled}
                  uri={_.get(_.find(_.get(productInfo, 'inspectables'), i => i.variant === selectedVariation && i.step === Number(step)), 'color_map_uri')}
                  features={features}
                  selectedVariation={selectedVariation}
                  initSelectedFeatureId={initSelectedFeatureId}
                />
              </div>
            }
            { displayMode === 'pointcloud' &&
              // <div className={`absolute w-full h-full ${displayMode === 'pointcloud' ? 'z-[2]' : 'z-[1]'}`}>
              <div className={`absolute w-full h-full`}>
                <canvas ref={threeDCanvasRef} />
              </div>
            }
          </div>
        </div>
        {/* scene display ends */}
        {/* ignore mask edit starts */}
        <div className={`w-full h-full bg-[#000] absolute top-0 left-0 z-[${isEditingIgnoreMask ? '3' : '2'}]`}>
          <ProductIgnoreMaskViewer
            curDisplayOptionsBrightness={curDisplayOptionsBrightness}
            curDisplayOptionsContrast={curDisplayOptionsContrast}
            curDisplayOptionsSaturation={curDisplayOptionsSaturation}
            ref={ignoreMaskViewerRef}
            displayCanvasRef={ignoreMaskViewerCanvasRef}
            selectedFeature={_.find(features, f => f.feature_id === Number(selectedFeatureId))}
            pencilStrokeWidth={pencilStrokeWidth}
            drawingMode={drawingMode}
            pencilStrokeColor={pencilStrokeColor}
          />
        </div>
        {/* ignore mask edit ends */}
        {/* mask drawing controllers starts */}
        <div className={`absolute bottom-4 left-4 ${isEditingIgnoreMask ? 'z-[3]' : 'z-[1]'}`}>
          <div
            className='flex w-[232px] py-2 px-4 flex-col items-start gap-2 rounded-[4px]'
            style={{
              background: 'rgba(0, 0, 0, 0.70)',
              boxShadow: '0px 0px 20px 0px rgba(0, 0, 0, 0.25)'
            }}
          >
            <div className='flex items-start gap-1 self-stretch'>
              <div className='flex p-0.5 items-center'>
                <img className='w-4 h-4' src='/img/icn/icn_info_white.svg' alt='info' />
              </div>
              <div className='flex items-center gap-1 self-stretch flex-wrap'>
                <span className='font-source text-[12px] font-bold'>
                  {drawingMode === 'pencil' && translation('viewInspection.pencilTool')}
                  {drawingMode === 'eraser' && translation('viewInspection.eraserTool')}
                  {drawingMode === 'pan' && translation('viewInspection.panTool')}
                </span>
                <span className='font-source text-[12px] font-normal'>
                  {drawingMode === 'pencil' && translation('viewInspection.pencilToolDesc')}
                  {drawingMode === 'eraser' && translation('viewInspection.eraserToolDesc')}
                  {drawingMode === 'pan' && translation('viewInspection.panToolDesc')}
                </span>
              </div>
            </div>
            <div className='flex px-6 items-start gap-2 self-stretch'>
              <div
                className={`flex w-8 h-8 items-center justify-center cursor-pointer ${drawingMode === 'pan' ? 'bg-[#2D2D2D]' : 'bg-[#000000]'} rounded-[4px]`}
                onClick={() => setDrawingMode('pan')}
              >
                {drawingMode === 'pan' ?
                  <img className='w-3 h-3' src='/img/icn/icn_cursorPointe_blue.svg' alt='pencil' />
                  : <img className='w-3 h-3' src='/img/icn/icn_cursorPointer_white.svg' alt='pencil' />
                }
              </div>
              <div
                className={`flex w-8 h-8 items-center justify-center cursor-pointer ${drawingMode === 'pencil' ? 'bg-[#2D2D2D]' : 'bg-[#000000]'} rounded-[4px]`}
                onClick={() => setDrawingMode('pencil')}
              >
                {drawingMode === 'pencil' ?
                  <img className='w-3 h-3' src='/img/icn/icn_pencil_blue.svg' alt='pencil' />
                  : <img className='w-3 h-3' src='/img/icn/icn_pencil_white.svg' alt='pencil' />
                }
              </div>
              <div
                className={`flex w-8 h-8 items-center justify-center cursor-pointer ${drawingMode === 'eraser' ? 'bg-[#2D2D2D]' : 'bg-[#000000]'} rounded-[4px]`}
                onClick={() => setDrawingMode('eraser')}
              >
                {drawingMode === 'eraser' ?
                  <img className='w-3 h-3' src='/img/icn/icn_erase_blue.svg' alt='eraser' />
                  : <img className='w-3 h-3' src='/img/icn/icn_erase_white.svg' alt='eraser' />
                }
              </div>
              <div className='flex w-8 h-8 items-center justify-center cursor-pointer'
                onClick={() => {
                  if (ignoreMaskViewerRef.current) ignoreMaskViewerRef.current.resetView();
                }}
              >
                <img className='w-3 h-3' src='/img/icn/icn_resetZoom_white.svg' alt='pencil' />
              </div>
              <Dropdown
                overlay={<InferenceImageDisplayOptions
                  curDisplayOptionsBrightness={curDisplayOptionsBrightness}
                  setCurDisplayOptionsBrightness={setCurDisplayOptionsBrightness}
                  curDisplayOptionsContrast={curDisplayOptionsContrast}
                  setCurDisplayOptionsContrast={setCurDisplayOptionsContrast}
                  curDisplayOptionsSaturation={curDisplayOptionsSaturation}
                  setCurDisplayOptionsSaturation={setCurDisplayOptionsSaturation}
                  isSharpnessEnabled={isSharpnessEnabled}
                  setIsSharpnessEnabled={setIsSharpnessEnabled}
                />}
                placement='topLeft'
              >
                <div className='flex w-8 h-8 items-center justify-center'>
                  <img className='w-3 h-3' src='/img/icn/icn_setting_white.svg' alt='reset' />
                </div>
              </Dropdown>
            </div>
            <div className='w-full h-[1px] bg-[#333]' />
            <span className='font-source text-[12px] font-semibold self-stretch'>
              {translation('viewInspection.stroke')}
            </span>
            <div className='flex flex-col items-start self-stretch'>
              <div className='flex py-0.5 items-center gap-2 self-stretch'>
                <div className='flex w-6 h-6 items-center justify-center gap-2.5'>
                  <img src='/img/icn/icn_zond_white.svg' alt='zond' className='w-3 h-3' />
                </div>
                <InputNumber
                  controls={false}
                  min={0.01}
                  step={0.01}
                  max={100}
                  size='small'
                  style={{ width: '84px' }}
                  value={pencilStrokeWidth}
                  onChange={(value) => setPencilStrokeWidth(value)}
                />
                <Slider
                  min={0.01}
                  max={100}
                  step={0.01}
                  style={{ width: '84px' }}
                  value={pencilStrokeWidth}
                  onChange={(value) => setPencilStrokeWidth(value)}
                />
              </div>
              <div className='flex py-0.5 items-center gap-2 self-stretch'>
                <div className='flex w-6 h-6 items-center justify-center gap-2.5'>
                  <ColorPicker
                    size='small'
                    value={pencilStrokeColor}
                    onChange={(color) => {
                      setPencilStrokeColor(color.toHexString());
                    }}
                  />
                </div>
                <Input
                  size='small'
                  style={{ width: '76px' }}
                  disabled
                  value={pencilStrokeColor}
                />
              </div>
            </div>
          </div>
        </div>
        {/* mask drawing controllers ends */}
        {/* floating bottom buttons starts */}
        <div className={`absolute bottom-4 right-4 ${isEditingIgnoreMask ? 'z-[2]' : 'z-[3]'}`}>
          <div className='flex items-center h-[60px] self-stretch gap-2'>
            <div
              className='inline-flex py-2 px-4 flex-col items-start gap-2 rounded-[4px] shadow'
              style={{ background: 'rgba(0, 0, 0, 0.70)' }}
            >
              <div className='flex items-start gap-1'>
                <div className='flex p-0.5 items-center'>
                  <img src='/img/icn/icn_info_white.svg' className='w-3 h-3' alt='info' />
                </div>
                <span className='font-source text-[12px] font-[700px]'>
                  {toolType === 'select' && translation('productAnnotation.selectToolDesc')}
                  {toolType === 'drawBbox' && translation('productAnnotation.drawBboxToolDesc')}
                  {toolType === 'erase' && translation('productAnnotation.eraseToolDesc')}
                  {toolType === 'translate' && translation('productAnnotation.translateToolDesc')}
                </span>
              </div>
              <div className='flex items-start gap-4 self-stretch'>
                <div className={`rounded-[4px] flex w-6 h-6 justify-center items-center cursor-pointer 
                ${toolType !== 'translate' && 'hover:bg-AOI-blue transition-all'}`} onClick={() => {
                  setToolType('translate');
                  if (viewRef.current) viewRef.current.setMode('translate');
                }}>
                  {toolType !== 'translate' ?
                    <img src='/img/icn/icn_backHand_white.svg' className='w-[14px] h-[14px]' alt='cursor' />
                    :
                    <img src='/img/icn/icn_backHand_blue.svg' className='w-[14px] h-[14px]' alt='cursor' />
                  }
                </div>
                <div className={`rounded-[4px] flex w-6 h-6 justify-center items-center cursor-pointer 
                ${toolType !== 'select' && 'hover:bg-AOI-blue transition-all'}`} onClick={() => {
                  setToolType('select');
                  if (viewRef.current) viewRef.current.setMode('select');
                }}>
                  {toolType !== 'select' ?
                    <img src='/img/icn/icn_cursorPointer_white.svg' className='w-[8.75px] h-[12.25px]' alt='cursor' />
                    :
                    <img src='/img/icn/icn_cursorPointe_blue.svg' className='w-[8.75px] h-[12.25px]' alt='cursor' />
                  }
                </div>
                <div className={`flex w-6 h-6 justify-center items-center cursor-pointer rounded-[4px] 
                ${toolType !== 'erase' && 'hover:bg-AOI-blue transition-all'}`} onClick={() => {
                  setToolType('erase');
                  if (viewRef.current) viewRef.current.setMode('eraseObject');
                }}>
                  {toolType !== 'erase' ?
                    <img src='/img/icn/icn_erase_white.svg' className='w-[12.498px] h-[12px] shrink-0' alt='erase' />
                  :
                    <img src='/img/icn/icn_erase_blue.svg' className='w-[12.498px] h-[12px] shrink-0' alt='erase' />
                  }
                </div>
                <div className={`flex w-6 h-6 justify-center items-center cursor-pointer rounded-[4px] 
                ${toolType !== 'drawBbox' && 'hover:bg-AOI-blue transition-all'}`} onClick={() => {
                  if (_.isEmpty(selectedVariation)) {
                    aoiAlert(translation('notification.error.selectAVariantionFirst'), ALERT_TYPES.COMMON_ERROR);
                    return;
                  }
                  setToolType('drawBbox');
                  if (viewRef.current) viewRef.current.setMode('draw');
                }}>
                  {toolType !== 'drawBbox' ?
                    <img src='/img/icn/icn_crossHair_white.svg' className='w-[12px] h-[12px] shrink-0' alt='drawBbox' />
                  :
                    <img src='/img/icn/icn_crossHair_blue.svg' className='w-[12px] h-[12px] shrink-0' alt='drawBbox' />
                  }
                </div>
                <div
                  className='flex w-6 h-6 justify-center items-center cursor-pointer hover:bg-AOI-blue transition-all rounded-[4px]'
                  onClick={() => {
                    if (viewRef.current) viewRef.current.resetView();
                  }}
                >
                  <img src='/img/icn/icn_resetZoom_white.svg' className='w-3 h-3 shrink' alt='resetZoom' />
                </div>
                <Dropdown
                  overlay={<InferenceImageDisplayOptions
                    curDisplayOptionsBrightness={curDisplayOptionsBrightness}
                    setCurDisplayOptionsBrightness={setCurDisplayOptionsBrightness}
                    curDisplayOptionsContrast={curDisplayOptionsContrast}
                    setCurDisplayOptionsContrast={setCurDisplayOptionsContrast}
                    curDisplayOptionsSaturation={curDisplayOptionsSaturation}
                    setCurDisplayOptionsSaturation={setCurDisplayOptionsSaturation}
                    isSharpnessEnabled={isSharpnessEnabled}
                    setIsSharpnessEnabled={setIsSharpnessEnabled}
                  />}
                  placement='topLeft'
                >
                  <div
                    className='flex w-6 h-6 justify-center items-center'
                  >
                    <img src='/img/icn/icn_setting_white.svg' className='w-3 h-3 shrink' alt='setting' />
                  </div>
                </Dropdown>
              </div>
            </div>
            {/* <div
              className='inline-flex items-center gap-2 rounded-[4px] shadow px-4 py-2 h-[60px]'
              style={{ background: 'rgba(0, 0, 0, 0.70)' }}
            >
              <div
                className='flex w-8 h-8 justify-center items-center cursor-pointer'
                onClick={() => {

                }}
              >
                <img src='/img/icn/icn_zoomIn_white.svg' className='w-3 h-3 shrink' alt='zoomIn' />
              </div>
              <div
                className='flex w-8 h-8 justify-center items-center cursor-pointer'
                onClick={() => {
                  
                }}
              >
                <img src='/img/icn/icn_zoomOut_white.svg' className='w-3 h-3 shrink' alt='zoomOut' />
              </div>
              <div
                className='flex w-8 h-8 justify-center items-center cursor-pointer'
                onClick={() => {
                  if (viewRef.current) viewRef.current.resetView();
                }}
              >
                <img src='/img/icn/icn_resetZoom_white.svg' className='w-3 h-3 shrink' alt='resetZoom' />
              </div>
              <div
                className='flex justify-center items-center gap-1'
                onClick={() => {
                  
                }}
              >
                <div className='flex w-8 h-8 justify-center items-center'>
                  <img src='/img/icn/icn_locator_white.svg' className='w-3 h-3' alt='locator' />
                </div>
                <span className='font-source text-[12px] font-normal whitespace-nowrap'>
                  zoom %
                </span>
              </div>
            </div> */}
          </div>
        </div>
        {/* floating bottom buttons ends */}
      </div>
    </Fragment>
  );
};

const InferenceImageDisplayOptions = (props) => {
  const {
    curDisplayOptionsBrightness,
    setCurDisplayOptionsBrightness,
    curDisplayOptionsContrast,
    setCurDisplayOptionsContrast,
    curDisplayOptionsSaturation,
    setCurDisplayOptionsSaturation,
    isSharpnessEnabled,
    setIsSharpnessEnabled,
  } = props;

  return (
    <div className='flex p-2 flex-col gap-2 w-[220px] bg-[#2D2D2D] rounded-[4px]'>
      <div className='flex justify-between items-center gap-2'>
        <span className='font-source text-[12px] font-semibold'>
          {translation('viewInspection.displayOptions')}
        </span>
        <div
          className='flex items-center justify-center h-8 w-8 cursor-pointer'
          onClick={() => {
            setCurDisplayOptionsBrightness(50);
            setCurDisplayOptionsContrast(50);
            setCurDisplayOptionsSaturation(50);
            setIsSharpnessEnabled(false);
          }}
        >
          <img src='/img/icn/icn_reset_white.svg' alt='reset' className='w-3 h-3' />
        </div>
      </div>
      <div className='flex flex-col self-stretch'>
        <span className='font-source text-[12px] font-normal'>
          {translation('viewInspection.brightness')}
        </span>
        <Slider
          value={curDisplayOptionsBrightness}
          min={0}
          max={100}
          step={1}
          onChange={(value) => setCurDisplayOptionsBrightness(value)}
        />
      </div>
      <div className='flex flex-col self-stretch'>
        <span className='font-source text-[12px] font-normal'>
          {translation('viewInspection.contrast')}
        </span>
        <Slider
          value={curDisplayOptionsContrast}
          min={0}
          max={100}
          step={1}
          onChange={(value) => setCurDisplayOptionsContrast(value)}
        />
      </div>
      <div className='flex flex-col self-stretch'>
        <span className='font-source text-[12px] font-normal'>
          {translation('viewInspection.saturation')}
        </span>
        <Slider
          value={curDisplayOptionsSaturation}
          min={0}
          max={100}
          step={1}
          onChange={(value) => setCurDisplayOptionsSaturation(value)}
        />
      </div>
      <div className='flex flex-col self-stretch'>
        <div className='flex items-center gap-2'>
          <span className='font-source text-[12px] font-normal'>
            {translation('viewInspection.sharpness')}
          </span>
          <Tooltip
            title={translation('viewInspection.sharpnessDesc')}
          >
            <img src='/img/icn/icn_info_white.svg' alt='info' className='w-3 h-3' />
          </Tooltip>
        </div>
        <div className='flex items-center justify-between'>
          <Switch
            size='small'
            checked={isSharpnessEnabled}
            onChange={(checked) => setIsSharpnessEnabled(checked)}
          />
        </div>
      </div>
    </div>
  );
};

export default ProductAnnotation;