import React, { Fragment, useEffect, useRef, useState } from 'react';
import { getCameraCountByCameraViewLayout, getQueryParams, handleRequestFailed, translation } from '../../common/util';
import { AOIGreenButtonConfig, AOIGreenPrimaryButtonConfig } from '../../common/darkModeComponents';
import { Button } from 'antd';
import { useGetProductByIdQuery, useRunDetectionPipelineMutation } from '../../services/product';
import CaptureForNewProdCameraSetting from '../modal/CaptureForNewProdCameraSetting';
import { useLazyGetCameraCaptureFrameQuery } from '../../services/camera';
import _ from 'lodash';
import ProductCameraSettingFragment from '../Camera/ProductCameraSettingFragment';
import { useDispatch, useSelector } from 'react-redux';
import { setContainerWindowLoadingLocked, setCurTrainingTaskStartTime, setIsTrainingRuning } from '../../actions/setting';
import { systemApi } from '../../services/system';
import DefineProduct from './DefineProrduct';
import MainMenuLayout from '../layout/MainMenuLayout';
import RetrainConfirmation from '../modal/RetrainConfirmation';
import { useModelUpdateTriggerMutation } from '../../services/model';
import { retrainModelType } from '../../common/const';


const NewProduct = (props) => {
  const { productId } = props.match.params;

  const dispatch = useDispatch();

  const { search } = props.location;
  const searchParams = getQueryParams(search);
  const {
    detectionStep, // detectionStep is not integer then it's a full product view
    editStep, // edit 0: camera setup, 1: define product
  } = searchParams;

  const captureCount = useRef([]);
  const initProductInfoChecked = useRef(false);
  const initCaptureReminderChecked = useRef(false);

  // const [curStep, setCurStep] = useState(0); // 0: camera setup, 1: define product
  const [isCameraSettingCaptureConfirmOpened, setIsCameraSettingCaptureConfirmOpened] = useState(false);
  const [isRegisterProdConfirmOpened, setIsRegisterProdConfirmOpened] = useState(false);
  const [captureFrames, setCaptureFrames] = useState({}); // { 'cameraId': { imageUri: '', pointCloudUri: '' } }
  const [threeDSceneViewer, setThreeDSceneViewer] = useState(null);
  const [isRetrainComfirmationModalOpened, setIsRetrainComfirmationModalOpened] = useState(false);

  const { data: productInfo, refetch: refetchProductInfo } = useGetProductByIdQuery(productId);
  const [triggerCapture] = useLazyGetCameraCaptureFrameQuery();
  const [productRegister] = useRunDetectionPipelineMutation();
  const [retrainTrigger] = useModelUpdateTriggerMutation();

  const { data: systemMetadata } = useSelector((state) => systemApi.endpoints.getSystemMetadata.select()(state));

  const handleCaptureAll = async (productId, systemMetadata) => {
    // clear the 3d scene first to free up memory
    if (threeDSceneViewer) {
      threeDSceneViewer.clearScene();
    }

    captureCount.current = [];
    dispatch(setContainerWindowLoadingLocked(true));
    const captureSingleCam = async (cameraId, productId) => {
      const res = await triggerCapture({ camera_id: cameraId, product_id: productId });
      if (_.get(res, 'error')) {
        handleRequestFailed('triggerCapture', _.get(res, 'error'));
        captureCount.current.push(cameraId);
        if (captureCount.current.length === getCameraCountByCameraViewLayout(_.get(systemMetadata, 'camera_view_layout'))) {
          dispatch(setContainerWindowLoadingLocked(false));
        }
        return;
      }

      captureCount.current.push(cameraId);
      if (captureCount.current.length === getCameraCountByCameraViewLayout(_.get(systemMetadata, 'camera_view_layout'))) {
        dispatch(setContainerWindowLoadingLocked(false));
      }

      return { 'imageUri': _.get(res, 'data.image.data_uri'), 'pointCloudUri': _.get(res, 'data.point_cloud.data_uri') };
    };
    // console.log(new Date().valueOf(), 'start capture all');
    const cameraCount = getCameraCountByCameraViewLayout(_.get(systemMetadata, 'camera_view_layout'));
    // _.times(cameraCount, (i) => {
    //   captureSingleCam(i, productId);
    // });
    const newCaptureFrames = {};
    for (let i = 0; i < cameraCount; i++) {
      const curRes = await captureSingleCam(i, productId);
      newCaptureFrames[`${i}`] = curRes;
    }
    setCaptureFrames(newCaptureFrames);
    refetchProductInfo();
  };

  const productInitRegister = async (productId, variantName) => {
    dispatch(setContainerWindowLoadingLocked(true));

    const res = await productRegister({ product_id: productId, variant: variantName });
    if (res.error) {
      dispatch(setContainerWindowLoadingLocked(false));
      handleRequestFailed('productRegister', res.error);
      return;
    }
    await refetchProductInfo();
    dispatch(setContainerWindowLoadingLocked(false));
    props.history.push(`/aoi/edit-product/${productId}?editStep=1`);
  };

  const handleRetrainTrigger = async (startAfterSession, pid) => {
    const payload = {
      model_types: [retrainModelType.defectModel, retrainModelType.heightModel],
    };
    if (_.isInteger(pid)) payload.golden_product_id = pid;

    const res = await retrainTrigger(payload);
    if (_.get(res, 'error')) {
      handleRequestFailed('retrainModel', _.get(res, 'error'));
      dispatch(setContainerWindowLoadingLocked(false));
      return;
    }

    dispatch(setIsTrainingRuning(true));
    dispatch(setCurTrainingTaskStartTime(new Date().getTime()));
    return;
  };

  useEffect(() => {
    if (initProductInfoChecked.current) return;
    initProductInfoChecked.current = true;
    if (!_.isEmpty(productInfo) && _.isEmpty(_.get(productInfo, 'product_specific_sensor_config')) && editStep === '1') {
      // setCurStep(0);
      props.history.push(`/aoi/edit-product/${productId}?editStep=0`);
    }
  }, [productInfo]);

  useEffect(() => {
    if (initCaptureReminderChecked.current) return;
    initCaptureReminderChecked.current = true;
    setIsCameraSettingCaptureConfirmOpened(editStep === '0');
  }, [editStep]);

  return (
    <MainMenuLayout>
      {/* init camera capture reminder modal */}
      <CaptureForNewProdCameraSetting
        isOpened={isCameraSettingCaptureConfirmOpened}
        setIsOpened={setIsCameraSettingCaptureConfirmOpened}
        handleCapture={() => {
          handleCaptureAll(productId, systemMetadata);
        }}
      />
      {/* this is for first time product registration */}
      <CaptureForNewProdCameraSetting
        isOpened={isRegisterProdConfirmOpened}
        setIsOpened={setIsRegisterProdConfirmOpened}
        handleCapture={() => productInitRegister(Number(productId), _.get(productInfo, 'product_name'))}
      />
      <RetrainConfirmation
        isOpened={isRetrainComfirmationModalOpened}
        setIsOpened={setIsRetrainComfirmationModalOpened}
        handleRetrainTrigger={handleRetrainTrigger}
        defaultGoldenProductId={Number(productId)}
      />
      <div className='flex py-2 px-8 items-start flex-col flex-1 gap-2 self-stretch h-full bg-[#131313]'>
        <div className='flex h-[48px] py-2 justify-between items-center self-stretch'>
          <div className='flex items-center gap-6'>
            <div className='flex items-center px-2 gap-2'>
              <div
                className='flex w-6 h-6 cursor-pointer justify-center items-center gap-2.5 hover:bg-[#c4c4c44d] rounded-[4px] transition-all duration-300'
                onClick={() => props.history.push('/aoi/manage-product')}
              >
                <img src='/img/icn/icn_arrowLeft_white.svg' alt='back' className='w-[8px] h-[16px] shrink' />
              </div>
              <span className='font-source text-[20px] font-semibold'>
                {translation('newProduct.registerNewProduct')}
              </span>
            </div>
            <div className='flex items-center rounded-[24px] border-[1px] border-gray-2'>
              <div
                className='flex py-2 px-4 items-center gap-2 cursor-pointer hover:bg-[#ffffff26] rounded-[24px] transition-all duration-300'
                onClick={() => {
                  if (editStep !== '0') props.history.push(`/aoi/edit-product/${productId}?editStep=0`);
                }}
              >
                <div className={`flex w-[18px] h-[18px] justify-center items-center rounded-[24px] border-[1px] ${editStep === '0' ? 'border-AOI-blue' : 'border-gray-2'}`}>
                  <span className={`font-source text-[12px] font-normal ${editStep === '0' ? 'text-AOI-blue' : 'text-gray-2'}`}>
                    1
                  </span>
                </div>
                <div className='flex items-center gap-[10px]'>
                  <span className={`font-source text-[12px] font-normal ${editStep === '0' ? 'text-AOI-blue' : 'text-gray-2'}`}>
                    {translation('newProduct.setupCamera')}
                  </span>
                </div>
              </div>
              <img src='/img/icn/icn_right_gray.svg' className='w-[12px] self-stretch' alt='right' />
              <div
                className='flex py-2 px-4 items-center gap-2 cursor-pointer hover:bg-[#ffffff26] rounded-[24px] transition-all duration-300'
                onClick={() => {
                  if (editStep !== '1') props.history.push(`/aoi/edit-product/${productId}?editStep=1`);
                }}
              >
                <div className={`flex w-[18px] h-[18px] justify-center items-center rounded-[24px] border-[1px] ${editStep === '1' ? 'border-AOI-blue' : 'border-gray-2'}`}>
                  <span className={`font-source text-[12px] font-normal ${editStep === '1' ? 'text-AOI-blue' : 'text-gray-2'}`}>
                    2
                  </span>
                </div>
                <div className='flex items-center gap-[10px]'>
                  <span className={`font-source text-[12px] font-normal ${editStep === '1' ? 'text-AOI-blue' : 'text-gray-2'}`}>
                    {translation('newProduct.captureAndDefineGoldenProduct')}
                  </span>
                </div>
              </div>
            </div>
          </div>
          { editStep === '0' &&
            <div className='flex items-center gap-2'>
              <AOIGreenPrimaryButtonConfig>
                <Button onClick={() => {
                    handleCaptureAll(productId, systemMetadata);
                }}>
                  <div className='flex items-center gap-2'>
                    <span className='font-source text-[12px] font-semibold'>
                      {translation('newProduct.capture')}
                    </span>
                  </div>
                </Button>
              </AOIGreenPrimaryButtonConfig>
              <AOIGreenPrimaryButtonConfig>
                <Button onClick={() => {
                  if (_.isEmpty(_.get(productInfo, 'inspectables'))) {
                    // have not registered the product yet
                    setIsRegisterProdConfirmOpened(true);
                    return;
                  }
                  props.history.push(`/aoi/edit-product/${productId}?editStep=1`);
                }}>
                  <div className='flex items-center gap-2'>
                    <span className='font-source text-[12px] font-semibold'>
                      {translation('newProduct.nextStep')}
                    </span>
                  </div>
                </Button>
              </AOIGreenPrimaryButtonConfig>
            </div>
          }
          { editStep === '1' &&
            <div className='flex items-center gap-2'>
              <AOIGreenPrimaryButtonConfig>
                <Button onClick={() => {
                  props.history.push(`/aoi/edit-product/${productId}?editStep=0`);
                }}>
                  <div className='flex items-center gap-2'>
                    <span className='font-source text-[12px] font-semibold'>
                      {translation('common.prevStep')}
                    </span>
                  </div>
                </Button>
              </AOIGreenPrimaryButtonConfig>
              <AOIGreenPrimaryButtonConfig>
                <Button onClick={() => {
                  props.history.push('/aoi/manage-product');
                }}>
                  <div className='flex items-center gap-2'>
                    <span className='font-source text-[12px] font-semibold'>
                      {translation('common.finish')}
                    </span>
                  </div>
                </Button>
              </AOIGreenPrimaryButtonConfig>
              <AOIGreenButtonConfig>
                <Button
                  onClick={() => {
                    setIsRetrainComfirmationModalOpened(true);
                  }}
                >
                  <span className='font-source text-[12px] font-semibold'>
                    {translation('productAnnotation.retrainModel')}
                  </span>
                </Button>
              </AOIGreenButtonConfig>
            </div>
          }
        </div>
        { editStep === '0' &&
          <ProductCameraSettingFragment
            productId={productId}
            productInfo={productInfo}
            captureFrames={captureFrames}
            handleCaptureAll={handleCaptureAll}
            threeDSceneViewer={threeDSceneViewer}
            setThreeDSceneViewer={setThreeDSceneViewer}
            systemMetadata={systemMetadata}
            isCameraSettingCaptureConfirmOpened={isCameraSettingCaptureConfirmOpened}
            setIsCameraSettingCaptureConfirmOpened={setIsCameraSettingCaptureConfirmOpened}
            isRegisterProdConfirmOpened={isRegisterProdConfirmOpened}
            setIsRegisterProdConfirmOpened={setIsRegisterProdConfirmOpened}
          />
        }
        { editStep === '1' &&
          <DefineProduct
            productId={productId}
            productInfo={productInfo}
            stepNumber={detectionStep}
            refetchProductById={refetchProductInfo}
            location={props.location}
            history={props.history}
          />
        }
      </div>
    </MainMenuLayout>
  );
};

export default NewProduct;