import { But<PERSON>, <PERSON>lapse, ConfigProvider, Input, InputNumber, Menu, Modal, Table } from 'antd';
import _ from 'lodash';
import styled from 'styled-components';


export const primaryColor = '#56CCF2';
export const defaultGreen = '#57F2C4';
export const activeGreen = '#5effd0';
export const lightGray = '#868686';

export const hoverBlue = '#3C8EA9';
export const hoverGreen = '#5AAA6B';
export const hoverYellow = '#AAA14D';

// NOTE: or use antd's ConfigProvider
export const DarkInput = styled(Input)`
  && {
    color: ${(props) => props.textColor || '#fff'};
    background-color: ${(props) => props.bgColor || '#333'};
    border-color: ${(props) => props.borderColor || '#555'};

    &:hover {
      border-color: ${(props) => props.hoverBorderColor || '#56CCF2'};
    }

    &:focus {
      border-color: ${(props) => props.focusBorderColor || '#56CCF2'};
      box-shadow: 0 0 0 2px ${(props) => props.focusShadowColor || 'rgba(85, 85, 85, 0.2)'};
      background-color: ${(props) => props.bgColor || '#000'};
    }
  }
  &::placeholder {
    color: #fff; /* Change this to your desired color */
    opacity: 0.5; /* Change this to your desired opacity */
    font-family: 'Source Sans Pro';
    font-size: 12px;
    font-weight: 400;
    font-style: normal;
  }

  /* For better browser compatibility */
  &::-webkit-input-placeholder {
    color: ${(props) => props.placeholderColor || '#fff'};
    opacity: 0.5;
  }
  &::-moz-placeholder {
    color: ${(props) => props.placeholderColor || '#fff'};
    opacity: 0.5;
  }
  &:-ms-input-placeholder {
    color: ${(props) => props.placeholderColor || '#fff'};
    opacity: 0.5;
  }
  &:-moz-placeholder {
    color: ${(props) => props.placeholderColor || '#fff'};
    opacity: 0.5;
  }
`;

export const DarkButton = styled(Button)`
  && {
    color: ${(props) => {
      if (props.isPrimary) {
        return props.textColor || '#333';
      } else {
        return props.textColor || '#fff';
      }
    }} !important;
    font-family: 'Source Sans Pro';
    font-size: 12px;
    font-weight: 400;
    font-style: normal;
    background-color: ${(props) => {
      if (props.isPrimary) {
        return props.bgColor || '#56CCF2';
      } else {
        return props.bgColor || '#333';
      }
    }};
    border-color: ${(props) => props.borderColor || '#56CCF2'};
    
    &:hover {
      background-color: ${(props) => {
        if (props.isPrimary) {
          return props.hoverBgColor || '#8bd8f0';
        } else {
          return props.hoverBgColor || '#56CCF2';
        }
      }} !important;
      border-color: ${(props) => props.hoverBorderColor || '#333'} !important;
      color: ${(props) => props.hoverTextColor || '#333'} !important;
    }

    ${(props) => props.isActiveEnabled && `
      &:active {
        background-color: ${(props) => {
          if (props.isPrimary) {
            return props.activeBgColor || '#8bd8f0';
          } else {
            return props.activeBgColor || '#56CCF2';
          }
        }} !important;
        border-color: ${(props) => props.activeBorderColor || '#333'} !important;
        color: ${(props) => props.activeTextColor || '#333'} !important;
      }
    `}
  }
`;

export const DarkTable = styled(Table)`
  .ant-table {
    background: #1f1f1f;
    color: #ffffff;
  }

  .ant-table-thead > tr > th {
    background: #2f2f2f;
    color: #ffffff;
  }

  .ant-table-tbody > tr > td {
    background: #1f1f1f;
    color: #ffffff;
  }

  .ant-pagination-item {
    background: #2f2f2f;
    color: #ffffff;
  }

  .ant-pagination-item-active {
    background: #4f4f4f;
    color: #ffffff;
  }

  .ant-pagination-options,
  .ant-pagination-options * {
    color: #ffffff;
  }

  .ant-table-pagination.ant-pagination {
    color: #ffffff;
  }
`;

export const DarkModal = styled(Modal)`
  .ant-modal-root {
    z-index: 999;
  }
  .ant-modal-content {
    background: #1f1f1f;
    color: #ffffff;
    padding: 0;
  }

  .ant-modal-header {
    background: #2f2f2f;
    border-bottom: 1px solid ${(props) => props.primaryColor || '#56CCF2'};
    color: #ffffff;
    margin-bottom: 0;
    height: 56px;
    display: flex;
    align-items: center;
    padding: 16px;
  }

  .ant-modal-title {
    color: #ffffff;
  }

  .ant-modal-close {
    color: #ffffff;
  }

  .ant-modal-body {
    background: #1f1f1f;
    color: #ffffff;
    ${(props) => props.bodyBottomRounded && 'border-radius: 0 0 6px 6px;'}
  }

  .ant-modal-footer {
    background: #2f2f2f;
    margin-top: 0;
    padding: 16px 24px;
    border-top: 1px solid ${(props) => props.primaryColor || '#56CCF2'};
    border-radius: 0 0 6px 6px;
  }

  .ant-modal {
    padding-bottom: 0 !important;
  }
`;

export const DarkInputNumber = styled(InputNumber)`
  && {
    background-color: ${(props) => props.bgColor || '#333'};
    border-color: ${(props) => props.borderColor || '#555'};

    &:hover {
      border-color: ${(props) => props.hoverBorderColor || '#56CCF2'};
    }
    
    &:active {
      border-color: ${(props) => props.hoverBorderColor || '#56CCF2'};
      background-color: ${(props) => props.activeBgColor || '#000'};
      box-shadow: 0 0 0 2px ${(props) => props.activeShadowColor || 'rgba(85, 85, 85, 0.2)'};
    }

    &:focus-within {
      border-color: ${(props) => props.hoverBorderColor || '#56CCF2'};
      background-color: ${(props) => props.activeBgColor || '#000'};
      box-shadow: 0 0 0 2px ${(props) => props.activeShadowColor || 'rgba(85, 85, 85, 0.2)'};
    }

    &:focus {
      border-color: ${(props) => props.focusBorderColor || '#56CCF2'};
      box-shadow: 0 0 0 2px ${(props) => props.focusShadowColor || 'rgba(85, 85, 85, 0.2)'};
      background-color: ${(props) => props.bgColor || '#000'};
    }

    ::placeholder {
      color: #fff; /* Placeholder color */
      opacity: 0.5;
      font-family: 'Source Sans Pro';
      font-size: 12px;
      font-weight: 400;
      font-style: normal;
    }
  }

  .ant-input-number-input {
    color: ${(props) => props.textColor || '#fff'};
  } 
`;

export const DarkMenu = styled(Menu)`
  background: #333;
  color: #ffffff;
  background-color: #333 !important;

  .ant-dropdown-menu-item {
    background: #333;
    color: #ffffff;

    &:hover {
      background: #4f4f4f;
    }
  }

  .ant-dropdown-menu-item-selected {
    background: #4f4f4f;
  }

  .ant-dropdown-menu-item-divider {
    background: #444;
  }

  .ant-dropdown-menu-submenu-title {
    background: #333;
    color: #ffffff;

    &:hover {
      background: #4f4f4f;
    }
  }

  .ant-dropdown-menu-title-content {
    color: #ffffff;
  }

  .ant-dropdown-menu-item-active {
    background: #4f4f4f !important;
    background-color: #4f4f4f !important;
  }
`;

export const RoundLabel = styled.div`
  display: flex;
  padding: 4px 8px;
  align-items: center;
  gap: 4px;

  border-radius: 24px;
  background: ${(props) => props.bgColor || '#4F4F4F'};
  color: ${(props) => props.textColor || '#fff'};
`;

export const GreenButton = ({ text, style }) => {
  return (
    <DarkButton
      bgColor={defaultGreen}
      hoverBgColor={activeGreen}
      borderColor={defaultGreen}
      hoverBorderColor={activeGreen}
      hoverTextColor={'#333'}
      textColor={'#333'}
      style={{ ...style }}
    >
      {text}
    </DarkButton>
  );
};

// remove the padding in ant-collapse-content-box
export const CustomCollapse = styled(Collapse)`
  border-radius: 0 !important;
  .ant-collapse-content-box {
    padding: 0 !important;
  }
  .ant-collapse-header {
    padding: 8px 16px !important;
    border-radius: 0 !important;
  }
  .ant-collapse-header[aria-expanded="true"] {
    background: rgba(86, 204, 242, 0.10) !important;
    color: #56CCF2 !important;
  }
  .ant-collapse-header {
    &:hover {
      background: rgba(86, 204, 242, 0.10) !important;
    }
  }
  .ant-collapse-content {
    border-radius: 0 !important;
  }
  .ant-collapse-header-text {
    height: 22px;
  }
`;

export const AOIGreenButtonConfig = (props) => <ConfigProvider
  theme={{
    components: {
      Button: {
        defaultBg: '#333',
        defaultBorderColor: '#57F2C4',
        defaultColor: '#57F2C4',
        defaultHoverBg: '#57F2C4',
        defaultHoverColor: '#333',
        defaultHoverBorderColor: '#57F2C4',
        defaultActiveColor: '#57F2C4',
        defaultActiveBorderColor: '#57F2C4',
      }
    }
  }}
>
  {props.children}
</ConfigProvider>;

export const AOIGreenPrimaryButtonConfig = (props) => <ConfigProvider
  theme={{
    components: {
      Button: {
        defaultBg: '#57F2C4',
        defaultColor: '#333',
        defaultHoverBg: '#333',
        defaultHoverColor: '#57F2C4',
        defaultHoverBorderColor: '#57F2C4',
        defaultActiveBorderColor: '#57F2C4',
        defaultActiveColor: '#57F2C4',
        defaultActiveBg: '#333',
      }
    }
  }}
>
  {props.children}
</ConfigProvider>;

export const PrimaryButtonConfigProvider = ({ children }) => {
  return (
    <ConfigProvider
      theme={{
        components: {
          Button: {
            defaultBorderColor: primaryColor,
            defaultBg: primaryColor,
            defaultColor: '#333',
            defaultHoverBorderColor: primaryColor,
            defaultHoverBg: '#333',
            defaultHoverColor: primaryColor,
            defaultActiveBg: primaryColor,
            defaultActiveColor: '#333',
            defaultActiveBorderColor: primaryColor,
          }
        }
      }}
    >
      {children}
    </ConfigProvider>
  );
};

export const SecondaryButtonConfigProvider = ({ children }) => {
  return (
    <ConfigProvider
      theme={{
        components: {
          Button: {
            defaultBorderColor: '#333',
            defaultBg: '#131313',
            defaultColor: '#fff',
            defaultHoverBg: 'rgba(86, 204, 242, 0.20)',
            defaultActiveBg: 'rgba(86, 204, 242, 0.30)',
            defaultHoverColor: '#fff',
            defaultHoverBorderColor: '#333',
          }
        },
        token: {
          colorPrimary: 'rgba(86, 204, 242, 0.20)',
          colorPrimaryBgHover: 'rgba(86, 204, 242, 0.30) !important',
          colorPrimaryBg: 'rgba(86, 204, 242, 0.20) !important',
        }
      }}
    >
      {children}
    </ConfigProvider>
  );
};

export const RedPrimaryButtonConfigProvider = ({ children }) => {
  return (
    <ConfigProvider
      theme={{
        components: {
          Button: {
            defaultBorderColor: '#EB5757',
            defaultBg: '#EB5757',
            defaultColor: '#333',
            defaultHoverBg: '#333',
            defaultHoverColor: '#EB5757',
            defaultHoverBorderColor: '#EB5757',
            defaultActiveBg: '#333',
            defaultActiveColor: '#EB5757',
            defaultActiveBorderColor: '#EB5757',
          }
        }
      }}
    >
      {children}
    </ConfigProvider>
  );
};