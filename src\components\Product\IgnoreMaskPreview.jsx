import _ from 'lodash';
import React, { useEffect, useState } from 'react';
import { serverEndpoint } from '../../common/const';
import { mapRange } from '../../common/util';


const IgnoreMaskPreview = (props) => {
  const {
    feature,
    ignoreMaskPreviewReload,
  } = props;
  
  const [curIgnoreMaskSrc, setCurIgnoreMaskSrc] = useState(null);
  const [curCroppedColorMapSrc, setCurCroppedColorMapSrc] = useState(null);

  useEffect(() => {
    if (_.isEmpty(feature)) return;

    // fetech ignore mask and cropped color map
    const fetchAndLoad = async (feature) => {
      if (_.isEmpty(feature)) return;
      let croppedColorMapRes;
      let ignoreMaskRes;
      try {
        croppedColorMapRes = await fetch(`${serverEndpoint}/data?data_uri=${_.get(feature, 'cropped_color_map_uri')}`);
        ignoreMaskRes = await fetch(`${serverEndpoint}/data?data_uri=${_.get(feature, 'mask_uri')}`);
      } catch (e) {
        console.error('Failed to fetch ignore mask or cropped color map');
        return;
      }
      // convert and load cropped color map
      const croppedColorMapBlob = await croppedColorMapRes.blob();
      const reader = new FileReader();
      reader.readAsDataURL(croppedColorMapBlob);
      const croppedColorMapSrc = await new Promise((resolve) => {
        reader.onloadend = () => {
          resolve(reader.result);
        };
      });
      setCurCroppedColorMapSrc(croppedColorMapSrc);

      // convert and load ignore mask
      const blob = await ignoreMaskRes.blob();
      // convert to uint8 array
      const reader1 = new FileReader();
      reader1.readAsArrayBuffer(blob);
      const ignoreMaskArr = await new Promise((resolve, reject) => {
        reader1.onload = (event) => {
          resolve(new Uint8Array(event.target.result));
        };
      });
      const roi = _.get(feature, 'roi');
      const rgbaArray = [];
      for (let i = 0; i < ignoreMaskArr.length; i++) {
        // mapRange(255 - ignoreMaskArr[i], 0, 255, 0, 127);
        rgbaArray.push(235, 87, 87, mapRange(255 - ignoreMaskArr[i], 0, 255, 0, 127));
        // rgbaArray.push(235, 87, 87, 255 - ignoreMaskArr[i]);
      }
      const width = roi.points[1].x - roi.points[0].x + 1;
      const height = roi.points[1].y - roi.points[0].y + 1;
      const imageData = new ImageData(new Uint8ClampedArray(rgbaArray), width, height);
      const canvas = document.createElement('canvas');
      canvas.width = width;
      canvas.height = height;
      const ctx = canvas.getContext('2d');
      ctx.putImageData(imageData, 0, 0);
      const ignoreMaskSrc = canvas.toDataURL();
      setCurIgnoreMaskSrc(ignoreMaskSrc);
      canvas.remove();
    };

    fetchAndLoad(feature);
  }, [feature, ignoreMaskPreviewReload]);

  return (
    <div className='relative w-full h-full'>
      <div className='absolute top-0 left-0 w-full h-full z-[2]'>
        <img className='object-contain w-full h-full' src={curIgnoreMaskSrc} alt='ignore mask' />
      </div>
      <div className='absolute top-0 left-0 w-full h-full z-[1]'>
        <img className='object-contain w-full h-full' src={curCroppedColorMapSrc} alt='cropped color map' />
      </div>
    </div>
  );
};

export default IgnoreMaskPreview;