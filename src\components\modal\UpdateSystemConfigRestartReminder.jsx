import { Button, Modal } from 'antd';
import React from 'react';
import { translation } from '../../common/util';
import { PrimaryButtonConfigProvider } from '../../common/darkModeComponents';


const UpdateSystemConfigRestartReminder = (props) => {
  const {
    isOpened,
    setIsOpened,
    handleSubmit,
  } = props;

  return (
    <Modal
      open={isOpened}
      onCancel={() => setIsOpened(false)}
      title={<span className='font-source text-[16px] font-semibold'>
        {translation('editSystemConfig.restartReminder')}
      </span>}
      footer={<div className='flex items-center gap-2'>
        <Button
          style={{ width: '50%' }}
          onClick={() => setIsOpened(false)}
        >
          <span className='font-source text-[14px] font-normal'>
            {translation('common.cancel')}
          </span>
        </Button>
        <PrimaryButtonConfigProvider>
          <Button
            onClick={() => {
              handleSubmit();
              setIsOpened(false);
            }}
            style={{ width: '50%' }}
          >
            <span className='font-source text-[14px] font-normal'>
              {translation('common.confirm')}
            </span>
          </Button>
        </PrimaryButtonConfigProvider>
      </div>}
    >
      <span className='font-source text-[14px] font-normal'>
        {translation('editSystemConfig.restartReminderDesc')}
      </span>
    </Modal>
  )
};

export default UpdateSystemConfigRestartReminder;