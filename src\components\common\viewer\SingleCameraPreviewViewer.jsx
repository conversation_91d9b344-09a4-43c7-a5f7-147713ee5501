import _ from 'lodash';
import TwoDBaseViwer from './TwoDBaseViewer';
import { fabric } from 'fabric-with-erasing';
import React from 'react';
import { serverEndpoint } from '../../../common/const';


export default class SingleCameraPreviewViewer extends TwoDBaseViwer {
  constructor(props) {
    super(props, props.displayCanvasRef);
    this.displayCanvasRef = props.displayCanvasRef;
    this.scene = null; // 2d image
    this.fabricCanvas = null;
    this.sceneOriginalWidth = 0;
    this.sceneOriginalHeight = 0;
    this.containerRef = React.createRef();
  };

  componentDidMount() {
    if (!this.displayCanvasRef.current) return;
    // init fabric canvas
    const fabricCanvas = new fabric.Canvas(
      this.displayCanvasRef.current,
      {
        // Disable uniform scaling
        uniformScaling: false,
      }
    );
    fabricCanvas.skipOffscreen = true;

    // Enable Panning
    let isPanning = false;
    fabricCanvas.on('mouse:down', (opt) => {
      if (opt.target) {
        isPanning = false;
        return;
      }
      
      isPanning = true;
      fabricCanvas.selection = false;
      this.scene.moveTo(0);
    });

    fabricCanvas.on('mouse:move', (opt) => {
      if (isPanning && opt && opt.e) {
        fabricCanvas.setCursor('grab');
        const delta = new fabric.Point(opt.e.movementX, opt.e.movementY);
        fabricCanvas.relativePan(delta);
      }
    });

    fabricCanvas.on('mouse:up', () => {
      isPanning = false;
      fabricCanvas.setCursor('default');
      this.fabricCanvas.renderAll();
    });

    // Enable Zooming
    fabricCanvas.on('mouse:wheel', (opt) => {
      const delta = opt.e.deltaY;
      let zoom = fabricCanvas.getZoom();
      zoom *= 0.999 ** delta;
      if (zoom > 20) zoom = 20;
      if (zoom < 0.01) zoom = 0.01;
      fabricCanvas.zoomToPoint({ x: opt.e.offsetX, y: opt.e.offsetY }, zoom);
      opt.e.preventDefault();
      opt.e.stopPropagation();
    });
    
    this.fabricCanvas = fabricCanvas;

    const {
      cameraId,
      cameraPreviewImageData,
      dataUri,
    } = this.props;
    
    if (!_.isEmpty(_.get(cameraPreviewImageData, `${cameraId}.imageUri`))) {
      this.loadScene(
        cameraPreviewImageData[cameraId].imageUri,
        this.containerRef.current.offsetWidth,
        this.containerRef.current.offsetHeight
      );
    } else if (!_.isEmpty(dataUri)) {
      const fetchAndLoad = async () => {
        const response = await fetch(`${serverEndpoint}/data?data_uri=${encodeURIComponent(dataUri.replace(/\\/g, '/'))}`);
        const blob = await response.blob();
        const reader = new FileReader();
        reader.readAsDataURL(blob);
        reader.onload = (event) => {
          this.loadScene(
            event.target.result,
            this.containerRef.current.offsetWidth,
            this.containerRef.current.offsetHeight
          );
        };
      };
      fetchAndLoad();
    }
  };

  componentDidUpdate(prevProps) {
    // dataUri is changed
    if (this.props.dataUri !== prevProps.dataUri) {
      this.clearScene();
      if (!_.isEmpty(this.props.dataUri)) {
        const fetchAndLoad = async () => {
          const response = await fetch(`${serverEndpoint}/data?data_uri=${encodeURIComponent(this.props.dataUri.replace(/\\/g, '/'))}`);
          const blob = await response.blob();
          const reader = new FileReader();
          reader.readAsDataURL(blob);
          reader.onload = (event) => {
            this.loadScene(
              event.target.result,
              this.containerRef.current.offsetWidth,
              this.containerRef.current.offsetHeight
            );
          };
        };
        fetchAndLoad();
      }
    } else if (!_.isEmpty(this.props.cameraPreviewImageData) && this.props.cameraPreviewImageData !== prevProps.cameraPreviewImageData) {
      this.clearScene();
      this.loadScene(
        this.props.cameraPreviewImageData,
        this.containerRef.current.offsetWidth,
        this.containerRef.current.offsetHeight,
      );
    }
  };

  loadScene = (dataUrl, canvasWidth, canvasHeight) => {
    if (!this.fabricCanvas || !this.displayCanvasRef.current) return;

    if (this.scene) {
      this.scene.setSrc(dataUrl, () => {
        this.sceneOriginalWidth = this.scene.width;
        this.sceneOriginalHeight = this.scene.height;
        this.scene.scaleToWidth(canvasWidth);
        this.scene.scaleToHeight(canvasHeight);
        this.fabricCanvas.setWidth(canvasWidth);
        this.fabricCanvas.setHeight(canvasHeight);
        this.fabricCanvas.renderAll();
      });
    } else {
      fabric.Image.fromURL(dataUrl, (img) => {
      // fabric.Image.fromURL(dataURL, (img) => {
        img.set({
          selectable: false,
          evented: false,
        })
        this.sceneOriginalWidth = img.width;
        this.sceneOriginalHeight = img.height;
        const { fabricCanvas } = this;
        fabricCanvas.setWidth(canvasWidth);
        fabricCanvas.setHeight(canvasHeight);
        // img.scaleToHeight(canvasHeight);
        // img.scaleToWidth(canvasWidth);
        img.moveTo(0);
        fabricCanvas.add(img);
        this.scene = img;
        // zoom and pan to view the whole scene
        // if (!this.props.isContinuousCaptureOn) this.resetView();
      });
    }
  };

  clearScene = () => {
    if (this.fabricCanvas) {
      this.fabricCanvas.clear();
    }
    this.scene = null;
  };

  resetView = () => {
    if (!this.fabricCanvas || !this.scene) return;
    const canvasWidth = this.fabricCanvas.getWidth();
    const canvasHeight = this.fabricCanvas.getHeight();
    const sceneWidth = this.scene.width * this.scene.scaleX;
    const sceneHeight = this.scene.height * this.scene.scaleY;
    const scaleX = canvasWidth / sceneWidth;
    const scaleY = canvasHeight / sceneHeight;
    const zoom = Math.min(scaleX, scaleY);

    const panX = (canvasWidth - sceneWidth * zoom) / 2;
    const panY = (canvasHeight - sceneHeight * zoom) / 2;

    this.fabricCanvas.viewportTransform = [zoom, 0, 0, zoom, panX, panY];
    this.fabricCanvas.renderAll();
  };

  render() {
    return (
      <div className='w-full h-full' ref={this.containerRef}>
        {super.render()}
      </div>
    );
  }
};