import React, { useEffect, useRef } from 'react';
import { handleRequestFailed, sleep, translation } from '../../common/util';
import { retrainModelTaskPhaseType, serverEndpoint } from '../../common/const';
import { ALERT_TYPES, aoiAlert } from '../../common/alert';
import { useDispatch } from 'react-redux';
import { setContainerWindowLoadingLocked } from '../../actions/setting';
import _ from 'lodash';


const TrainingInProgress = (props) => {
  const dispatch = useDispatch();

  const loopCheckRetrainStatus = useRef(null);

  const checkIfReadyForRetrain = (modelStatus) => {
    if (_.isEmpty(modelStatus)) return true;
    return _.isEmpty(_.filter(modelStatus, (taskStatus) => !_.includes([
      retrainModelTaskPhaseType.failure,
      retrainModelTaskPhaseType.complete,
      retrainModelTaskPhaseType.invalid
    ], taskStatus.phase)));
  };

  useEffect(() => {
    // init get model update polling
    loopCheckRetrainStatus.current = setInterval(async () => {
      let modelStatusRes;
      try {
        modelStatusRes = await fetch(`${serverEndpoint}/getModelUpdates`);
      } catch (error) {
        handleRequestFailed('getModelUpdates', error);
        clearInterval(loopCheckRetrainStatus.current);
        loopCheckRetrainStatus.current = null;
        dispatch(setContainerWindowLoadingLocked(false));
        return;
      }
      const modelStatus = await modelStatusRes.json();
      if (checkIfReadyForRetrain(modelStatus)) {
        clearInterval(loopCheckRetrainStatus.current);
        loopCheckRetrainStatus.current = null;
        dispatch(setContainerWindowLoadingLocked(false));
        // search for the task with greatest schedule_id
        const latestTask = _.maxBy(modelStatus, (task) => task.schedule_id);
        if (_.includes([retrainModelTaskPhaseType.failure, retrainModelTaskPhaseType.invalid], _.get(latestTask, 'phase'))) {
          aoiAlert(translation('notification.error.retrainFailed'), ALERT_TYPES.COMMON_ERROR);
          return;
        }
        aoiAlert(translation('notification.success.retrainFinished'), ALERT_TYPES.COMMON_SUCCESS);
        await sleep(500);
        props.history.push('/aoi/home');
        return;
      }
    }, 1000);
  }, []);

  return (
    <div className='flex flex-col gap-8 justify-center items-center flex-1 h-full self-stretch'>
      <div className='flex items-center w-[406px] h-[143px]'>
        {/* <img
          src='/img/img/trainingInProgress2.svg'
          className='w-[223px] h-[136px] shrink-0'
          alt='training-in-progress'
        />
        <img
          src='/img/img/trainingInProgress1.svg'
          className='w-[222px] h-[136px] shrink-0'
          alt='training-in-progress'
        /> */}
        <img
          src='/img/img/trainingInProgress.png'
          className='w-[406px] h-[143px] shrink-0'
          alt='training-in-progress'
        />
      </div>
      <div className='flex flex-col justify-center items-center gap-2'>
        <span className='font-inter text-[24px] font-normal'>
          {translation('trainingInProgress.modelRetrainingInProgress')}
        </span>
        <span className='font-inter text-[14px] font-normal'>
          {translation('trainingInProgress.theSystemIsTemporary')}
        </span>
      </div>
    </div>
  );
};

export default TrainingInProgress;