import { Button, Modal } from 'antd';
import React, { useState } from 'react';
import { translation } from '../../common/util';
import { RedPrimaryButtonConfigProvider } from '../../common/darkModeComponents';


const RemoveFeatureConfirmation = (props) => {
  const {
    isOpened,
    setIsOpened,
    handleRemoveFeature,
  } = props;

  const [isLoading, setIsLoading] = useState(false);

  return (
    <Modal
      open={isOpened}
      onCancel={() => setIsOpened(false)}
      title={
        <div className='flex items-center gap-2 flex-1'>
          <img src='/img/icn/icn_exclamationCircle_white.svg' alt='exclamation-circle' className='w-4 h-4' />
          <span className='font-source text-[16px] font-semibold'>
            {translation('productAnnotation.removeComponent')}
          </span>
        </div>
      }
      footer={
        <div className='flex p-2 items-start gap-2 self-stretch justify-center'>
          <Button
            style={{ width: '50%' }}
            onClick={() => setIsOpened(false)}
          >
            <span className='font-source text-[12px] font-semibold'>
              {translation('common.cancel')}
            </span>
          </Button>
          <RedPrimaryButtonConfigProvider>
            <Button
              style={{ width: '50%' }}
              loading={isLoading}
              onClick={async () => {
                setIsLoading(true);
                await handleRemoveFeature();
                setIsLoading(false);
                setIsOpened(false);
              }}
            >
              <span className='font-source text-[12px] font-semibold'>
                {translation('productAnnotation.yesRemoveComponent')}
              </span>
            </Button>
          </RedPrimaryButtonConfigProvider>
        </div>
      }
    >
      <div className='flex py-6 px-4 flex-col items-start self-stretch gap-8'>
        <span className='font-source text-[14px] font-normal'>
          {translation('productAnnotation.thisActionWillRemoveOneComponent')}
        </span>
      </div>
    </Modal>
  );
};

export default RemoveFeatureConfirmation;