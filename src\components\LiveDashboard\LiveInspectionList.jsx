import { Button, List } from 'antd';
import _ from 'lodash';
import React, { Fragment, useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { setContainerWindowLoadingLocked, setCurRunningIpcSessionIds } from '../../actions/setting';
import { ALERT_TYPES, aoiAlert } from '../../common/alert';
import { getQueryParams, handleRequestFailed, translation } from '../../common/util';
import { useGetAllProductsQuery, useLazyGetCurProductComponentToggleQuery, useLazyGetInferenceStatusQuery, usePostInferenceActionMutation, useStopInferenceMutation } from '../../services/product';
import { useLazyGetAllInspectionsQuery, useLazyGetSessionInfoQuery } from '../../services/session';
import MainMenuLayout from '../layout/MainMenuLayout';
import InferenceContinueConfirmation from '../modal/InferenceContinueConfirmation';
import NewInspection from '../modal/NewInspection';
import LiveInspectionCard from './LiveInspectionCard';
import LiveComponentFilter from '../Inference/LiveComponentFilter';
import { RedPrimaryButtonConfigProvider } from '../../common/darkModeComponents';


const LiveInspectionList = (props) => {
  const dispatch = useDispatch();

  const { search } = props.location;
  const searchParams = getQueryParams(search);
  const { isNewInspectionTriggered } = searchParams;

  // const { data: allProducts } = useGetAllProductsQuery();
  const [getInspections] = useLazyGetAllInspectionsQuery();
  const [postInferenceAction] = usePostInferenceActionMutation();
  const [stopInference] = useStopInferenceMutation();
  const [getInferenceStatus] = useLazyGetInferenceStatusQuery();
  const [getSessionInfo] = useLazyGetSessionInfoQuery();
  const [getCurProductComponentlist] = useLazyGetCurProductComponentToggleQuery();

  const listContainerRef = useRef();
  const paginationRef = useRef({ current: 0, pageSize: 0, total: 0 });

  const [isNewInspectionModalOpened, setIsNewInspectionModalOpened] = useState(false);
  const [inspectionList, setInspectionList] = useState([]);
  // const [curRunningGoldenProductName, setCurRunningGoldenProductName] = useState(translation('liveDashboard.noInspectionIsRunning'));
  const [isInferenceContinueOpened, setIsInferenceContinueOpened] = useState(false);
  const [pagination, setPagination] = useState({ current: 0, pageSize: 0, total: 0 });
  const [curSessionInfo, setCurSessionInfo] = useState({});
  const [isLiveComponentFilterOpened, setIsLiveComponentFilterOpened] = useState(false);
  const [currentFilterMap, setCurrentFilterMap] = useState({});

  const isInferenceRunning = useSelector(state => state.setting.isInferenceRunning);
  const curRunningIpcSessionIds = useSelector(state => state.setting.curRunningIpcSessionIds);
  const inferenceStatusResponse = useSelector(state => state.setting.inferenceStatusResponse);

  if (_.isEmpty(curRunningIpcSessionIds)) window.location.href = '/aoi/home';

  const autoRefetchSessionInterval = useRef();

  const handlePostInferenceAction = async () => {
    await postInferenceAction();
  };

  const handlePaginationUpdate = async (pageIndex, curRunningIpcSessionIds) => {
    const res = await getInspections({
      is_golden: false,
      ipc_session_id: _.first(curRunningIpcSessionIds),
      page: pageIndex - 1,
      limit: paginationRef.current.pageSize,
    });

    if (_.get(res, 'error')) {
      handleRequestFailed('getInspections', _.get(res, 'error'));
      return;
    }

    setInspectionList(_.get(res, 'data.data', []));
    setPagination({
      ...paginationRef.current,
      current: pageIndex,
      total: _.get(res, 'data.pageCount', 0) * paginationRef.current.pageSize,
    });

    paginationRef.current = {
      ...paginationRef.current,
      current: pageIndex,
      total: _.get(res, 'data.pageCount', 0) * paginationRef.current.pageSize,
    };
  };

  const initCurSessionInfo = async (sessionId) => {
    const res = await getSessionInfo(sessionId);
    if (_.get(res, 'error')) {
      handleRequestFailed('getSessionInfo', _.get(res, 'error'));
      return;
    }

    setCurSessionInfo(_.get(res, 'data', {}));
  };

  const initFilterMap = async () => {
    const res = await getCurProductComponentlist();
    if (res.error) {
      handleRequestFailed('getCurProductComponentlist', res.error);
      return;
    }

    setCurrentFilterMap(_.get(res, 'data.current_product_options', {}));
  };

  useEffect(() => {
    setIsInferenceContinueOpened(isNewInspectionTriggered === 'true');

    const initInspectionList = async (curRunningIpcSessionIds) => {
      if (paginationRef.current.pageSize === 0 || paginationRef.current.current === 0 || _.isEmpty(curRunningIpcSessionIds)) return;

      const res = await getInspections({
        is_golden: false,
        ipc_session_id: _.first(curRunningIpcSessionIds),
        page: paginationRef.current.current - 1,
        limit: paginationRef.current.pageSize,
      });

      if (_.get(res, 'error')) {
        handleRequestFailed('getInspections', _.get(res, 'error'));
        return;
      }

      setInspectionList(_.get(res, 'data.data', []));
      setPagination({
        ...pagination,
        total: _.get(res, 'data.pageCount', 0) * paginationRef.current.pageSize,
      });
      paginationRef.current = {
        ...paginationRef.current,
        total: _.get(res, 'data.pageCount', 0) * paginationRef.current.pageSize,
      };
      
      const autoRefetchSession = setInterval(async () => {
        const res = await getInspections({
          is_golden: false,
          ipc_session_id: _.first(curRunningIpcSessionIds),
          page: paginationRef.current.current - 1,
          limit: paginationRef.current.pageSize,
        });
        if (_.get(res, 'error')) {
          handleRequestFailed('getInspections', _.get(res, 'error'));
          clearInterval(autoRefetchSessionInterval.current);
          return;
        }
        setPagination({
          ...paginationRef.current,
          total: _.get(res, 'data.pageCount', 0) * paginationRef.current.pageSize,
        });
        paginationRef.current = {
          ...paginationRef.current,
          total: _.get(res, 'data.pageCount', 0) * paginationRef.current.pageSize,
        };
        setInspectionList(_.get(res, 'data.data', []));
        initCurSessionInfo(_.first(curRunningIpcSessionIds));
        initFilterMap();
      }, 500); // 500ms
      autoRefetchSessionInterval.current = autoRefetchSession;
    };

    // calc page size for list
    const inspectionCardHeight = 152;
    const listContainerHeight = listContainerRef.current.clientHeight;
    const pageSize = Math.floor(listContainerHeight / inspectionCardHeight);
    paginationRef.current = { current: 1, pageSize, total: 0 };
    setPagination({
      current: 1,
      pageSize,
      total: 0,
    });

    initInspectionList(curRunningIpcSessionIds);
    initCurSessionInfo(_.first(curRunningIpcSessionIds));
    initFilterMap();

    return () => {
      if (autoRefetchSessionInterval.current) clearInterval(autoRefetchSessionInterval.current);
    };
  }, []);

  return (
    <Fragment>
      <InferenceContinueConfirmation
        isOpened={isInferenceContinueOpened}
        setIsOpened={setIsInferenceContinueOpened}
        handleRunInference={handlePostInferenceAction}
        handleRedirect={(path) => props.history.push(path)}
      />
      <NewInspection
        isOpened={isNewInspectionModalOpened}
        setIsOpened={setIsNewInspectionModalOpened}
        handleRedirect={(path) => props.history.push(path)}
      />
      <MainMenuLayout>
        <div className='relative h-full w-full'>
          {/* <LiveComponentFilter
            isLiveComponentFilterOpened={isLiveComponentFilterOpened}
            setIsLiveComponentFilterOpened={setIsLiveComponentFilterOpened}
            currentFilterMap={currentFilterMap}
            setCurrentFilterMap={setCurrentFilterMap}
          /> */}
        <div className='absolute top-0 left-0 w-full h-full z-[1]'>
        <div className='flex h-full'>
        <div className='flex items-start gap-4 flex-1 self-stretch'>
          <div className='flex flex-col py-4 px-8 gap-4 self-stretch flex-1 items-start rounded-[2px] bg-[#131313]'>
            <div className='flex h-[48px] py-2 items-center gap-2.5 self-stretch'>
              <div
                className='flex w-6 h-6 cursor-pointer justify-center items-center gap-2.5'
                onClick={() => window.location.href = '/aoi/home'}
              >
                <img src='/img/icn/icn_arrowLeft_white.svg' alt='back' className='w-[8px] h-[16px] shrink' />
              </div>
              <div className='flex w-5 h-5 justify-center items-center gap-2.5'>
                <img src='/img/icn/icn_tachmeter_white.svg' className='w-[20px] h-[15.3px]' alt='tachmeter' />
              </div>
              <span className='font-source text-[20px] font-semibold'>
                {translation('liveDashboard.liveDashboard')}
              </span>
            </div>
            <div className='h-[1px] w-full bg-[#4F4F4F]' />
            <div className='flex py-2 items-center justify-between self-stretch'>
              <div className='flex items-baseline gap-6'>
                {/* <div className='flex gap-2 items-center'>
                  <span className='font-source text-[14px] font-normal'>
                    {translation('liveDashboard.currentProduct')}
                  </span>
                  <span className='font-source text-[14px] font-semibold'>
                    {curRunningGoldenProductName}
                  </span>
                </div> */}
                <div className='flex gap-2 items-center'>
                  <span className='font-source text-[14px] font-normal'>
                    {translation('common.todayTotalProduct')}:
                  </span>
                  <span className='font-source text-[14px] font-semibold'>
                    {/* {_.get(curSessionInfo, 'total_product_count', 0)} */}
                    {_.get(inferenceStatusResponse, 'inspected_count', 0)}
                  </span>
                </div>
                <div className='flex gap-2 items-center'>
                  <span className='font-source text-[14px] font-normal'>
                    {translation('common.curSession')}
                  </span>
                  <span className='font-source text-[14px] font-normal'>
                    {translation('liveDashboard.totalProducts')}
                  </span>
                  <span className='font-source text-[14px] font-semibold'>
                    {_.get(curSessionInfo, 'total_product_count', 0)}
                  </span>
                </div>
                <div className='flex gap-2 items-center'>
                  <span className='font-source text-[14px] font-normal'>
                    {translation('liveDashboard.goodInferenceCount')}
                  </span>
                  <span className='font-source text-[14px] font-semibold text-AOI-green'>
                    {_.get(curSessionInfo, 'total_product_count', 0)-_.get(curSessionInfo, 'defective_product_count', 0)}
                  </span>
                </div>
                <div className='flex gap-2 items-center'>
                  <span className='font-source text-[14px] font-normal'>
                    {translation('liveDashboard.defectiveInferenceCount')}
                  </span>
                  <span className='font-source text-[14px] font-semibold text-[#EB5757]'>
                    {_.get(curSessionInfo, 'defective_product_count', 0)}
                  </span>
                </div>
              </div>
              <div className='flex items-center gap-2 self-stretch'>
                <Button
                  type='text'
                  onClick={() => {
                    const latestInspection = _.first(inspectionList);
                    if (_.isEmpty(latestInspection)) return;
                    window.location.href = `/aoi/view-inference/${latestInspection.golden_product_id}/${latestInspection.product_id}?step=0`;
                  }}
                >
                  <div className='flex py-[6px] px-4 items-center gap-2 justify-center'>
                    <span className='font-source text-[12px] font-normal text-AOI-blue'>
                      {translation('liveDashboard.viewLatestInspection')}
                    </span>
                    <img src='/img/icn/icn_rightArrow_blue.svg' className='w-3 h-3' alt='rightArrow' />
                  </div>
                </Button>
                { isInferenceRunning ?
                  <RedPrimaryButtonConfigProvider>
                    <Button
                      onClick={async () => {
                        dispatch(setContainerWindowLoadingLocked(true));
                        const res = await getInferenceStatus();
                        if (_.get(res, 'data.status') === 'stopped') {
                          aoiAlert(translation('notification.error.inferenceAlreadyStopped'), ALERT_TYPES.COMMON_ERROR);
                          dispatch(setContainerWindowLoadingLocked(false));
                          return;
                        }
                        const stopInfRes = await stopInference();
                        if (stopInfRes.error) {
                          handleRequestFailed('stopInference', stopInfRes.error);
                          dispatch(setContainerWindowLoadingLocked(false));
                          return;
                        }

                        dispatch(setCurRunningIpcSessionIds([]));
                        dispatch(setContainerWindowLoadingLocked(false));

                        window.location.href = '/aoi/home';
                      }}
                    >
                      <div className='flex py-1.5 px-4 items-center gap-2 justify-center'>
                        <span className='font-source text-[12px] font-normal'>
                          {translation('liveDashboard.stopInspection')}
                        </span>
                      </div>
                    </Button>
                  </RedPrimaryButtonConfigProvider>
                  :
                  <Button
                    onClick={async () => {
                      const res = await getInferenceStatus();
                      if (_.get(res, 'data.status') !== 'stopped') {
                        aoiAlert(translation('notification.error.inferenceAlreadyRunning'), ALERT_TYPES.COMMON_ERROR);
                        return;
                      }
                      setIsNewInspectionModalOpened(true);
                    }}
                  >
                    <div className='flex py-1.5 px-4 items-center gap-2 justify-center'>
                      <span className='font-source text-[12px] font-normal'>
                        {translation('liveDashboard.startInspection')}
                      </span>
                    </div>
                  </Button>
                }
              </div>
            </div>
            {/* inspection list goes here */}
            <div className='flex flex-col flex-1 self-stretch' ref={listContainerRef}>
              <List
                grid={{
                  column: 1,
                }}
                pagination={{
                  pageSize: pagination.pageSize,
                  total: pagination.total,
                  current: pagination.current,
                  hideOnSinglePage: true,
                  onChange: (page, pageSize) => {
                    handlePaginationUpdate(page, curRunningIpcSessionIds);
                  },
                }}
                dataSource={inspectionList}
                renderItem={(item) => (
                  <List.Item>
                    <LiveInspectionCard
                      inspection={item}
                      handleRedirect={(path) => props.history.push(path)}
                      goldenProductName={_.get(item, 'golden_product_name', '')}
                    />
                  </List.Item>
                )}
              />
            </div>
          </div>
        </div>
        </div>
        </div>
        </div>
      </MainMenuLayout>
    </Fragment>
  );
};

export default LiveInspectionList;