import { Button, Select } from 'antd';
import React, { useEffect, useState } from 'react';
import { AOIGreenButtonConfig, AOIGreenPrimaryButtonConfig, DarkModal, defaultGreen } from '../../common/darkModeComponents';
import { translation } from '../../common/util';
import { useGetAllProductsQuery } from '../../services/product';
import _ from 'lodash';
import { ALERT_TYPES, aoiAlert } from '../../common/alert';
import { useTranslation } from 'react-i18next';


const RetrainConfirmation = (props) => {
  const {
    isOpened,
    setIsOpened,
    handleRetrainTrigger,
    defaultGoldenProductId,
  } = props;
  
  const { t } = useTranslation();

  const [selectedGoldenProductId, setSelectedGoldenProductId] = useState(null);

  const { data: allProducts, refetch: refetchAllProducts } = useGetAllProductsQuery();
  
  useEffect(() => {
    if (_.isInteger(defaultGoldenProductId)) setSelectedGoldenProductId(defaultGoldenProductId);
  }, [defaultGoldenProductId]);

  useEffect(() => {
    if (!isOpened) return;
    refetchAllProducts();
  }, [isOpened]);

  return (
    <DarkModal
      width={560}
      primaryColor={defaultGreen}
      open={isOpened}
      onCancel={() => setIsOpened(false)}
      title={
        <span className='font-source text-[16px] font-semibold'>
          {translation('viewInspection.readyToRetrainTheModel')}
        </span>
      }
      footer={
        <div className='flex gap-2 self-stretch items-start'>
          <AOIGreenButtonConfig>
            <Button
              style={{ width: '50%' }}
              onClick={() => setIsOpened(false)}
            >
              <span className='text-source text-[12px] font-normal'>
                {translation('common.cancel')}
              </span>
            </Button>
          </AOIGreenButtonConfig>
          {/* <AOIGreenPrimaryButtonConfig>
            <Button
              style={{ width: '50%' }}
              onClick={() => {
                setIsOpened(false);
                handleRetrainTrigger(true);
              }}
            >
              <span className='text-source text-[12px] font-normal'>
                {translation('viewInspection.startTrainingAfterSession')}
              </span>
            </Button>
          </AOIGreenPrimaryButtonConfig> */}
          <AOIGreenPrimaryButtonConfig>
            <Button
              style={{ width: '50%' }}
              onClick={() => {
                if (!_.isInteger(selectedGoldenProductId)) {
                  aoiAlert(t('notification.error.selectAGoldenProduct'), ALERT_TYPES.COMMON_ERROR);
                  return;
                }
                setIsOpened(false);
                handleRetrainTrigger(false, selectedGoldenProductId);
              }}
            >
              <span className='text-source text-[12px] font-normal'>
                {translation('viewInspection.retrainModel')}
              </span>
            </Button>
          </AOIGreenPrimaryButtonConfig>
        </div>
      }
    >
      <div className='flex py-6 px-4 flex-col items-start gap-8 self-stretch flex-1'>
        <span className='text-source text-[12px] font-normal'>
          {translation('viewInspection.retrainModelModalDesc')}
        </span>
        { allProducts && allProducts.length > 0 &&
          <div className='flex items-center gap-2 self-stretch flex-1'>
            <span className='text-source text-[12px] font-normal'>
              {translation('viewInspection.pickRetrainGoldenProduct')}
            </span>
            <div className='flex flex-1'>
              <Select
                value={selectedGoldenProductId}
                style={{ width: '100%' }}
                options={[
                  ...allProducts.map((product) => ({
                    label: <span className='text-source text-[12px] font-normal'>
                      {product.product_name}
                    </span>,
                    value: Number(product.product_id),
                  })),
                  // { label: <span className='text-source text-[12px] font-normal'>{translation('common.all')}</span>, value: null }
                ]}
                onChange={(value) => setSelectedGoldenProductId(value)}
                popupMatchSelectWidth={false}
              />
            </div>
          </div>
        }
      </div>
    </DarkModal>
  );
};

export default RetrainConfirmation;