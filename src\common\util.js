import _ from 'lodash';
import i18n from '../i18n';
import { defectDetection, heightDiff, retrainModelType, serverEndpoint } from './const';
import { ALERT_TYPES, aoiAlert } from './alert';
import moment from 'moment';
import md5 from 'md5';
import { text as translationText } from '../common/translation';


export const translation = (textPath, templateValues=null) => {
  let translatedText = i18n.t(textPath);
  if (templateValues) {
    return formatString(translatedText, templateValues);
  }
  return translatedText;
};

export const formatString = (template, values) => {
  // usage
  // const template = `${value1} test string ${value2}`;
  // const values = { value1: 'Hello', value2: 'World' };
  return template.replace(/\$\{(\w+)\}/g, (match, key) => {
    return values[key] || match;  // returns the original match if key doesn't exist in values
  });
};

/**
 * Get every image dimensions by view layout and allowed space
 * @param {Array} viewLayout array of column number of every row
 * @param {HTMLElement} htmlElement
 */
export const getImageDimensionsByViewLayout = (viewLayout, htmlElement) => {
  // every image's dimension could be different
  // we return

};

/**
 * Get camera count by camera view layout
 * @param {Array} cameraViewLayout 
 */
export const getCameraCountByCameraViewLayout = (cameraViewLayout) => {
  if (_.isEmpty(cameraViewLayout)) return 0;
  return cameraViewLayout.reduce((acc, cur) => acc + cur, 0);
};

export const getInspectionCountByInspectionViewLayout = (inspectionViewLayout) => {
  if (_.isEmpty(inspectionViewLayout)) return 0;
  return inspectionViewLayout.reduce((acc, cur) => acc + cur, 0);
};

/**
 * Get binary data from server
 * @param {String} dataUri 
 * @param {Function} callback 
 */
export const getBinaryData = async (dataUri, callback) => {
  const res = await fetch(`${serverEndpoint}/data/${dataUri}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/octet-stream',
    },
  });
  const blob = await res.blob();
  callback(blob);
};

export const handleRequestFailed = (requestName, error) => {
  aoiAlert(translation('notification.error.requestFailed', { requestName }), ALERT_TYPES.COMMON_ERROR);
  if (!_.isEmpty(error)) console.error(`${requestName} failed: ` + error);
};

/**
 * Debounce function
 * @param {Function} func
 * @param {Number} deplay in ms
 */
export const debounce = (func, deplay) => {
  let inDebounce;
  return function (...args) {
    if (inDebounce) {
      clearTimeout(inDebounce);
    }
    inDebounce = setTimeout(() => func(...args), deplay);
  };
};

export const loadAndDecodePoints = async (url) => {
  // console.log(Date.now().valueOf(), 'start downloading pointcloud');
  const response = await fetch(url);
  // console.log(Date.now().valueOf(), 'finish downloading pointcloud');
  
  // console.log(Date.now().valueOf(), 'start decoding pointcloud');
  const blob = await response.arrayBuffer();
  const buffer = new Uint8Array(blob);

  const pointSize = 15; // 3 floats (4 bytes each) + 3 bytes for RGB
  const numPoints = buffer.length / pointSize;

  const positions = new Float32Array(buffer.buffer, 0, numPoints * 3);
  const colors = new Uint8Array(buffer.buffer, 0 + numPoints * 12, numPoints * 3);
  // console.log(Date.now().valueOf(), 'finish decoding pointcloud');

  // filter out NaN and Infinity values in position and colors
  // remove NaN and Infinity values in positions
  for (let i = 0; i < positions.length; i += 3) {
    if (!isFinite(positions[i]) || !isFinite(positions[i + 1]) || !isFinite(positions[i + 2])) {
      positions[i] = 0;
      positions[i + 1] = 0;
      positions[i + 2] = 0;
      colors[i] = 0;
      colors[i + 1] = 0;
      colors[i + 2] = 0;
    }
  }

  return {positions, colors};
};

export const getQueryParams = (search) => {
  const params = {};
  const searchParams = new URLSearchParams(search);
  for (const [key, value] of searchParams) {
    params[key] = value;
  }
  return params;
};

export const backendTimestampToDisplayString = (timestamp) => {
  if (!_.isString(timestamp)) return '';

  // ex. 2024-08-12T15:54:58 Pacific Daylight Time
  // Use regex to extract the date and time part, ignoring the timezone part
  const cleanedTimeString = timestamp.match(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/)[0];
  // Parse the cleaned time string
  // const parsedTime = moment(cleanedTimeString, 'YYYY-MM-DDTHH:mm:ss');
  // Format the parsed time
  // return parsedTime.format('YYYY-MM-DD HH:mm:ss');

  // convert backend's utc to local time
  let parsedTime = moment.utc(cleanedTimeString).format('YYYY-MM-DDTHH:mm:ss');
  // parsedTime = moment.utc(cleanedTimeString).toDate();
  return moment(parsedTime).local().format('YYYY-MM-DD HH:mm:ss');
};

export const backendAutoGenTimeToDisplayString = (timeStr) => {
  if (_.isEmpty(timeStr)) return '';

  // auto generated time in db is utc + 4 hrs and 30 mins
  // need to convert to local time's moment object
  const cleanedTimeString = timeStr.match(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/)[0];

  // subtract 4 hrs and 30 mins
  // const parsedTime = moment.utc(cleanedTimeString, 'YYYY-MM-DDTHH:mm:ss').subtract(4, 'hours').subtract(30, 'minutes');

  const parsedTime = moment.utc(cleanedTimeString, 'YYYY-MM-DDTHH:mm:ss');
  // convert from utc to local
  return parsedTime.local().format('YYYY-MM-DD HH:mm:ss');
};

export const localDayjsToUTCMoment = (localTime) => {
  // Dayjs object to formated utc time string
  return moment(localTime).utc();
};

export const convertBackendTimestampToMoment = (timestamp) => {
  if (!_.isString(timestamp)) return moment();

  // ex. 2024-08-12T15:54:58 Pacific Daylight Time
  // Use regex to extract the date and time part, ignoring the timezone part
  const cleanedTimeString = timestamp.match(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/)[0];
  // Parse the cleaned time string
  // const parsedTime = moment(cleanedTimeString, 'YYYY-MM-DDTHH:mm:ss');
  // return parsedTime;

  // subtract 4 hrs and 30 mins
  // const parsedTime = moment.utc(cleanedTimeString, 'YYYY-MM-DDTHH:mm:ss').subtract(4, 'hours').subtract(30, 'minutes');

  const parsedTime = moment.utc(cleanedTimeString, 'YYYY-MM-DDTHH:mm:ss');
  return parsedTime;
};

export const convertBackendLocalTimeToUTCTimestamp = (timestamp) => {
  if (!_.isString(timestamp)) return 0;

  // ex. 2024-08-12T15:54:58 Pacific Daylight Time
  // Use regex to extract the date and time part, ignoring the timezone part
  const cleanedTimeString = timestamp.match(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/)[0];
  // Parse the cleaned time string
  const parsedTime = moment(cleanedTimeString, 'YYYY-MM-DDTHH:mm:ss');
  // convert from local to utc
  return parsedTime.utc().valueOf();
};

export const checkOutlierStrengthBasedOnAlgorithmParams = (algorithmParams) => {
  if (_.isEmpty(algorithmParams)) return 'customized';
  if (
    _.get(algorithmParams, 'enable_outlier') === false &&
    _.get(algorithmParams, 'outlier') === 0 &&
    _.get(algorithmParams, 'enable_face_normal') === false &&
    _.get(algorithmParams, 'face_normal_angle') === 0 &&
    _.get(algorithmParams, 'enable_cluster_filter') === false &&
    _.get(algorithmParams, 'cluster_neighbor_distance') === 5 &&
    _.get(algorithmParams, 'cluster_filter_strength') === 0
  ) {
    return 'off';
  } else if (
    _.get(algorithmParams, 'enable_outlier') === true &&
    _.get(algorithmParams, 'outlier') === 10 &&
    _.get(algorithmParams, 'enable_face_normal') === false &&
    _.get(algorithmParams, 'face_normal_angle') === 0 &&
    _.get(algorithmParams, 'enable_cluster_filter') === true &&
    _.get(algorithmParams, 'cluster_neighbor_distance') === 25 &&
    _.get(algorithmParams, 'cluster_filter_strength') === 1
  ) {
    return 'weak';
  } else if (
    _.get(algorithmParams, 'enable_outlier') === true &&
    _.get(algorithmParams, 'outlier') === 3 &&
    _.get(algorithmParams, 'enable_face_normal') === true &&
    _.get(algorithmParams, 'face_normal_angle') === 20 &&
    _.get(algorithmParams, 'enable_cluster_filter') === true &&
    _.get(algorithmParams, 'cluster_neighbor_distance') === 25 &&
    _.get(algorithmParams, 'cluster_filter_strength') === 4
  ) {
    return 'normal';
  } else if (
    _.get(algorithmParams, 'enable_outlier') === true &&
    _.get(algorithmParams, 'outlier') === 2 &&
    _.get(algorithmParams, 'enable_face_normal') === true &&
    _.get(algorithmParams, 'face_normal_angle') === 30 &&
    _.get(algorithmParams, 'enable_cluster_filter') === true &&
    _.get(algorithmParams, 'cluster_neighbor_distance') === 25 &&
    _.get(algorithmParams, 'cluster_filter_strength') === 10
  ) {
    return 'strong';
  } else {
    return 'customized';
  }
};

export const getOutlierStrengthAlgorithmParams = (outlierStrength) => {
  if (outlierStrength === 'off') {
    return {
      enable_outlier: false,
      outlier: 0,
      enable_face_normal: false,
      face_normal_angle: 0,
      enable_cluster_filter: false,
      cluster_neighbor_distance: 5,
      cluster_filter_strength: 0,
    };
  } else if (outlierStrength === 'weak') {
    return {
      enable_outlier: true,
      outlier: 10,
      enable_face_normal: false,
      face_normal_angle: 0,
      enable_cluster_filter: true,
      cluster_neighbor_distance: 25,
      cluster_filter_strength: 1,
    };
  } else if (outlierStrength === 'normal') {
    return {
      enable_outlier: true,
      outlier: 3,
      enable_face_normal: true,
      face_normal_angle: 20,
      enable_cluster_filter: true,
      cluster_neighbor_distance: 25,
      cluster_filter_strength: 4,
    };
  } else if (outlierStrength === 'strong') {
    return {
      enable_outlier: true,
      outlier: 2,
      enable_face_normal: true,
      face_normal_angle: 30,
      enable_cluster_filter: true,
      cluster_neighbor_distance: 25,
      cluster_filter_strength: 10,
    };
  } else {
    return {
      enable_outlier: true,
      outlier: 10,
      enable_face_normal: false,
      face_normal_angle: 0,
      enable_cluster_filter: true,
      cluster_neighbor_distance: 25,
      cluster_filter_strength: 1,
    };
  }
};

export const checkSmoothingStrengthBasedOnAlgorithmParams = (algorithmParams) => {
  if (_.isEmpty(algorithmParams)) return 'customized';
  if (
    _.get(algorithmParams, 'enable_gaussian') === false &&
    _.get(algorithmParams, 'gaussian_strength') === 0 &&
    _.get(algorithmParams, 'enable_median') === false &&
    _.get(algorithmParams, 'median_kernel_size') === 3 &&
    _.get(algorithmParams, 'enable_smooth') === false &&
    _.get(algorithmParams, 'smooth_granularity') === 'OneHundredth'
  ) {
    return 'off';
  } else if (
    _.get(algorithmParams, 'enable_gaussian') === true &&
    _.get(algorithmParams, 'gaussian_strength') === 1 &&
    _.get(algorithmParams, 'enable_median') === false &&
    _.get(algorithmParams, 'median_kernel_size') === 0 &&
    _.get(algorithmParams, 'enable_smooth') === false &&
    _.get(algorithmParams, 'smooth_granularity') === 'OneHundredth'
  ) {
    return 'weak';
  } else if (
    _.get(algorithmParams, 'enable_gaussian') === true &&
    _.get(algorithmParams, 'gaussian_strength') === 3 &&
    _.get(algorithmParams, 'enable_median') === true &&
    _.get(algorithmParams, 'median_kernel_size') === 3 &&
    _.get(algorithmParams, 'enable_smooth') === false &&
    _.get(algorithmParams, 'smooth_granularity') === 'OneHundredth'
  ) {
    return 'normal';
  } else if (
    _.get(algorithmParams, 'enable_gaussian') === true &&
    _.get(algorithmParams, 'gaussian_strength') === 7 &&
    _.get(algorithmParams, 'enable_median') === true &&
    _.get(algorithmParams, 'median_kernel_size') === 5 &&
    _.get(algorithmParams, 'enable_smooth') === false &&
    _.get(algorithmParams, 'smooth_granularity') === 'OneHundredth'
  ) {
    return 'strong';
  } return 'customized';
};

export const getSmoothingStrengthAlgorithmParams = (smoothingStrength) => {
  if (smoothingStrength === 'off') {
    return {
      enable_gaussian: false,
      gaussian_strength: 0,
      enable_median: false,
      median_kernel_size: 3,
      enable_smooth: false,
      smooth_granularity: 'OneHundredth',
    };
  } else if (smoothingStrength === 'weak') {
    return {
      enable_gaussian: true,
      gaussian_strength: 1,
      enable_median: false,
      median_kernel_size: 0,
      enable_smooth: false,
      smooth_granularity: 'OneHundredth',
    };
  } else if (smoothingStrength === 'normal') {
    return {
      enable_gaussian: true,
      gaussian_strength: 3,
      enable_median: true,
      median_kernel_size: 3,
      enable_smooth: false,
      smooth_granularity: 'OneHundredth',
    };
  } else if (smoothingStrength === 'strong') {
    return {
      enable_gaussian: true,
      gaussian_strength: 7,
      enable_median: true,
      median_kernel_size: 5,
      enable_smooth: false,
      smooth_granularity: 'OneHundredth',
    };
  } else {
    return {
      enable_gaussian: true,
      gaussian_strength: 3,
      enable_median: true,
      median_kernel_size: 3,
      enable_smooth: false,
      smooth_granularity: 'OneHundredth',
    };
  }
};

export const checkContrastBasedOnAlgorithmParams = (algorithmParams) => {
  if (_.isEmpty(algorithmParams)) return 'customized';
  if (
    _.get(algorithmParams, 'saturation') === false &&
    _.get(algorithmParams, 'enable_intensity') === false &&
    _.get(algorithmParams, 'intensity') === 0 &&
    _.get(algorithmParams, 'enable_phase_quality') === false &&
    _.get(algorithmParams, 'phase_quality_threshold') === 0
  ) {
    return 'off';
  } else if (
    _.get(algorithmParams, 'saturation') === false &&
    _.get(algorithmParams, 'enable_intensity') === true &&
    _.get(algorithmParams, 'intensity') === 10 &&
    _.get(algorithmParams, 'enable_phase_quality') === true &&
    _.get(algorithmParams, 'phase_quality_threshold') === 0.05
  ) {
    return 'weak';
  } else if (
    _.get(algorithmParams, 'saturation') === false &&
    _.get(algorithmParams, 'enable_intensity') === true &&
    _.get(algorithmParams, 'intensity') === 20 &&
    _.get(algorithmParams, 'enable_phase_quality') === true &&
    _.get(algorithmParams, 'phase_quality_threshold') === 0.1
  ) {
    return 'normal';
  } else if (
    _.get(algorithmParams, 'saturation') === true &&
    _.get(algorithmParams, 'enable_intensity') === true &&
    _.get(algorithmParams, 'intensity') === 30 &&
    _.get(algorithmParams, 'enable_phase_quality') === true &&
    _.get(algorithmParams, 'phase_quality_threshold') === 0.2
  ) {
    return 'strong';
  } return 'customized';
};

export const getContrastAlgorithmParams = (contrast) => {
  if (contrast === 'off') {
    return {
      saturation: false,
      enable_intensity: false,
      intensity: 0,
      enable_phase_quality: false,
      phase_quality_threshold: 0,
    };
  } else if (contrast === 'weak') {
    return {
      saturation: false,
      enable_intensity: true,
      intensity: 10,
      enable_phase_quality: true,
      phase_quality_threshold: 0.05,
    };
  } else if (contrast === 'normal') {
    return {
      saturation: false,
      enable_intensity: true,
      intensity: 20,
      enable_phase_quality: true,
      phase_quality_threshold: 0.1,
    };
  } else if (contrast === 'strong') {
    return {
      saturation: true,
      enable_intensity: true,
      intensity: 30,
      enable_phase_quality: true,
      phase_quality_threshold: 0.2,
    };
  } else {
    return {
      saturation: false,
      enable_intensity: true,
      intensity: 20,
      enable_phase_quality: true,
      phase_quality_threshold: 0.1,
    };
  }
};

const normVector3d = (v) => {
  const len = Math.sqrt(v.x * v.x + v.y * v.y + v.z * v.z);
  return { x: v.x / len, y: v.y / len, z: v.z / len };
};

const subtractVector3d = (v1, v2) => {
  return { x: v1.x - v2.x, y: v1.y - v2.y, z: v1.z - v2.z };
};

const crossVector3d = (v0, v1) => {
  return {
    x: v0.y * v1.z - v0.z * v1.y,
    y: v0.z * v1.x - v0.x * v1.z,
    z: v0.x * v1.y - v0.y * v1.x
  };
};

const addVector3d = (v1, v2) => {
  return { x: v1.x + v2.x, y: v1.y + v2.y, z: v1.z + v2.z };
};

const scaleVector3d = (v, s) => {
  return { x: v.x * s, y: v.y * s, z: v.z * s };
};

const rotateVector3d = (rotation, v) => {
  return {
      x: rotation.basis_0.x * v.x + rotation.basis_1.x * v.y + rotation.basis_2.x * v.z,
      y: rotation.basis_0.y * v.x + rotation.basis_1.y * v.y + rotation.basis_2.y * v.z,
      z: rotation.basis_0.z * v.x + rotation.basis_1.z * v.y + rotation.basis_2.z * v.z
  };
}

/**
 * Get translation and rotation from selected points (for camera preview's custom coordinate system)
 * @param {Object} p0
 * @param {Object} p1
 * @param {Object} p2
 * @returns {Object} { translation: { x, y, z }, rotation: { basis_0, basis_1, basis_2 } }
 */
export const getTranslationAndRotationFromSelectedPoints = ({ p0, p1, p2 }) => {
  const position = {
    x: -p0.x,
    y: -p0.y,
    z: -p0.z,
  };

  const left =  normVector3d(subtractVector3d(p1, p0));
  let up = normVector3d(subtractVector3d(p2, p0));
  const forward = normVector3d(crossVector3d(left, up));
  up = normVector3d(crossVector3d(forward, left));

  return {
    translation: position,
    rotation: {
      basis_0: left,
      basis_1: up,
      basis_2: forward,
    },
  };
};

/**
 * Get points and transformed camera detail
 **/
export const getPointsAndTransformedCameraDetail = ({ translation, rotation, curCameraDetail }) => {
  const distance = 50;

  const p0 = { x: -translation.x, y: -translation.y, z: -translation.z };
  const p1 = addVector3d(p0, scaleVector3d(rotation.basis_0, distance));
  const p2 = addVector3d(p0, scaleVector3d(rotation.basis_1, distance));

  const newPostion = rotateVector3d(rotation, addVector3d(curCameraDetail.position, translation));

  const newLookAt = rotateVector3d(rotation, addVector3d(curCameraDetail.lookAt, translation));

  const upPoint = addVector3d(curCameraDetail.position, curCameraDetail.up);
  const newUpPoint = rotateVector3d(rotation, addVector3d(upPoint, translation));
  const newUp = normVector3d(subtractVector3d(newUpPoint, newPostion));

  return {
    points: {p0, p1, p2},
    transformedCameraDetail: {
      position: newPostion,
      lookAt: newLookAt,
      up: newUp,
    },
  };
};

/**
 * return random color by string, same string will return same color
 * @param {String} str 
 */
export const getColorByStr = (str) => {
  const hue = Number('0x' + md5(str).toString().substring(0, 10)) % 360;
  const storkeColor = `hsl(${hue}, 100%, 50%)`;
  return storkeColor;
};

export const sleep = (ms) => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

export const cropInferenceFrameThumbnailBasedOnFeatureRoi = async (productId, step, featureRoi) => {
  let res;

  try {
    res = await fetch(`${serverEndpoint}/frame?product_id=${productId}&step=${step}`);
  } catch (e) {
    handleRequestFailed('getFrame', e);
    return null;
  }

  let frameUri = await res.json();
  frameUri = _.get(frameUri, 'image.data_uri');
  
  try {
    res = await fetch(`${serverEndpoint}/data?data_uri=${frameUri}`);
  } catch (e) {
    handleRequestFailed('getFrame', e);
    return null;
  }

  // read frame
  const blob = await res.blob();
  const imageSrc = await new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result);
    reader.onerror = reject;
    reader.readAsDataURL(blob);
  });

  // dataurl to image
  let img = new Image();
  img = await new Promise((resolve, reject) => {
    img.onload = () => resolve(img);
    img.onerror = reject;
    img.src = imageSrc;
  });

  // create tmp canvas to crop image
  const c = document.createElement('canvas');
  const ctx = c.getContext('2d');
  const pMin = _.get(featureRoi, 'points.0');
  const pMax = _.get(featureRoi, 'points.1');
  const width = pMax.x - pMin.x;
  const height = pMax.y - pMin.y;
  c.width = width;
  c.height = height;
  ctx.drawImage(img, pMin.x, pMin.y, width, height, 0, 0, width, height);
  const result  = c.toDataURL();
  ctx.clearRect(0, 0, c.width, c.height);
  c.remove();
  return result;
};

export const cropFrameDataUrlBasedOnFeatureRoi = async (dataUrl, featureRoi) => {
  // dataurl to image
  let img = new Image();
  img = await new Promise((resolve, reject) => {
    img.onload = () => resolve(img);
    img.onerror = reject;
    img.src = dataUrl;
  });

  // create tmp canvas to crop image
  const c = document.createElement('canvas');
  const ctx = c.getContext('2d');
  const pMin = _.get(featureRoi, 'pMin');
  const pMax = _.get(featureRoi, 'pMax');
  const width = pMax.x - pMin.x;
  const height = pMax.y - pMin.y;
  c.width = width;
  c.height = height;
  ctx.drawImage(img, pMin.x, pMin.y, width, height, 0, 0, width, height);
  const result  = c.toDataURL();
  ctx.clearRect(0, 0, c.width, c.height);
  c.remove();
  return result;
};

export const mapRange = (value, inMin, inMax, outMin, outMax) => {
  return outMin + ((value - inMin) * (outMax - outMin)) / (inMax - inMin);
};

export const getFeatureTypeDisplayText = (featureType) => {
  return _.get(translationText, `viewInspection.featureTypes.${featureType}`) ? translation(`viewInspection.featureTypes.${featureType}`) : featureType;
};

// only used for InferenceDetail but can be used as reference
export const convertGrayScaleArrayBufferToGreenToRedImage = async (arrayBuffer, width, height) => {
  // convert grayscale array to green to red image
  // 0 means dl's good result so convert to transparent pixel
  // 1-255 map to green to red gradient

  const data = new Uint8Array(arrayBuffer);
  const c = document.createElement('canvas');
  c.width = width;
  c.height = height;
  const ctx = c.getContext('2d');
  const imgData = ctx.createImageData(width, height);
  const imgDataData = imgData.data;
  for (let i = 0; i < data.length; i++) {
    if (data[i] === 0) {
      imgDataData[i * 4] = 0;
      imgDataData[i * 4 + 1] = 0;
      imgDataData[i * 4 + 2] = 0;
      imgDataData[i * 4 + 3] = 0;
    } else {
      const ratio = data[i] / 255;
      imgDataData[i * 4] = 255;                           // Red channel stays 255 for both yellow and red
      imgDataData[i * 4 + 1] = Math.floor(255 * (1 - ratio)); // Green channel transitions from 255 (yellow) to 0 (red)
      imgDataData[i * 4 + 2] = 0;                         // Blue channel (unchanged)
      imgDataData[i * 4 + 3] = 255; // Alpha channel (fully opaque)
    }
  }

  ctx.putImageData(imgData, 0, 0);
  return c.toDataURL();
};

export const convertFeedbackCorrectnessToChoice = (correctness, prediction) => {
  if (!correctness) {
    return !prediction;
  } else {
    return prediction;
  }
};

export const generateFeatureTypeDisplayText = (featureType) => {
  return _.startsWith(featureType, '_') ? featureType.slice(1) : featureType;
};

export const toLocalISOString = (date) => {
  const pad = (num) => num.toString().padStart(2, '0');

  const year = date.getFullYear();
  const month = pad(date.getMonth() + 1); // Months are 0-based
  const day = pad(date.getDate());
  const hours = pad(date.getHours());
  const minutes = pad(date.getMinutes());
  const seconds = pad(date.getSeconds());
  const milliseconds = date.getMilliseconds().toString().padStart(3, '0');

  return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}.${milliseconds}Z`;
};

export const getTimeSinceMidnight = (date) => {
  return (
    date.getHours() * 60 * 60 * 1000 + // Convert hours to milliseconds
    date.getMinutes() * 60 * 1000 +   // Convert minutes to milliseconds
    date.getSeconds() * 1000 +        // Convert seconds to milliseconds
    date.getMilliseconds()            // Add milliseconds
  );
};

export const getLatestRetrainFinishTime = (res) => {
  // return the latest retrain finish time by model by
  // ex. res [ { model_type: 'DEFECT_MODEL', 'finished_at': '2025-01-27...', ... }, { ... }, ... ]
  // return { 'DEFECT_MODEL': 12456(timestamp), 'MDOEL2': 123456, ... }

  const result = {};

  for (const model of res) {
    const modelType = model.model_type;
    const finishedAt = convertBackendTimestampToMoment(model.finished_at).valueOf();
    if (_.has(result, modelType)) {
      if (finishedAt > result[modelType]) {
        result[modelType] = finishedAt;
      }
    } else {
      result[modelType] = finishedAt;
    }
  }

  return result;
};

export const isRevaluateTimeLaterThanLatestRetrainFinishTime = (lineItemRecord, latestRetrainFinishTime) => {
  // console.log('lineItemRecord', lineItemRecord);
  // console.log('latestRetrainFinishTime', latestRetrainFinishTime);
  // have not retrained yet
  if (_.isEmpty(latestRetrainFinishTime)) return true;
  if (_.isEmpty(lineItemRecord)) return false;

  // have not reevaluated yet
  if (_.isEmpty(_.get(lineItemRecord, 'reevaluation_result.updated_at', null))) return false;

  let retrainTime = 0;
  if (_.get(lineItemRecord, 'detail', '') === defectDetection) {
    retrainTime = _.get(latestRetrainFinishTime, retrainModelType.defectModel, 0);
  } else if (_.get(lineItemRecord, 'detail', '') === heightDiff) {
    retrainTime = _.get(latestRetrainFinishTime, retrainModelType.heightModel, 0);
  }

  const revaluateTime = convertBackendTimestampToMoment(_.get(lineItemRecord, 'reevaluation_result.updated_at', '')).valueOf();

  return revaluateTime > retrainTime;
};