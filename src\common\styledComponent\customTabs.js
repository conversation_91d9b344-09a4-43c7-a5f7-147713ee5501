import React from 'react';
import styled from 'styled-components';


const CustomTabs = (props) => {
  const {
    items, // [{key: '1', label: 'Tab 1'}, {key: '2', label: 'Tab 2'}]
    activeKey,
    onChange,
  } = props;

  return (
    <Container>
      {items.map((item, index) => {
        return (
          <SingleTab
            key={item.key}
            isActive={item.key === activeKey}
            onClick={() => onChange(item.key)}
          >
            <span className={`font-source text-[12px] font-${item.key === activeKey ? 'semibold' : 'normal'} ${item.key === activeKey ? 'text-white' : 'text-gray-4'}`}>
              {item.label}
            </span>
          </SingleTab>
        );
        // if (!item.disabled) {
        //   return (<SingleTab
        //     key={item.key}
        //     isActive={item.key === activeKey}
        //     onClick={() => onChange(item.key)}
        //   >
        //     <span className={`font-source text-[12px] font-${item.key === activeKey ? 'semibold' : 'normal'} ${item.key === activeKey ? 'text-white' : 'text-gray-4'}`}>
        //       {item.label}
        //     </span>
        //   </SingleTab>);
        // } else {
        //   return (<DisabledTab key={item.key}>
        //     <Title size='h4' weight='bold'>
        //       {item.label}
        //     </Title>
        //   </DisabledTab>);
        // }
      })}
    </Container>
  );
};

const Container = styled.div`
  display: flex;
  align-items: flex-start;
  gap: 10px;
  width: 100%;
`;

const SingleTab = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1 0 0;
  height: 35px;

  border-radius: 2px 2px 0px 0px;
  ${props => props.isActive && `border-bottom: 2px solid #ffffff;`}
  ${props => props.isActive && `box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.25);`}
  ${props => !props.isActive && `opacity: 0.38;`}
  cursor: pointer;
`;

const DisabledTab = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1 0 0;
  height: 35px;
  border-radius: 2px 2px 0px 0px;
  opacity: 0.38;
  cursor: not-allowed;
`;


export default CustomTabs;