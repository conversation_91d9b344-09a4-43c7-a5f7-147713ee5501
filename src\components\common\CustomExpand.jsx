import _ from 'lodash';
import React, { useState } from 'react';


const CustomExpand = (props) => {
  const {
    title,
    children,
    onExpand,
    isExpanded,
  } = props;

  const [internalExpanded, setInternalExpanded] = useState(false);

  return (
    <div className='flex flex-col gap-1 self-stretch items-start transition-all duration-300'>
      <div
        className='flex py-0.5 items-center self-stretch cursor-pointer'
        onClick={() => {
          onExpand();
          if (_.isUndefined(isExpanded)) {
            setInternalExpanded(!internalExpanded);
          }
        }}
      >
        <div className='flex h-6 w-6 flex-col justify-center items-center gap-2.5'>
          <img
            className='w-[14px] h-[7px] shrink-0 transition-transform duration-300'
            src='/img/icn/icn_arrowRight_white.svg'
            alt='arrow'
            style={{
              transform: (_.isUndefined(isExpanded) ? internalExpanded: isExpanded) ? 'rotate(90deg)' : 'rotate(0deg)',
            }}
          />
        </div>
        <div
          className='flex items-center self-stretch'
          style={{
            lineHeight: 'normal',
          }}
        >
          {title}
        </div>
      </div>
      { (_.isUndefined(isExpanded) ? internalExpanded: isExpanded) && children }
    </div>
  );
};

export default CustomExpand;