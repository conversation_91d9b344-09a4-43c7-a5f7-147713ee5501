import { ColorPicker, Dropdown, Input, InputNumber, Slider, Switch, Tooltip } from 'antd';
import _ from 'lodash';
import React, { Fragment, useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { setContainerTransparentLockEnabled, setIsViewLiveFullScreenEnabled } from '../../actions/setting';
import { ALERT_TYPES, aoiAlert } from '../../common/alert';
import { translation } from '../../common/util';
import InferenceMaskAnnotateViewer from '../common/viewer/InferenceMaskAnnotateViewer';
import InferenceStepViewer from '../common/viewer/InferenceStepViewer';
import FullscreenOutlined from '@ant-design/icons/FullscreenOutlined';
import FileImageOutlined from '@ant-design/icons/FileImageOutlined';


const InferenceDetailScene = (props) => {
  const {
    dataUri,
    step,
    ipcProductId,
    goldenProductId,
    sessionStepInfo,
    selectedFeatureId,
    setSelectedFeatureId,
    setSelectedLineItem,
    isDrawMaskEnabled,
    canvasRef,
    viewerRef,
    drawMaskViewerRef,
    drawMaskCanvasRef,
    curSessionStepInfo,
    curDisplayOptionsBrightness,
    setCurDisplayOptionsBrightness,
    curDisplayOptionsContrast,
    setCurDisplayOptionsContrast,
    curDisplayOptionsSaturation,
    setCurDisplayOptionsSaturation,
    isSharpnessEnabled,
    setIsSharpnessEnabled,
    goldenProdFeatures,
    curSession,
    curIpc,
    isFullScreen,
    isLive,
    selectedLineItem,
  } = props;

  const dispatch = useDispatch();

  // const [getFeatures] = useLazyGetAllFeaturesByProductIdAndStepQuery();

  const [drawingMode, setDrawingMode] = useState('pan'); // pencil, eraser, pan
  const [pencilStrokeWidth, ********************] = useState(10.0);
  const [pencilStrokeColor, setPencilStrokeColor] = useState('#F2C94C');

  useEffect(() => {
    if (_.isEmpty(dataUri) || _.isEmpty(sessionStepInfo)) return;
    if (!viewerRef.current || !canvasRef.current) return;

    const loadSceneAndFeatures = async (dataUri, sessionStepInfo, goldenProdFeatures) => {
      dispatch(setContainerTransparentLockEnabled(true));
      
      // roi info is in get allFeatures but we only know if the feature is defected or not in sessionStepInfo
      const features = [];
      _.forEach(goldenProdFeatures, (feature) => {
        const defectedFeature = _.find(
          sessionStepInfo,
          (defect) => defect.feature_id === feature.feature_id && !defect.pass
        );
        features.push({
          feature,
          pass: _.isEmpty(defectedFeature),
        });
      });

      // console.log('allFeatures', _.get(res, 'data', []));
      // console.log('sessionStepInfo', sessionStepInfo);
      // console.log('defectedFeatures', defectedFeatures);

      // viewerRef.current.clearScene();
      viewerRef.current.loadSceneAndFeatures(
        dataUri,
        features,
        () => dispatch(setContainerTransparentLockEnabled(false)),
      );
    };

    loadSceneAndFeatures(dataUri, sessionStepInfo, goldenProdFeatures);
  }, [dataUri, sessionStepInfo, goldenProdFeatures]);

  useEffect(() => {
    if (_.isEmpty(dataUri) || _.isEmpty(ipcProductId) || _.isEmpty(step)) return;
    if (!drawMaskViewerRef.current || !drawMaskCanvasRef.current) return;
    if (!isDrawMaskEnabled) {
      drawMaskViewerRef.current.handleChangeMode('pan');
      setDrawingMode('pan');
    }
    if (!isDrawMaskEnabled || !curSessionStepInfo) return;

    const loadCroppedFrame = async () => {
      const selectedFeature = _.find(goldenProdFeatures, (feature) => String(feature.feature_id) === String(selectedFeatureId));

      if (!selectedFeature) {
        aoiAlert(translation('notification.error.selectedFeatureNotFound'), ALERT_TYPES.COMMON_ERROR);
        return;
      }

      drawMaskViewerRef.current.clearScene();
      drawMaskViewerRef.current.loadCroppedFrame(
        dataUri,
        selectedFeature,
        _.get(curSessionStepInfo, 'feedback.defect_mask_uri'),
      );
    };

    loadCroppedFrame();
  }, [isDrawMaskEnabled, curSessionStepInfo, goldenProdFeatures]);

  return (
    <Fragment>
      <div className='relative w-full h-full'>
        {/* scene display starts */}
        <div className={`w-full h-full absolute top-0 left-0 ${isDrawMaskEnabled ? 'z-[1]' : 'z-[2]'} bg-[#000000]`}>
          <InferenceStepViewer
            displayCanvasRef={canvasRef}
            ref={viewerRef}
            setSelectedFeatureId={setSelectedFeatureId}
            setSelectedLineItem={setSelectedLineItem}
            selectedFeatureId={selectedFeatureId}
            curDisplayOptionsBrightness={curDisplayOptionsBrightness}
            curDisplayOptionsContrast={curDisplayOptionsContrast}
            curDisplayOptionsSaturation={curDisplayOptionsSaturation}
            isSharpnessEnabled={isSharpnessEnabled}
            curSession={curSession}
            curIpc={curIpc}
            sessionStepInfo={sessionStepInfo}
            isFullScreen={isFullScreen}
            selectedLineItem={selectedLineItem}
          />
        </div>
        {/* scene display ends */}
        {/* current product pass status starts */}
        {/* <div className={`absolute top-4 left-4 ${isDrawMaskEnabled ? 'z-[1]' : 'z-[3]'}`}>
          <div
            className={`flex py-3 px-6 flex-col justify-center items-center gap-1 rounded-[8px] border-[1px]`}
            style={{
              background: `${_.get(curIpc, 'defect_count') === 0 ? 'rgba(39, 174, 96, 0.75)' : 'rgba(235, 87, 87, 0.75)'}`,
              borderColor: `${_.get(curIpc, 'defect_count') === 0 ? '#81F499' : '#eb5757'}`,
            }}
          >
            <div className='flex items-center gap-2 justify-center'>
              <span className='font-source text-[20px] font-semibold'>
                {`${translation('viewInspection.currentProdInspResult')} ${_.get(curIpc, 'defect_count') === 0 ? translation('viewInspection.pass') : translation('viewInspection.defective')}`}
              </span>
            </div>
          </div>
        </div> */}
        {/* current product pass status ends */}
        {/* scene controllers starts */}
        <div className={`absolute bottom-4 left-4 ${isDrawMaskEnabled ? 'z-[1]' : 'z-[3]'} `}> 
          <div
            className='flex gap-2 items-center self-stretch p-2 rounded-[4px]'
            // style={{
            //   background: 'rgba(0, 0, 0, 0.70)',
            //   boxShadow: '0px 0px 20px 0px rgba(0, 0, 0, 0.25)'
            // }}
          >
            {isLive &&
              <Tooltip
                title={
                  <span className='font-source text-[12px] font-normal'>
                    {isFullScreen ? translation('common.exitFullScreenMode') : translation('common.fullScreenMode')}
                  </span>
                }
              >
                <div
                  className='flex w-6 h-6 items-center justify-center cursor-pointer bg-[#2D2D2D] rounded-[4px]'
                  onClick={() => {
                    setIsSharpnessEnabled(false);
                    dispatch(setIsViewLiveFullScreenEnabled(isFullScreen ? false : true))
                  }}
                >
                  <FullscreenOutlined
                    width={12}
                    height={12}
                  />
                </div>
              </Tooltip>
            }
            <Tooltip
              title={
                <span className='font-source text-[12px] font-normal'>
                  {translation('common.showFullView')}
                </span>
              }
            >
              <div
                className='flex w-6 h-6 items-center justify-center cursor-pointer bg-[#2D2D2D] rounded-[4px]'
                onClick={() => { if (!isDrawMaskEnabled && viewerRef.current) viewerRef.current.resetView(); }}
              >
                <img className='w-3 h-3' src='/img/icn/icn_resetZoom_white.svg' alt='pencil' />
              </div>
            </Tooltip>
            <Dropdown
              overlay={<InferenceImageDisplayOptions
                curDisplayOptionsBrightness={curDisplayOptionsBrightness}
                setCurDisplayOptionsBrightness={setCurDisplayOptionsBrightness}
                curDisplayOptionsContrast={curDisplayOptionsContrast}
                setCurDisplayOptionsContrast={setCurDisplayOptionsContrast}
                curDisplayOptionsSaturation={curDisplayOptionsSaturation}
                setCurDisplayOptionsSaturation={setCurDisplayOptionsSaturation}
                isSharpnessEnabled={isSharpnessEnabled}
                setIsSharpnessEnabled={setIsSharpnessEnabled}
              />}
              placement='topLeft'
            >
              <div className='flex w-6 h-6 items-center justify-center cursor-pointer bg-[#2D2D2D] rounded-[4px]'>
                <img className='w-3 h-3' src='/img/icn/icn_setting_white.svg' alt='setting' />
              </div>
            </Dropdown>
            <Tooltip
              title={
                <span className='font-source text-[12px] font-normal'>
                  { isSharpnessEnabled ? translation('common.disableSharpness') : translation('common.enableSharpness')}
                </span>
              }
            >
              <div
                className={`flex w-6 h-6 items-center justify-center cursor-pointer ${!isSharpnessEnabled ? 'bg-[#2D2D2D]' : 'bg-AOI-blue'} rounded-[4px]`}
                onClick={() => setIsSharpnessEnabled(!isSharpnessEnabled)}
              >
                {/* <img className='w-3 h-3' src='/img/icn/icn_close_white.svg' alt='close' /> */}
                <FileImageOutlined
                  width={12}
                  height={12}
                />
              </div>
            </Tooltip>
          </div>
        </div>
        {/* scene controllers ends */}
        {/* mask drawing starts */}
        {/* <div className={`w-full h-full absolute top-0 left-0 ${isDrawMaskEnabled ? 'z-[2]' : 'z-[1]'} bg-[#000000]`}>
          <InferenceMaskAnnotateViewer
            displayCanvasRef={drawMaskCanvasRef}
            ref={drawMaskViewerRef}
            drawingMode={drawingMode}
            pencilStrokeWidth={pencilStrokeWidth}
            pencilStrokeColor={pencilStrokeColor}
            curDisplayOptionsBrightness={curDisplayOptionsBrightness}
            curDisplayOptionsContrast={curDisplayOptionsContrast}
            curDisplayOptionsSaturation={curDisplayOptionsSaturation}
            isSharpnessEnabled={isSharpnessEnabled}
          />
        </div> */}
        {/* mask drawing ends */}
        {/* mask drawing controllers starts */}
        <div className={`absolute bottom-4 left-4 ${isDrawMaskEnabled ? 'z-[3]' : 'z-[1]'} `}>
          <div
            className='flex w-[232px] py-2 px-4 flex-col items-start gap-2 rounded-[4px]'
            style={{
              background: 'rgba(0, 0, 0, 0.70)',
              boxShadow: '0px 0px 20px 0px rgba(0, 0, 0, 0.25)'
            }}
          >
            <div className='flex items-start gap-1 self-stretch'>
              <div className='flex p-0.5 items-center'>
                <img className='w-4 h-4' src='/img/icn/icn_info_white.svg' alt='info' />
              </div>
              <div className='flex items-center gap-1 self-stretch flex-wrap'>
                <span className='font-source text-[12px] font-bold'>
                  {drawingMode === 'pencil' && translation('viewInspection.pencilTool')}
                  {drawingMode === 'eraser' && translation('viewInspection.eraserTool')}
                  {drawingMode === 'pan' && translation('viewInspection.panTool')}
                </span>

                <span className='font-source text-[12px] font-normal'>
                  {drawingMode === 'pencil' && translation('viewInspection.pencilToolDesc')}
                  {drawingMode === 'eraser' && translation('viewInspection.eraserToolDesc')}
                  {drawingMode === 'pan' && translation('viewInspection.panToolDesc')}
                </span>
              </div>
            </div>
            <div className='flex px-6 items-start gap-2 self-stretch'>
              <div
                className={`flex w-8 h-8 items-center justify-center cursor-pointer ${drawingMode === 'pan' ? 'bg-[#2D2D2D]' : 'bg-[#000000]'} rounded-[4px]`}
                onClick={() => setDrawingMode('pan')}
              >
                {drawingMode === 'pan' ?
                  <img className='w-3 h-3' src='/img/icn/icn_cursorPointe_blue.svg' alt='pencil' />
                  : <img className='w-3 h-3' src='/img/icn/icn_cursorPointer_white.svg' alt='pencil' />
                }
              </div>
              <div
                className={`flex w-8 h-8 items-center justify-center cursor-pointer ${drawingMode === 'pencil' ? 'bg-[#2D2D2D]' : 'bg-[#000000]'} rounded-[4px]`}
                onClick={() => setDrawingMode('pencil')}
              >
                {drawingMode === 'pencil' ?
                  <img className='w-3 h-3' src='/img/icn/icn_pencil_blue.svg' alt='pencil' />
                  : <img className='w-3 h-3' src='/img/icn/icn_pencil_white.svg' alt='pencil' />
                }
              </div>
              <div
                className={`flex w-8 h-8 items-center justify-center cursor-pointer ${drawingMode === 'eraser' ? 'bg-[#2D2D2D]' : 'bg-[#000000]'} rounded-[4px]`}
                onClick={() => setDrawingMode('eraser')}
              >
                {drawingMode === 'eraser' ?
                  <img className='w-3 h-3' src='/img/icn/icn_erase_blue.svg' alt='eraser' />
                  : <img className='w-3 h-3' src='/img/icn/icn_erase_white.svg' alt='eraser' />
                }
              </div>
              <div className='flex w-8 h-8 items-center justify-center cursor-pointer'
                onClick={() => {
                  if (drawMaskViewerRef.current) drawMaskViewerRef.current.resetView();
                }}
              >
                <img className='w-3 h-3' src='/img/icn/icn_resetZoom_white.svg' alt='pencil' />
              </div>
              <Dropdown
                overlay={<InferenceImageDisplayOptions
                  curDisplayOptionsBrightness={curDisplayOptionsBrightness}
                  setCurDisplayOptionsBrightness={setCurDisplayOptionsBrightness}
                  curDisplayOptionsContrast={curDisplayOptionsContrast}
                  setCurDisplayOptionsContrast={setCurDisplayOptionsContrast}
                  curDisplayOptionsSaturation={curDisplayOptionsSaturation}
                  setCurDisplayOptionsSaturation={setCurDisplayOptionsSaturation}
                  isSharpnessEnabled={isSharpnessEnabled}
                  setIsSharpnessEnabled={setIsSharpnessEnabled}
                />}
                placement='topLeft'
              >
                <div className='flex w-8 h-8 items-center justify-center'>
                  <img className='w-3 h-3' src='/img/icn/icn_setting_white.svg' alt='reset' />
                </div>
              </Dropdown>
            </div>
            <div className='w-full h-[1px] bg-[#333]' />
            <span className='font-source text-[12px] font-semibold self-stretch'>
              {translation('viewInspection.stroke')}
            </span>
            <div className='flex flex-col items-start self-stretch'>
              <div className='flex py-0.5 items-center gap-2 self-stretch'>
                <div className='flex w-6 h-6 items-center justify-center gap-2.5'>
                  <img src='/img/icn/icn_zond_white.svg' alt='zond' className='w-3 h-3' />
                </div>
                <InputNumber
                  controls={false}
                  min={0.01}
                  step={0.01}
                  max={100}
                  size='small'
                  style={{ width: '84px' }}
                  value={pencilStrokeWidth}
                  onChange={(value) => ********************(value)}
                />
                <Slider
                  min={0.01}
                  max={100}
                  step={0.01}
                  style={{ width: '84px' }}
                  value={pencilStrokeWidth}
                  onChange={(value) => ********************(value)}
                />
              </div>
              <div className='flex py-0.5 items-center gap-2 self-stretch'>
                <div className='flex w-6 h-6 items-center justify-center gap-2.5'>
                  <ColorPicker
                    size='small'
                    value={pencilStrokeColor}
                    onChange={(color) => {
                      setPencilStrokeColor(color.toHexString());
                    }}
                  />
                </div>
                <Input
                  size='small'
                  style={{ width: '76px' }}
                  disabled
                  value={pencilStrokeColor}
                />
              </div>
            </div>
          </div>
        </div>
        {/* mask drawing controllers ends */}
      </div>
    </Fragment>
  )
};

const InferenceImageDisplayOptions = (props) => {
  const {
    curDisplayOptionsBrightness,
    setCurDisplayOptionsBrightness,
    curDisplayOptionsContrast,
    setCurDisplayOptionsContrast,
    curDisplayOptionsSaturation,
    setCurDisplayOptionsSaturation,
    isSharpnessEnabled,
    setIsSharpnessEnabled,
  } = props;

  return (
    <div className='flex p-2 flex-col gap-2 w-[220px] bg-[#2D2D2D] rounded-[4px]'>
      <div className='flex justify-between items-center gap-2'>
        <span className='font-source text-[12px] font-semibold'>
          {translation('viewInspection.displayOptions')}
        </span>
        <div
          className='flex items-center justify-center h-8 w-8 cursor-pointer'
          onClick={() => {
            setCurDisplayOptionsBrightness(50);
            setCurDisplayOptionsContrast(50);
            setCurDisplayOptionsSaturation(50);
            setIsSharpnessEnabled(false);
          }}
        >
          <img src='/img/icn/icn_reset_white.svg' alt='reset' className='w-3 h-3' />
        </div>
      </div>
      <div className='flex flex-col self-stretch'>
        <span className='font-source text-[12px] font-normal'>
          {translation('viewInspection.brightness')}
        </span>
        <Slider
          value={curDisplayOptionsBrightness}
          min={0}
          max={100}
          step={1}
          onChange={(value) => setCurDisplayOptionsBrightness(value)}
        />
      </div>
      <div className='flex flex-col self-stretch'>
        <span className='font-source text-[12px] font-normal'>
          {translation('viewInspection.contrast')}
        </span>
        <Slider
          value={curDisplayOptionsContrast}
          min={0}
          max={100}
          step={1}
          onChange={(value) => setCurDisplayOptionsContrast(value)}
        />
      </div>
      <div className='flex flex-col self-stretch'>
        <span className='font-source text-[12px] font-normal'>
          {translation('viewInspection.saturation')}
        </span>
        <Slider
          value={curDisplayOptionsSaturation}
          min={0}
          max={100}
          step={1}
          onChange={(value) => setCurDisplayOptionsSaturation(value)}
        />
      </div>
      {/* <div className='flex flex-col self-stretch'>
        <div className='flex items-center gap-2'>
          <span className='font-source text-[12px] font-normal'>
            {translation('viewInspection.sharpness')}
          </span>
          <Tooltip
            title={translation('viewInspection.sharpnessDesc')}
          >
            <img src='/img/icn/icn_info_white.svg' alt='info' className='w-3 h-3' />
          </Tooltip>
        </div>
        <div className='flex items-center justify-between'>
          <Switch
            size='small'
            checked={isSharpnessEnabled}
            onChange={(checked) => setIsSharpnessEnabled(checked)}
          />
        </div>
      </div> */}
    </div>
  );
};

export default InferenceDetailScene;