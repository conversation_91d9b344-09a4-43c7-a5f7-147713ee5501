/** @type {import('tailwindcss').Config} */
module.exports = {
	content: ['./src/**/*.{js,ts,jsx,tsx}'],
	theme: {
		extend: {
			colors: {
        'blue-3': '#56CCF2',
        'gray-1': '#333',
        'gray-2': '#4F4F4F',
        'gray-3': '#828282',
        'gray-6': '#F2F2F2',
        'red': '#EB5757',
				'AOI-blue': '#56CCF2',
        'AOI-yellow': '#F4E76E',
        'AOI-green': '#81F499',
				'AOI-blue-hover': '#3C8EA9',
				'AOI-green-hover': '#5AAA6B',
				'AOI-yellow-hover': '#AAA14D',
				'AOI-red-hover': '#eb575780',
				'default-yellow': '#F2C94C',
				'default-purple-2': '#BB6BD9',
			},
			boxShadow: {
				modal: '0px 0px 10px 0px rgba(0, 0, 0, 0.25)',
				input: '0px 0px 2px 0px rgba(0, 0, 0, 0.15)',
				card: '0px 0px 4px 0px rgba(0, 0, 0, 0.25)',
				dropdown:
					'0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12)',
				banner: '0px 2px 2px 0px rgba(0, 0, 0, 0.20)',
			},
			backgroundOpacity: {
				15: '0.15',
			},
			fontSize: {
				xxs: '0.625rem',
			},
			fontFamily: {
				rubik: ['Rubik', 'sans-serif'],
				source: ['Source Sans Pro', 'sans-serif'],
				inter: ['Inter', 'sans-serif'],
			},
		},
	},
	plugins: [],
};
