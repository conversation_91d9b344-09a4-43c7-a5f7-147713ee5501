import { createApi } from '@reduxjs/toolkit/query/react';
import { baseQuery } from './api';


export const calibrationApi = createApi({
  reducerPath: 'calibrationApi',
  baseQuery,
  tagTypes: ['Calibration'],
  endpoints: (build) => ({
    initializeCalibration: build.mutation({
      query: (body) => {
        return {
          url: '/camera/initializeCalibration',
          method: 'PUT',
          body,
        };
      }
    }),
    addCalibrationPattern: build.mutation({
      query: (body) => {
        return {
          url: '/camera/addCalibrationPattern',
          method: 'POST',
          body,
        };
      }
    }),
    removeLastCalibrationPattern: build.mutation({
      query: (body) => {
        return {
          url: '/camera/removeLastCalibrationPattern',
          method: 'DELETE',
          params: body,
        };
      }
    }),
    getCalibrationPatternCount: build.query({
      query: ({ capture_step, id }) => ({
        url: '/camera/calibrationPatternCount',
        method: 'GET',
        params: { capture_step, id },
      }),
    }),
    updateCalibration: build.mutation({
      query: (body) => ({
        url: '/camera/updateCalibration',
        method: 'PUT',
        params: { id: body.id },
      }),
    }),
    evaluateCalibration: build.mutation({
      query: (body) => ({
        url: '/camera/evaluateCalibration',
        method: 'PUT',
        body,
      }),
    }),
  }),
});

export const {
  useInitializeCalibrationMutation,
  useAddCalibrationPatternMutation,
  useRemoveLastCalibrationPatternMutation,
  useGetCalibrationPatternCountQuery,
  useLazyGetCalibrationPatternCountQuery,
  useUpdateCalibrationMutation,
  useEvaluateCalibrationMutation,
} = calibrationApi;