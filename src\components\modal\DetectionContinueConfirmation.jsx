import React, { useState } from 'react';
import { activeGreen, DarkButton, DarkModal, defaultGreen } from '../../common/darkModeComponents';
import { translation } from '../../common/util';
import _ from 'lodash';


const DetectionContinueConfirmation = (props) => {
  const {
    isOpened,
    setIsOpened,
    productId,
    handleRunDetection,
    productInfo,
  } = props;

  const [isLoading, setIsLoading] = useState(false);

  return (
    <DarkModal
      primaryColor={'#57F2C4'}
      open={isOpened}
      onCancel={() => {
        if (isLoading) return;
        setIsOpened(false);
        window.location.href = '/aoi/manage-product';
      }}
      title={
        <span className='font-source text-[16px] font-semibold'>
          {translation('imageCaptureRequired.userActionRequired')}
        </span>
      }
      footer={
        <div className='flex flex-col items-start self-stretch gap-2'>
          <DarkButton
            bgColor={defaultGreen}
            hoverBgColor={activeGreen}
            borderColor={defaultGreen}
            hoverTextColor={'#333'}
            hoverBorderColor={activeGreen}
            textColor={'#333'}
            style={{ width: '100%' }}
            loading={isLoading}
            onClick={() => {
              if (_.isEmpty(productId)) return;
              setIsLoading(true);
              handleRunDetection(productId, _.get(productInfo, 'product_name', ''));
            }}
          >
            {translation('common.continue')}
          </DarkButton>
          <DarkButton
            bgColor={defaultGreen}
            hoverBgColor={activeGreen}
            borderColor={defaultGreen}
            hoverBorderColor={activeGreen}
            hoverTextColor={'#333'}
            textColor={'#333'}
            style={{ width: '100%' }}
            loading={isLoading}
            onClick={() => {
              setIsOpened(false);
              window.location.href = '/aoi/manage-product';
            }}
          >
            {translation('common.stop')}
          </DarkButton>
        </div>
      }
    >
      <div className='flex py-6 px-4 flex-col items-start gap-8 self-stretch'>
        <span className='text-source text-[14px] font-normal'>
          {translation('imageCaptureRequired.positionTheNextPart')}
        </span>
      </div>
    </DarkModal>
  );
};

export default DetectionContinueConfirmation;