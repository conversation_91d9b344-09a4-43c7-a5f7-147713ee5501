import React, { useContext, useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import CameraPreviewViewer from '../common/viewer/CameraPreviewViewer';
import { debounce, handleRequestFailed, loadAndDecodePoints } from '../../common/util';
import { CameraPreviewContext } from '../Context/Provider';
import _ from 'lodash';
import { setCurCoordSystemSelectedPointPosition, setCurCoordSystemSelectingPointIndex } from '../../actions/camera';
import { serverEndpoint } from '../../common/const';


const PreviewPointcloud = (props) => {
  const {
    viewer,
    setViewer,
    frames,
  } = props;

  const dispatch = useDispatch();

  const dimensionCalcRef = useRef();
  const canvasRef = useRef();

  const selectedCameraView = useSelector((state) => state.camera.selectedCameraView);
  const isCoordSystemSelectingPoints = useSelector((state) => state.camera.isCoordSystemSelectingPoints);
  const curCoordSystemSelectingPointIndex = useSelector((state) => state.camera.curCoordSystemSelectingPointIndex);

  const {
    cameraPreviewPointCloudData
  } = useContext(CameraPreviewContext);

  const handleCoordSystemSelectingPoint = () => {
    if (!viewer || !canvasRef.current) return;
    const position = viewer.getFirstIntersectedPoint();
    dispatch(setCurCoordSystemSelectedPointPosition({ x: position.x, y: position.y, z: position.z }));
    // wait for CameraPreview component update the position then set the index to null there
    // dispatch(setCurCoordSystemSelectingPointIndex(null));
    viewer.addCustomCoordSystemPoint(position, Number(curCoordSystemSelectingPointIndex));
    canvasRef.current.removeEventListener('mousedown', handleCoordSystemSelectingPoint);
  };

  const fetchAndDecodePointCloudData = async (pointCloudUri, viewer) => {
    let res;
    try {
      res = await loadAndDecodePoints(`${serverEndpoint}/data?data_uri=${pointCloudUri}`);
      // dataRes = await loadAndDecodePoints('/test/165479lYqDEx.flatbuffer');
    } catch (error) {
      handleRequestFailed('fetchPointCloudData', error);
      return;
    }
    viewer.clearScene();
    viewer.loadScene(res);
  };

  useEffect(() => {
    if (!viewer || !_.isNull(selectedCameraView) && _.isEmpty(_.get(cameraPreviewPointCloudData, `${selectedCameraView}`))) return;
    viewer.clearScene();
    viewer.loadScene(cameraPreviewPointCloudData[selectedCameraView]);
  }, [selectedCameraView, cameraPreviewPointCloudData]);

  useEffect(() => {
    if (!viewer || !canvasRef.current) return;
    if (!_.isNull(curCoordSystemSelectingPointIndex)) {
      canvasRef.current.addEventListener('mousedown', handleCoordSystemSelectingPoint);
    } else {
      canvasRef.current.removeEventListener('mousedown', handleCoordSystemSelectingPoint);
    }
  }, [curCoordSystemSelectingPointIndex, viewer]);

  useEffect(() => {
    if (!viewer || _.isNull(selectedCameraView) || _.isEmpty(_.get(frames, `${selectedCameraView}.pointCloudUri`))) return;

    fetchAndDecodePointCloudData(_.get(frames, `${selectedCameraView}.pointCloudUri`), viewer);
  }, [frames]);

  useEffect(() => {
    // init 3d scene
    if (!canvasRef.current || !dimensionCalcRef.current) return;

    let curViewer = new CameraPreviewViewer(
      canvasRef.current,
      dimensionCalcRef.current.offsetHeight,
      dimensionCalcRef.current.offsetWidth,
      () => ({ width: dimensionCalcRef.current.offsetWidth, height: dimensionCalcRef.current.offsetHeight })
    );
    setViewer(curViewer);

    // if (!_.isNull(selectedCameraView) && !_.isEmpty(_.get(cameraPreviewPointCloudData, `${selectedCameraView}`))) {
    if (!_.isNull(selectedCameraView) && !_.isEmpty(_.get(frames, `${selectedCameraView}.pointCloudUri`))) {
      // load point cloud data
      // curViewer.loadScene(cameraPreviewPointCloudData[selectedCameraView]);
      fetchAndDecodePointCloudData(_.get(frames, `${selectedCameraView}.pointCloudUri`), curViewer);
    }

    const updateCanvasDimension = () => {
      if (!curViewer || !dimensionCalcRef.current) return;
      curViewer.updateSceneSize(dimensionCalcRef.current.offsetWidth, dimensionCalcRef.current.offsetHeight);
    };

    const debounceUpdateCanvasDimension = debounce(updateCanvasDimension, 300);   
    
    window.addEventListener('resize', debounceUpdateCanvasDimension);

    return () => {
      if (curViewer) {
        // console.log('dispose camera preview display gpu resource');
        curViewer.clearScene();
        curViewer.destroy();
        curViewer = null;
        setViewer(null);
        window.removeEventListener('resize', debounceUpdateCanvasDimension);
      }
    };
  }, []);

  return (
    <div className='relative h-full w-full'>
      <div className='absolute w-full h-full z-[15]'>
        <canvas ref={canvasRef} />
      </div>
      <div className='absolute w-full h-full z-[10]' ref={dimensionCalcRef} />
    </div>
  );
};

export default PreviewPointcloud;