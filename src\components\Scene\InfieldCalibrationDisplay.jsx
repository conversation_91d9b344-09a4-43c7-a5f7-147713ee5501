import React, { useEffect, useRef } from 'react';
import InfieldCalibrationViewer from '../common/viewer/InfieldCalibrationViewer';
import _ from 'lodash';
import { serverEndpoint } from '../../common/const';


const InfieldCalibrationDisplay = (props) => {
  const {
    curFrame
  } = props;
  
  const displayCanvasRef = useRef(null);
  const viewerRef = useRef(null);

  const fetchAndLoad = async (imgUrl, viewerRef) => {
    if (_.isEmpty(imgUrl) || !viewerRef.current) return;

    const res = await fetch(`${serverEndpoint}/data?data_uri=${imgUrl}`);
    const blob = await res.blob();
    const reader = new FileReader();
    reader.readAsDataURL(blob);
    const dataUrl = await new Promise((resolve) => {
      reader.onload = (event) => resolve(event.target.result);
    });

    viewerRef.current.loadScene(dataUrl);
  };

  useEffect(() => {
    if (!_.isEmpty(curFrame) && viewerRef.current) {
      fetchAndLoad(_.get(curFrame, 'image.data_uri'), viewerRef);
    }
  }, [curFrame]);

  return (
    <InfieldCalibrationViewer
      displayCanvasRef={displayCanvasRef}
      ref={viewerRef}
    />
  );
};

export default InfieldCalibrationDisplay;