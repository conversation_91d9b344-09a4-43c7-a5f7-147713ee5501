import { PlusOutlined, SearchOutlined, UploadOutlined } from '@ant-design/icons';
import { Button, Upload } from 'antd';
import _ from 'lodash';
import React, { useEffect, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import { DarkInput, DarkTable, RedPrimaryButtonConfigProvider } from '../../common/darkModeComponents';
import { handleRequestFailed, translation } from '../../common/util';
import { useGetAllProductsQuery } from '../../services/product';
import { systemApi } from '../../services/system';
import MultiViewGridSelection from '../common/MultiViewGridSelection';
import MainMenuLayout from '../layout/MainMenuLayout';
import ManageBoardsLayout from '../layout/ManageBoardsLayout';
import DeleteProductConfirmation from '../modal/DeleteProductConfirmation';
import NewBoardModal from '../modal/NewProduct';
import ExportGoldenProductDefinition from '../modal/ExportGoldenProductDefinition';
import { ALERT_TYPES, aoiAlert } from '../../common/alert';
import { serverEndpoint } from '../../common/const';
import { FileSearchOutlined } from '@ant-design/icons';


const ManageProduct = (props) => {
  const tableContainerRef = useRef();
  const handleResizeRef = useRef();

  const [boardSearchQuery, setBoardSearchQuery] = useState('');
  const [isCreateBoardModalOpened, setIsCreateBoardModalOpened] = useState(false);
  const [filteredProducts, setFilteredProducts] = useState([]);
  const [isDeleteProductModalOpened, setIsDeleteProductModalOpened] = useState(false);
  const [selectedDeleteProductId, setSelectedDeleteProductId] = useState(null);
  const [selectedDeleteProductName, setSelectedDeleteProductName] = useState(null);
  const [pageSize, setPageSize] = useState(0);
  const [isExportGoldenProductDefinitionModalOpened, setIsExportGoldenProductDefinitionModalOpened] = useState(false);
  
  const { data: allProducts, isLoading, isFetching, refetch: refetchAllProd } = useGetAllProductsQuery();

  const { data: systemMetadata } = useSelector((state) => systemApi.endpoints.getSystemMetadata.select()(state));

  const columns = [
    {
      key: 'product_name',
      dataIndex: 'product_name',
      title: translation('manageBoards.productName'),
    },
    // {
    //   key: 'description',
    //   dataIndex: 'description',
    //   title: translation('manageBoards.description'),
    // },
    // {
    //   key: 'product_model',
    //   dataIndex: 'product_model',
    //   title: translation('manageBoards.productModel'),
    // },
    {
      key: 'golden_product',
      title: translation('common.goldenProduct'),
      render: (record) => (
        <Button
          type='text'
          onClick={() => {
            // TODO: switch to url with param query
            // props.history.push('/aoi/view-product');
            // props.history.push(`/aoi/edit-product/${record.product_id}?editStep=1`);

            if (_.isEmpty(_.get(record, 'inspectables', []))) {
              props.history.push(`/aoi/edit-product/${record.product_id}?editStep=0`);
            } else {
              props.history.push(`/aoi/edit-product/${record.product_id}?editStep=1&detectionStep=0`);
            }
          }}
        >
          <span className='font-source text-[12px] font-semibold'>
            {translation('common.view')}
          </span>
        </Button>
      ),
    },
    // {
    //   key: 'ai_model',
    //   dataIndex: 'ai_model',
    //   title: translation('manageBoards.aiModel'),
    // },
    {
      key: 'image_map',
      title: translation('manageBoards.imagingMap'),
      render: () => (
        <div className='flex py-1 px-2 flex-col items-center flex-1 rounded-[0.833px] bg-[#131313]'>
          <MultiViewGridSelection
            layout={_.get(systemMetadata, 'inspection_view_layout')}
            selectedViewId={-1}
            width={74}
            rowHeight={42}
            height={42}
          />
        </div>
      ),
    },
    {
      key: 'actions',
      title: translation('manageBoards.actions'),
      render: (record) => (
        <div className='flex gap-2 items-center flex-1'>
          {/* <div
            className='flex w-6 h-6 justify-center items-center cursor-pointer hover:bg-AOI-blue-hover transition-all duration-300 rounded-[2px]'
            onClick={() => {

            }}
          >
            <img src='/img/icn/icn_download_blue.svg' className='w-3 h-3' alt='copy' />
          </div> */}
          {/* <div
            className='flex w-6 h-6 justify-center items-center cursor-pointer hover:bg-[#eb575780] transition-all duration-300 rounded-[2px]'
            onClick={() => {
              setSelectedDeleteProductId(record.product_id);
              setSelectedDeleteProductName(record.product_name);
              setIsDeleteProductModalOpened(true);
            }}
          >
            <img src='/img/icn/icn_delete_red.svg' className='w-[10.6px] h-[12px] shrink-0' alt='delete' />
          </div> */}
          <RedPrimaryButtonConfigProvider>
            <Button
              onClick={() => {
                setSelectedDeleteProductId(record.product_id);
                setSelectedDeleteProductName(record.product_name);
                setIsDeleteProductModalOpened(true);
              }}
            >
              <span className='font-source text-[12px] font-normal'>
                {translation('common.delete')}
              </span>
            </Button>
          </RedPrimaryButtonConfigProvider>
          {/* <div
            className='flex w-6 h-6 justify-center items-center cursor-pointer hover:bg-[#c4c4c480] transition-all duration-300 rounded-[2px]'
            onClick={() => {
              props.history.push(`/aoi/component-review/${record.product_id}`);
            }}
          >
            <FileSearchOutlined
              width={10}
              height={12}
            />
          </div> */}
          <Button
            onClick={() => {
              props.history.push(`/aoi/component-review/${record.product_id}`);
            }}
          >
            <span className='font-source text-[12px] font-normal'>
              {translation('manageBoards.viewTrainingSet')}
            </span>
          </Button>
        </div>
      ),
    }
  ];

  const handleUploadGoldenProduct = (file) => {
    if (_.isEmpty(file)) return false;
    // check if file extension is .zip
    const fileExtension = _.toLower(_.last(_.split(file.name, '.')));
    if (fileExtension !== 'zip') {
      aoiAlert(translation('notification.error.onlyZipFileAcceptedForGoldenProductDefinitionUpload'), ALERT_TYPES.COMMON_ERROR);
      return;
    }

    const upload = async (fileObj, refetchAllProd) => {
      const formData = new FormData();
      formData.append('file', fileObj);
      try {
        const res = await fetch(`${serverEndpoint}/file?extension=zip`,  {
          method: 'PUT',
          body: formData,
        });

        const data = await res.json();

        if (_.isEmpty(data, 'data_uri')) {
          handleRequestFailed('uploadGoldenProductDefinition', 'No data_uri returned from server');
          return;
        }

        await fetch(`${serverEndpoint}/importDefinitions`, {
          method: 'PUT',
          body: JSON.stringify({ data_uri: data.data_uri }),
        });

        await refetchAllProd();
      } catch (error) {
        handleRequestFailed('uploadGoldenProductDefinition', error);
        console.error('uploadGoldenProductDefinition', error);
      }
    }

    upload(file, refetchAllProd);

    return false;
  };

  useEffect(() => {
    if (_.isEmpty(boardSearchQuery)) {
      setFilteredProducts(allProducts);
    } else {
      setFilteredProducts(_.filter(allProducts, (product) => 
        product.is_golden && (
          product.product_name.toLowerCase().includes(boardSearchQuery.toLowerCase()) ||
          product.description.toLowerCase().includes(boardSearchQuery.toLowerCase())
        )
      ));
    }
  }, [allProducts, boardSearchQuery]);

  useEffect(() => {
    if (tableContainerRef.current) {
      const tableTBodyHeight = window.innerHeight - 387;
      // const tableTBodyHeight = window.innerHeight - 217 - 32;
      const pageSize = Math.floor(tableTBodyHeight / 93);
      setPageSize(Math.max(pageSize, 1));
    }

    handleResizeRef.current = () => {
      if (tableContainerRef.current) {
        const tableTBodyHeight = window.innerHeight - 387;
        // const tableTBodyHeight = window.innerHeight - 217 - 32;
        const pageSize = Math.floor(tableTBodyHeight / 93);
        setPageSize(Math.max(pageSize, 1));
      }
    };

    window.addEventListener('resize', handleResizeRef.current);

    return () => {
      if (handleResizeRef.current) window.removeEventListener('resize', handleResizeRef.current);
    }
  }, []);

  return (
    <MainMenuLayout>
      <NewBoardModal
        isOpened={isCreateBoardModalOpened}
        setIsOpened={setIsCreateBoardModalOpened}
        handleRedirect={(url) => props.history.push(url)}
      />
      <DeleteProductConfirmation
        isOpened={isDeleteProductModalOpened}
        setIsOpened={setIsDeleteProductModalOpened}
        selectedProductId={selectedDeleteProductId}
        selectedProductName={selectedDeleteProductName}
      />
      <ExportGoldenProductDefinition
        isOpened={isExportGoldenProductDefinitionModalOpened}
        setIsOpened={setIsExportGoldenProductDefinitionModalOpened}
        allProducts={allProducts}
      />
      <ManageBoardsLayout
        handleBack={() => window.location.href = '/aoi/home'}
      >
        <div className='flex px-4 items-center gap-4 self-stretch'>
          <div className='flex gap-2.5 flex-col items-center justify-center'>
            <span className='font-source text-[12px] font-nromal'>
              {`${filteredProducts?.length} ${translation('manageBoards.boardsCount')}`}
            </span>
          </div>
          <DarkInput
            style={{ width: '360px' }}
            prefix={<SearchOutlined />}
            value={boardSearchQuery}
            onChange={(e) => setBoardSearchQuery(e.target.value)}
          />
          <Button onClick={() => setIsCreateBoardModalOpened(true)}>
            <div className='flex items-center gap-2'>
              <PlusOutlined />
              <span className='font-source text-[12px] font-semibold'>
                {translation('manageBoards.createBoard')}
              </span>
            </div>
          </Button>
          <Button onClick={() => setIsExportGoldenProductDefinitionModalOpened(true)}>
            <div className='flex items-center gap-2'>
              <img src='/img/icn/icn_download_blue.svg' className='w-3 h-3' alt='export' />
              <span className='font-source text-[12px] font-semibold'>
                {translation('manageBoards.exportGoldenProduct')}
              </span>
            </div>
          </Button>
          <Upload
            showUploadList={false}
            accept='.zip'
            beforeUpload={handleUploadGoldenProduct}
          >
            <Button>
              <div className='flex items-center gap-2'>
              <UploadOutlined />
                <span className='font-source text-[12px] font-semibold'>
                  {translation('manageBoards.importGoldenProduct')}
                </span>
              </div>
            </Button>
          </Upload>
        </div>
        <div
          className='flex flex-col flex-1 self-stretch items-start'
          ref={tableContainerRef}
        >
          <DarkTable
            loading={isLoading || isFetching}
            style={{ width: '100%' }}
            columns={columns}
            dataSource={filteredProducts}
            pagination={{
              pageSize: pageSize,
              hideOnSinglePage: true,
              showSizeChanger: false,
              showQuickJumper: true,
            }}
            rowHoverable={false}
          />
        </div>
      </ManageBoardsLayout>
    </MainMenuLayout>
  );
};

export default ManageProduct;