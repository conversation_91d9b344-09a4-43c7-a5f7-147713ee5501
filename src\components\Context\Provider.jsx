import React, { useState, createContext } from 'react';


export const CameraPreviewContext = createContext();

export const ContextProvider = (props) => {
  const [cameraPreviewImageData, setCameraPreviewImageData] = useState({}); // { cameraIndex: { image: blob, depthImage: blob } }
  const [cameraPreviewPointCloudData, setCameraPreviewPointCloudData] = useState({}); // { cameraIndex: blob }
  const [curInferenceFrameImageData, setCurInferenceFrameImageData] = useState({}); // { inspectionStep: { image: blob ... } }

  const cameraPreview = {
    cameraPreviewImageData,
    setCameraPreviewImageData,
    cameraPreviewPointCloudData,
    setCameraPreviewPointCloudData,
    curInferenceFrameImageData,
    setCurInferenceFrameImageData,
  };

  // const [goldenBoardInspectData, ]

  return (
    <CameraPreviewContext.Provider value={cameraPreview}>
      {props.children}
    </CameraPreviewContext.Provider>
  )
};