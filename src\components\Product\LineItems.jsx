import { But<PERSON>, Checkbox, InputNumber, Select, Switch, Tooltip } from 'antd';
import _ from 'lodash';
import React, { Fragment, useEffect, useRef, useState } from 'react';
import { useAddFeatureByProductIdAndStepMutation, useUpdateFeatureByProductIdAndStepMutation } from '../../services/feature';
import { useDispatch } from 'react-redux';
import { setContainerWindowLoadingLocked } from '../../actions/setting';
import { handleRequestFailed, sleep, translation } from '../../common/util';
import { ALERT_TYPES, aoiAlert } from '../../common/alert';
import { text } from '../../common/translation';
import { aiDeviationThreshold, paixianToolAgent, userDefinedThreshold, useUserThreshold } from '../../common/const';
import { useTranslation } from 'react-i18next';
import { useDetectLineMutation } from '../../services/product';


const LineItems = (props) => {
  const {
    lineItems,
    feature,
    systemMetadata,
    handleGetAllStepsFeatures,
    productId,
    step,
  } = props;

  // const lineItems = {
  //   "defect_detection": {
  //       "name": "defect_detection",
  //       "agent_name": "defect_detection",
  //       "enabled": true,
  //       "params": {
  //           "sensitivity": {
  //               "description": "Sensitivity of the defect detection algorithm",
  //               "param_int": {
  //                   "value": 5,
  //                   "max": 10,
  //                   "min": 0
  //               },
  //               "param_float": null,
  //               "param_enum": null,
  //               "param_bool": null
  //           },
  //           "enable_box_fitting": {
  //               "description": "Maximum rotation angle of the box in degrees",
  //               "param_int": null,
  //               "param_float": null,
  //               "param_enum": null,
  //               "param_bool": true
  //           }
  //       }
  //   },
  //   "height_diff": {
  //       "name": "height_diff",
  //       "agent_name": "height_diff",
  //       "enabled": false,
  //       "params": {
  //           "sensitivity": {
  //               "description": null,
  //               "param_int": {
  //                   "value": 5,
  //                   "max": 10,
  //                   "min": 0
  //               },
  //               "param_float": null,
  //               "param_enum": null,
  //               "param_bool": null
  //           }
  //       }
  //     }
  //   };

  const dispatch = useDispatch();

  const [updateFeature] = useUpdateFeatureByProductIdAndStepMutation();

  const handleEnableParam = async (agentName, enabled, feature, systemMetadata, handleGetAllStepsFeatures) => {
    if (_.isEmpty(feature)) return;

    let curFeature = _.cloneDeep(feature);
    curFeature = _.set(curFeature, `line_item_params.${agentName}.enabled`, enabled);

    dispatch(setContainerWindowLoadingLocked(true));

    // console.log('curFeature', curFeature);

    const res = await updateFeature(curFeature);
    if (_.get(res, 'error')) {
      handleRequestFailed('updateFeature', _.get(res, 'error'));
      dispatch(setContainerWindowLoadingLocked(false));
      return;
    }

    await sleep(500);
    handleGetAllStepsFeatures(systemMetadata, Number(_.get(feature, 'product_id', 0)));

    dispatch(setContainerWindowLoadingLocked(false));
    aoiAlert(translation('notification.success.featureUpdated'), ALERT_TYPES.COMMON_SUCCESS);

    return;
  };

  return (
    <div className='flex flex-col gap-4 w-full h-[320px] overflow-y-auto'>
      {_.map(_.keys(lineItems), (agentName) =>
        <div className='flex gap-2 self-stretch w-full flex-col'>
          <div className='flex gap-2 items-center'>
            <Checkbox
              checked={_.get(lineItems, `${agentName}.enabled`, false)}
              onChange={(e) => {
                handleEnableParam(agentName, e.target.checked, feature, systemMetadata, handleGetAllStepsFeatures);
              }}
            />
            <span className='font-source text-[12px] font-normal whitespace-nowrap'>
              {translation(`productAnnotation.agentName.${agentName}`)}
            </span>
          </div>
          <div className='flex flex-col gap-1 pl-6'>
            {_.map(
              // _.filter(
              //   _.keys(_.get(lineItems, `${agentName}.params`, {})),
              //   paramName => {
              //     if (paramName === userDefinedThreshold && !_.get(lineItems, `${agentName}.params.${useUserThreshold}.param_bool`, false)) return false;
              //     if (paramName === aiDeviationThreshold && _.get(lineItems, `${agentName}.params.${useUserThreshold}.param_bool`, false)) return false;
              //     return true;
              //   }
              // )
              _.keys(_.get(lineItems, `${agentName}.params`, {}))
              , (paramName) =>
              <SingleParam
                systemMetadata={systemMetadata}
                paramName={paramName}
                data={_.get(lineItems, `${agentName}.params.${paramName}`, {})}
                agentName={agentName}
                feature={feature}
                lineItems={lineItems}
                handleGetAllStepsFeatures={handleGetAllStepsFeatures}
              />
            )}
          </div>
          {agentName === paixianToolAgent && _.get(lineItems, `${agentName}.enabled`, false) &&
            <LineDetectionConfigDisplay
              feature={feature}
              productId={productId}
              step={step}
            />
          }
        </div>
      )}
    </div>
  );
};

const SingleParam = (props) => {
  const {
    paramName,
    data,
    agentName,
    feature,
    lineItems,
    systemMetadata,
    handleGetAllStepsFeatures,
  } = props;

  const dispatch = useDispatch();

  const [updateFeature] = useUpdateFeatureByProductIdAndStepMutation();

  const handleUpdateParam = async (feature, paramName, agentName, paramType, newValue, systemMetadata, handleGetAllStepsFeatures) => {
    let curFeature = _.cloneDeep(feature);
    let curLineItems = _.cloneDeep(lineItems);
    curLineItems = _.set(curLineItems, `${agentName}.params.${paramName}.${paramType}`, newValue);
    curFeature = _.set(curFeature, 'line_item_params', curLineItems);

    dispatch(setContainerWindowLoadingLocked(true));

    const res = await updateFeature(curFeature);

    if (_.get(res, 'error')) {
      handleRequestFailed('updateFeature', _.get(res, 'error'));
      dispatch(setContainerWindowLoadingLocked(false));
      return;
    }

    await sleep(500);
    handleGetAllStepsFeatures(systemMetadata, Number(_.get(feature, 'product_id', 0)));

    aoiAlert(translation('notification.success.featureUpdated'), ALERT_TYPES.COMMON_SUCCESS);
    dispatch(setContainerWindowLoadingLocked(false));
  };

  return (
    <div className='flex items-center gap-2 justify-between'>
      <div className='flex items-center gap-1 self-stretch'>
        <span className='font-source text-[12px] font-normal whitespace-nowrap'>
          {translation(`productAnnotation.paramName.${agentName}.${paramName}.title`)}
        </span>
        {!_.isEmpty(_.get(text, `productAnnotation.paramName.${agentName}.${paramName}.desc`, '')) &&
          <Tooltip title={
            <span className='font-source text-[12px] font-normal'>
              {translation(`productAnnotation.paramName.${agentName}.${paramName}.desc`)}
            </span>
          }>
            <img src='/img/icn/icn_info_white.svg' className='w-[12px] h-[12px]' />
          </Tooltip>
        }
      </div>
      <div className='flex flex-1 justify-end self-stretch'>
        {!_.isNull(_.get(data, 'param_int', null)) &&
          <InputNumber
            style={{ width: '30px' }}
            controls={false}
            size='small'
            // min={_.get(data, 'param_int.min', 0)}
            // max={_.get(data, 'param_int.max', 0)}
            min={_.get(systemMetadata, `default_line_items.${agentName}.params.${paramName}.param_int.min`, 0)}
            max={_.get(systemMetadata, `default_line_items.${agentName}.params.${paramName}.param_int.max`, 0)}
            value={_.get(data, 'param_int.value', 0)}
            precision={0}
            onBlur={(e) => {
              // max and min works in onChange, not onBlur
              // manually validate here
              const value = Number(e.target.value);
              if (!_.isInteger(value)) {
                aoiAlert(translation('notification.error.pleaseEnterAValidInteger'), ALERT_TYPES.COMMON_ERROR);
                return
              }
              if (value < _.get(systemMetadata, `default_line_items.${agentName}.params.${paramName}.param_int.min`, 0) ||
               value > _.get(systemMetadata, `default_line_items.${agentName}.params.${paramName}.param_int.max`, 0)) {
                aoiAlert(translation('notification.error.pleaseEnterANumberWithinTheRange'), ALERT_TYPES.COMMON_ERROR);
                return;
              }
              handleUpdateParam(
                feature,
                paramName,
                agentName,
                'param_int',
                {
                  ..._.get(data, 'param_int', {}),
                  value: Number(e.target.value),
                },
                systemMetadata,
                handleGetAllStepsFeatures
              );
            }}
          />
        }
        {!_.isNull(_.get(data, 'param_float', null)) &&
          <InputNumber
            style={{ width: '60px' }}
            controls={false}
            size='small'
            // min={_.get(data, 'param_float.min', 0)}
            // max={_.get(data, 'param_float.max', 0)}
            min={_.get(systemMetadata, `default_line_items.${agentName}.params.${paramName}.param_float.min`, 0)}
            max={_.get(systemMetadata, `default_line_items.${agentName}.params.${paramName}.param_float.max`, 0)}
            value={_.get(data, 'param_float.value', 0)}
            precision={6}
            onBlur={(e) => {
              // max and min works in onChange, not onBlur
              // manually validate here
              const value = Number(e.target.value);
              if (!_.isNumber(value)) {
                aoiAlert(translation('notification.error.pleaseEnterAValidNumber'), ALERT_TYPES.COMMON_ERROR);
                return
              }
              if (value < _.get(systemMetadata, `default_line_items.${agentName}.params.${paramName}.param_float.min`, 0) ||
               value > _.get(systemMetadata, `default_line_items.${agentName}.params.${paramName}.param_float.max`, 0)) {
                aoiAlert(translation('notification.error.pleaseEnterANumberWithinTheRange'), ALERT_TYPES.COMMON_ERROR);
                return;
              }
              handleUpdateParam(
                feature,
                paramName,
                agentName,
                'param_float',
                {
                  ..._.get(data, 'param_float', {}),
                  value: Number(e.target.value),
                },
                systemMetadata,
                handleGetAllStepsFeatures
              );
            }}
          />
        }
        {!_.isNull(_.get(data, 'param_bool', null)) &&
          <Switch
            size='small'
            value={_.get(data, 'param_bool', false)}
            onChange={(v) => {
              handleUpdateParam(
                feature,
                paramName,
                agentName,
                'param_bool',
                v,
                systemMetadata,
                handleGetAllStepsFeatures
              );
            }}
          />
        }
        {!_.isNull(_.get(data, 'param_enum', null)) &&
          <Select
            size='small'
            value={_.get(data, 'param_enum.value', '')}
            options={_.map(_.get(data, 'param_enum.options', []), o => ({
              label: <span className='font-source text-[12px] font-normal'>{translation(`productAnnotation.paramName.${agentName}.${paramName}.options.${o}`)}</span>,
              value: o,
            }))}
            onChange={(v) => {
              handleUpdateParam(
                feature,
                paramName,
                agentName,
                'param_enum',
                {
                ..._.get(data, 'param_enum', {}),
                  value: v,
                },
                systemMetadata,
                handleGetAllStepsFeatures
              );
            }}
          />
        }
      </div>
    </div>
  );
};

const LineDetectionConfigDisplay = (props) => {
  const {
    feature,
    productId,
    step,
  } = props;

  const displayCanvasRef = useRef();

  const [curResultLineInfo, setCurResultLineInfo] = useState(null);

  const [detectLine] = useDetectLineMutation();

  const handleGetResultLineInfo = async (
    feature,
    productId,
    step,
    showSuccessAlart=true,
  ) => {
    const res = await detectLine({
      product_id: Number(productId),
      step: Number(step),
      variant: _.get(feature, 'variant', ''),
      feature_id: _.get(feature, 'feature_id', 0),
    });

    if (_.get(res, 'error')) {
      handleRequestFailed('detectLine', _.get(res, 'error'));
      return;
    }

    setCurResultLineInfo(_.get(res, 'data', null)); // { p1: {x, y}, p2: {x, y}, angle: 0 }

    if (showSuccessAlart) {
      aoiAlert(translation('notification.success.lineDetection'), ALERT_TYPES.COMMON_SUCCESS);
    }

    return;
  };

  useEffect(() => {
    handleGetResultLineInfo(feature, productId, step, false);
  }, []);

  return (
    <div className='flex flex-col gap-2'>
      <Button
        onClick={() => {
          handleGetResultLineInfo(feature, productId, step);
        }}
      >
        <span className='font-source text-[12px] font-normal'>
          {translation('productAnnotation.paixianLineDetection')}
        </span>
      </Button>
      <div
        className='flex items-center justify-center w-full h-[230px]'
      >

      </div>
    </div>
  );
};

export default LineItems;