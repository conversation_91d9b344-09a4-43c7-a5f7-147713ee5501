import _ from 'lodash';
import TwoDBaseViwer from './TwoDBaseViewer';
import { fabric } from 'fabric-with-erasing';
import React from 'react';


export default class InfieldCalibrationViewer extends TwoDBaseViwer {
  constructor(props) {
    super(props, props.displayCanvasRef);
    this.displayCanvasRef = props.displayCanvasRef;
    this.scene = null; // 2d image
    this.fabricCanvas = null;
    this.sceneOriginalWidth = 0;
    this.sceneOriginalHeight = 0;
    this.containerRef = React.createRef();
  };

  componentDidMount() {
    if (!this.displayCanvasRef.current) return;
    // init fabric canvas
    const fabricCanvas = new fabric.Canvas(
      this.displayCanvasRef.current,
      {
        // Disable uniform scaling
        uniformScaling: false,
      }
    );
    fabricCanvas.skipOffscreen = true;

    // Enable Panning
    let isPanning = false;
    fabricCanvas.on('mouse:down', (opt) => {
      if (opt.target) {
        isPanning = false;
        return;
      }
      
      isPanning = true;
      fabricCanvas.selection = false;
      this.scene.moveTo(0);
    });

    fabricCanvas.on('mouse:move', (opt) => {
      if (isPanning && opt && opt.e) {
        fabricCanvas.setCursor('grab');
        const delta = new fabric.Point(opt.e.movementX, opt.e.movementY);
        fabricCanvas.relativePan(delta);
      }
    });

    fabricCanvas.on('mouse:up', () => {
      isPanning = false;
      fabricCanvas.setCursor('default');
      this.fabricCanvas.renderAll();
    });

    // Enable Zooming
    fabricCanvas.on('mouse:wheel', (opt) => {
      const delta = opt.e.deltaY;
      let zoom = fabricCanvas.getZoom();
      zoom *= 0.999 ** delta;
      if (zoom > 20) zoom = 20;
      if (zoom < 0.01) zoom = 0.01;
      fabricCanvas.zoomToPoint({ x: opt.e.offsetX, y: opt.e.offsetY }, zoom);
      opt.e.preventDefault();
      opt.e.stopPropagation();
    });
    
    this.fabricCanvas = fabricCanvas;
  };

  loadScene = (dataUrl) => {
    if (!this.fabricCanvas || !this.displayCanvasRef.current) return;

    // dispose previous scene
    if (this.scene) {
      this.fabricCanvas.remove(this.scene);
      this.scene = null;
    }

    fabric.Image.fromURL(dataUrl, (img) => {
    // fabric.Image.fromURL(dataURL, (img) => {
      img.set({
        selectable: false,
        evented: false,
      })
      this.sceneOriginalWidth = img.width;
      this.sceneOriginalHeight = img.height;
      const { fabricCanvas } = this;
      fabricCanvas.setWidth(this.containerRef.current.offsetWidth);
      fabricCanvas.setHeight(this.containerRef.current.offsetHeight);
      // img.scaleToHeight(canvasHeight);
      // img.scaleToWidth(canvasWidth);
      img.moveTo(0);
      fabricCanvas.add(img);
      this.scene = img;
      // zoom and pan to view the whole scene
      this.resetView();
    });
  };

  clearScene = () => {
    if (this.fabricCanvas) {
      this.fabricCanvas.clear();
    }
    this.scene = null;
  };

  resetView = () => {
    if (!this.fabricCanvas || !this.scene) return;
    const canvasWidth = this.fabricCanvas.getWidth();
    const canvasHeight = this.fabricCanvas.getHeight();
    const sceneWidth = this.scene.width * this.scene.scaleX;
    const sceneHeight = this.scene.height * this.scene.scaleY;
    const scaleX = canvasWidth / sceneWidth;
    const scaleY = canvasHeight / sceneHeight;
    const zoom = Math.min(scaleX, scaleY);

    const panX = (canvasWidth - sceneWidth * zoom) / 2;
    const panY = (canvasHeight - sceneHeight * zoom) / 2;

    this.fabricCanvas.viewportTransform = [zoom, 0, 0, zoom, panX, panY];
    this.fabricCanvas.renderAll();
  };

  render() {
    return (
      <div className='w-full h-full' ref={this.containerRef}>
        {super.render()}
      </div>
    );
  }
};