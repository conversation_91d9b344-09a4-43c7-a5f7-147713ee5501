import React, { useEffect, useRef, useState } from 'react';
import { translation } from '../../common/util';
import { Button, Select } from 'antd';
import _ from 'lodash';
import { serverEndpoint } from '../../common/const';
import { CloseCircleOutlined } from '@ant-design/icons';
import { useDispatch } from 'react-redux';
import { setIsGoldenComponentChecklistOpened } from '../../actions/setting';


const GoldenComponentChecklist = (props) => {
  const {
    // setIsGoldenComponentChecklistOpened,
    allSessionStepInfo,
    allStepsFeatures, // { 'stepNumber': [features] }
  } = props;

  const dispatch = useDispatch();

  const listContRef = useRef();

  const [items, setItems] = useState([]);
  const [listHeight, setListHeight] = useState(0);
  const [componentTypeOptions, setComponentTypeOptions] = useState([]);
  const [selectedComponentType, setSelectedComponentType] = useState(null);

  useEffect(() => {
    // set list height
    if (listContRef.current) {
      setListHeight(listContRef.current.clientHeight);
    }

    // set component type options
    const mergedFeatures = Object.values(allStepsFeatures).reduce((acc, cur) => {
      return acc.concat(cur);
    }, []);
    const newComponentTypes = _.uniq(mergedFeatures.map(f => {
      return f.feature_scope === 'global' ? f.feature_type : _.get(_.split(f.feature_type, '_'), '1')
    }));
    const tmp = _.map(newComponentTypes, t => ({ label: t, value: t }));
    setComponentTypeOptions(_.map(newComponentTypes, t => ({ label: t, value: t })));

    setSelectedComponentType(_.get(_.first(tmp), 'value'));
  }, [allSessionStepInfo, allStepsFeatures]);

  useEffect(() => {
    // set component type options
    const mergedFeatures = Object.values(allStepsFeatures).reduce((acc, cur) => {
      return acc.concat(cur);
    }, []);

    // set items
    const newItems = [];
    for (const f of mergedFeatures) {
      if ((f.feature_scope === 'global' && f.feature_type === selectedComponentType) || (f.feature_scope === 'product' && f.feature_type === `_${selectedComponentType}`)) {
        newItems.push({
          goldenImageUri: f.cropped_color_map_uri,
          ipcImageUri: _.get(_.find(allSessionStepInfo, s => s.step_number === f.step_number && s.feature_id === f.feature_id), 'cropped_color_map_uri', ''),
        });
      }
    }

    setItems(newItems);
  }, [
    allSessionStepInfo,
    selectedComponentType,
    allStepsFeatures,
  ]);

  return (
    <div
      className='flex w-[368px] flex-col items-start gap-2 self-stretch rounded-[2px] bg-[#ffffff0d]'
      style={{ height: 'calc(100vh - 198px)' }}
    >
      <div className='flex flex-1 flex-col self-stretch'>
        <div className='flex p-2 flex-col self-stretch gap-2'>
          <div className='flex items-center justify-between self-stretch'>
            <span className='font-source text-[14px] font-semibold'>
              {translation('viewInspection.checklist')}
            </span>
            <Button
              size='small'
              type='text'
              onClick={() => {
                dispatch(setIsGoldenComponentChecklistOpened(false));
              }}
            >
              <CloseCircleOutlined style={{color: '#EB5757'}} />
            </Button>
          </div>
          <div className='flex justify-between items-center self-stretch'>
            <div className='flex items-center gap-1 self-stretch'>
              <span className='font-source text-[12px] font-normal'>
                {translation('viewInspection.totalComponent')}
              </span>
              <span className='font-source text-[12px] font-semibold'>
                {items.length}
              </span>
            </div>
            <Select
              size='small'
              popupMatchSelectWidth={false}
              options={componentTypeOptions}
              value={selectedComponentType}
              onChange={(value) => {
                setSelectedComponentType(value);
              }}
              style={{ width: '120px' }}
            />
          </div>
        </div>
        <div
          className='flex flex-1 self-stretch'
          ref={listContRef}
        >
          <div style={{
            height: `${listHeight}px`,
            width: '100%',
            overflowY: 'auto',
            flexWrap: 'wrap',
            gap: '4px',
            padding: '4px',
            display: 'flex',
          }}>
            {items.map((item, index) => {
              return (
                <div
                  key={index}
                  className='flex flex-col items-center justify-center rounded-[2px] bg-[#ffffff0d]'
                  style={{
                    width: '161px',
                    height: '161px',
                  }}
                >
                  {/* <img
                    src={item.goldenImageUri}
                    alt='golden'
                    className='w-full h-full object-contain'
                  /> */}
                  <CustomImage uri={item.goldenImageUri} />
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
};

const CustomImage = (props) => {
  const {
    uri
  } = props;

  const [src, setSrc] = useState('');

  useEffect(() => {
    if (!uri) return;
    const fetchData = async (uri) => {
      let dataRes;
      try {
        dataRes = await fetch(`${serverEndpoint}/data?data_uri=${uri}`);
      } catch (error) {
        console.error('fetch data error', error);
        return;
      }
      const blob = await dataRes.blob();
      const reader = new FileReader();
      reader.readAsDataURL(blob);
      reader.onload = (event) => {
        setSrc(event.target.result);
      };
    };
    fetchData(uri);
  }, [uri]);

  return (
    <img
      src={src}
      alt='golden'
      className='w-full h-full object-contain'
    />
  );
};

export default GoldenComponentChecklist;