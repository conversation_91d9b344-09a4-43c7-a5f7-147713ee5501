import { toast } from 'react-toastify';

const config = {
  commonError: {
    position: 'bottom-right',
    autoClose: 3000,
    hideProgressBar: true,
    closeOnClick: true,
    pauseOnHover: true,
    draggable: true,
    progress: undefined,
    theme: "colored",
  },
  commonSuccess: {
    position: 'bottom-right',
    autoClose: 7000,
    hideProgressBar: true,
    closeOnClick: true,
    pauseOnHover: true,
    draggable: true,
    progress: undefined,
    theme: "colored",
  }
}

export const ALERT_TYPES = {
  COMMON_ERROR: 'COMMON_ERROR',
  COMMON_SUCCESS: 'COMMON_SUCCESS',
  COMMON_WARNING: 'COMMON_WARNING',
  COMMON_INFO: 'COMMON_INFO',
}

export const aoiAlert = (translation, types) => {
  switch (types) {
    case ALERT_TYPES.COMMON_ERROR:
      toast.error(translation, config.commonError);
      break;
    case ALERT_TYPES.COMMON_SUCCESS:
      toast.success(translation, config.commonSuccess);
      break;
    case ALERT_TYPES.COMMON_WARNING:
      toast.warning(translation, config.commonSuccess);
      break;
    case ALERT_TYPES.COMMON_INFO:
      toast.info(translation, config.commonSuccess);
      break;
    default:
      break;
  }
};
