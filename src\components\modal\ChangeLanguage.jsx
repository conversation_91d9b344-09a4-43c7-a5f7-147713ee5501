import { Modal, Select } from 'antd';
import React from 'react';
import { translation } from '../../common/util';
import i18n from '../../i18n';


const ChangeLanguage = (props) => {
  const {
    isOpened,
    setIsOpened,
  } = props;

  return (
    <Modal
      open={isOpened}
      onCancel={() => setIsOpened(false)}
      title={<span className='font-source text-[16px] font-semibold'>
        {translation('common.settings')}
      </span>}
      footer={null}
    >
      <div className='flex flex-col items-start gap-8 self-stretch px-4 py-6'>
        <div className='flex items-center gap-2 self-stretch'>
          <span className='font-source text-[14px] font-normal flex-1'>
            {translation('common.language')}
          </span>
          <Select
            style={{ width: '120px' }}
            options={[
              { label: translation('common.english'), value: 'en' },
              { label: translation('common.chinese'), value: 'cn' },
            ]}
            value={i18n.language}
            onChange={(value) => {
              i18n.changeLanguage(value);
              setIsOpened(false);
            }}
          />
        </div>
      </div>
    </Modal>
  );
};

export default ChangeLanguage;