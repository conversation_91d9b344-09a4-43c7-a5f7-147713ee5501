import { But<PERSON>, Modal, Select } from 'antd';
import React, { useState } from 'react';
import { handleRequestFailed, translation } from '../../common/util';
import _ from 'lodash';
import { serverEndpoint } from '../../common/const';
import { ALERT_TYPES, aoiAlert } from '../../common/alert';
import { useLazyExportDefinitionsQuery } from '../../services/product';

const ExportGoldenProductDefinition = (props) => {
  const {
    isOpened,
    setIsOpened,
    allProducts,
  } = props;
  
  const [selectedProdIds, setSelectedProdIds] = useState([]);

  const [exportDefinitionsByProdIds] = useLazyExportDefinitionsQuery();

  const handleExport = async (selectedProdIds) => {
    let res;
    if (_.isEmpty(selectedProdIds)) {
      res = await exportDefinitionsByProdIds();
    } else {
      res = await exportDefinitionsByProdIds({ product_ids: _.join(selectedProdIds, ',') });
    }
    if (res.error) {
      handleRequestFailed('exportDefinitionsByProdIds', res.error);
      return;
    }
    if (!_.isEmpty(_.get(res, 'data.data_uri'))) {
      window.open(`${serverEndpoint}/file?file_uri=${_.get(res, 'data.data_uri')}`);
      aoiAlert(translation('notification.success.goldenProductZipDownloadStarted'), ALERT_TYPES.COMMON_INFO);
    }
    setIsOpened(false);
  };

  return (
    <Modal
      open={isOpened}
      onCancel={() => setIsOpened(false)}
      title={
        <span className='font-source text-[16px] font-semibold'>
          {translation('exportGoldenProductDefinition.title')}
        </span>
      }
      footer={null}
    >
      <div className='flex py-6 px-4 items-start gap-8 self-stretch flex-col'>
        <div className='flex items-start gap-2 self-stretch flex-col'>
          <span className='font-source text-[12px] font-normal'>
            {translation('exportGoldenProductDefinition.selectGoldenProduct')}
          </span>
          <Select
            style={{ width: '100%' }}
            options={_.map(allProducts, p => ({ value: Number(p.product_id), label: p.product_name }))}
            showSearch
            optionFilterProp='label'
            placeholder={translation('exportGoldenProductDefinition.searchGoldenProduct')}
            value={selectedProdIds}
            mode='tags'
            onChange={(value) => setSelectedProdIds(value)}
            allowClear
          />
        </div>
        <div className='flex items-start gap-2 self-stretch'>
          <Button style={{ width: '50%' }} onClick={() => handleExport(_.map(allProducts, p => Number(p.product_id)))}>
            <span className='font-source text-[12px] font-semibold'>
              {translation('common.exportAll')}
            </span>
          </Button>
          <Button style={{ width: '50%' }} onClick={() => {
            if (_.isEmpty(selectedProdIds)) {
              aoiAlert(translation('notification.error.selectAGoldenProduct'), ALERT_TYPES.COMMON_ERROR);
              return;
            }
            handleExport(selectedProdIds);
          }}>
            <span className='font-source text-[12px] font-semibold'>
              {translation('common.exportSelected')}
            </span>
          </Button>
        </div>
      </div>
    </Modal>
  )
};

export default ExportGoldenProductDefinition;