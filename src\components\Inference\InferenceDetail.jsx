import { Button, Checkbox, Collapse, ConfigProvider, Spin, Tabs, Tooltip } from 'antd';
import _ from 'lodash';
import React, { Fragment, useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import styled from 'styled-components';
import { pushMsgToInspectionErrorQueue, setContainerTransparentLockEnabled, setContainerWindowLoadingLocked, setCurRunningIpcSessionIds, setCurTrainingTaskStartTime, setIsComponentFilterVisible, setIsGoldenComponentChecklistOpened, setIsInferenceRunning, setIsTrainingRuning, setPrevResetIpcCountDate, setStayAtLeatestEnabled, setTodayGoodManualChangeOffset, setTodayTotalManualChangeOffset } from '../../actions/setting';
import { ALERT_TYPES, aoiAlert } from '../../common/alert';
import { defectDetection, retrainModelTaskPhaseType, retrainModelType, serverEndpoint, threeDLineItems } from '../../common/const';
import { AOIGreenButtonConfig, CustomCollapse, lightGray, primaryColor, RoundLabel } from '../../common/darkModeComponents';
import { backendTimestampToDisplayString, convertGrayScaleArrayBufferToGreenToRedImage, getColorByStr, getFeatureTypeDisplayText, getInspectionCountByInspectionViewLayout, getQueryParams, handleRequestFailed, mapRange, sleep, translation } from '../../common/util';
import { useGetAllFeaturesByProductIdAndStepQuery, useLazyGetAllFeaturesByProductIdAndStepQuery } from '../../services/feature';
import { useLazyGetFrameByProductIdAndStepQuery } from '../../services/frame';
import { useModelUpdateTriggerMutation } from '../../services/model';
import { useGetProductByIdQuery, useLazyGetCurProductComponentToggleQuery, useLazyGetInferenceStatusQuery, usePostInferenceActionMutation, useStopInferenceMutation, useResetSessionMutation } from '../../services/product';
import { useLazyGetAllInspectionsQuery, useLazyGetInspectionInfoQuery, useLazyGetNeighborInspectionsQuery, useLazyGetSessionInfoQuery, useLazyGetSessionStepInfoQuery, useReplaySessionMutation, useSubmitGoodFeedbackForAllMutation } from '../../services/session';
import { systemApi, useLazyGetErrorQueueQuery } from '../../services/system';
import { store } from '../../store';
import MainMenuLayout from '../layout/MainMenuLayout';
import ViewInspectionLayout from '../layout/ViewInspectionLayout';
import AdjustPassRate from '../modal/AdjustPassRate';
import RetrainConfirmation from '../modal/RetrainConfirmation';
import SubmitAllGoodFeedback from '../modal/SubmitAllGoodFeedback';
import InferenceAnnotation from './InferenceAnnotation';
import ObjectContainImage from './ObjectContainImage';
import CloseOutlined from '@ant-design/icons/CloseOutlined';
import LiveComponentFilter from './LiveComponentFilter';
import CustomTabs from '../../common/styledComponent/customTabs';
import HeightDiffComparison from '../Scene/HeightDiffComparison';
import ReferenceComparison from './ReferenceComparison';
import AllStepInferenceDisplay from '../Scene/AllStepInferenceDisplay';
import MultiViewGridSelection from '../common/MultiViewGridSelection';
import GoldenComponentChecklist from './GoldenComponentChecklist';
import FilterComponentCol from './FilterComponentCol';
import moment from 'moment';
import SwitchGoldenProduct from '../modal/SwitchGoldenProduct';


const InferenceDetail = (props) => {
  const dispatch = useDispatch();
  // const { ipcProductId, productId } = useParams();

  const { search } = props.location;
  const searchParams = getQueryParams(search);
  const { step } = searchParams; // step is null then it's a full product view
  // const { productId, ipcProductId } = props.match.params;

  const autoRefetchSession = useRef();
  const autoRefetchSessionInfo = useRef();
  const loopCheckRetrainStatus = useRef();
  const curSessionInitialized = useRef(false);
  const ipcProductIdRef = useRef(props.match.params.ipcProductId);
  const productIdRef = useRef(props.match.params.productId);
  const pollLatestIpcRef = useRef();
  const curLatestIpcProductId = useRef(null); // a flag to compare to the latest ipc product id req response to update ipc index
  const drawMaskViewerRef = useRef();
  const errorPollingRef = useRef();

  const [ipcProductId, setIpcProductId] = useState(props.match.params.ipcProductId);
  const [productId, setProductId] = useState(props.match.params.productId);

  // const { data: ipcProduct } = useGetProductByIdQuery(ipcProductId);
  const { data: goldenProduct } = useGetProductByIdQuery(productId);
  const { data: goldenProdFeatures } = useGetAllFeaturesByProductIdAndStepQuery({ product_id: productId, step: step || 0 });
  const [getSessionStepInfo] = useLazyGetSessionStepInfoQuery();
  const [getSessionInfo] = useLazyGetSessionInfoQuery();
  const [stopInference] = useStopInferenceMutation();
  const [getInferenceStatus] = useLazyGetInferenceStatusQuery();
  const [getInspections] = useLazyGetAllInspectionsQuery();
  const [getFrame] = useLazyGetFrameByProductIdAndStepQuery();
  const [retrainTrigger] = useModelUpdateTriggerMutation();
  const [submitGoodFeedbackForAll] = useSubmitGoodFeedbackForAllMutation();
  const [replaySession] = useReplaySessionMutation();
  const [inferenceTrigger] = usePostInferenceActionMutation();
  const [getNeighborInspections] = useLazyGetNeighborInspectionsQuery();
  const [getInspectionInfo] = useLazyGetInspectionInfoQuery();
  const [resetSession] = useResetSessionMutation();
  const [getCurProductComponentlist] = useLazyGetCurProductComponentToggleQuery();
  const [lazyGetAllFeatures] = useLazyGetAllFeaturesByProductIdAndStepQuery();
  const [lazyGetAllErrorQueue] = useLazyGetErrorQueueQuery();

  const [allStepsFeatures, setAllStepsFeatures] = useState({}); // { 'stepNumber': [features] }
  const [allSessionStepInfo, setAllSessionStepInfo] = useState({});
  const [curSessionStepInfo , setCurSessionStepInfo] = useState([]); // session step info
  const [curSessionInfo, setCurSessionInfo] = useState(null);
  const [curInspectionInfo, setCurInspectionInfo] = useState(null);
  const [isCurSessionFinished, setIsCurSessionFinished] = useState(false);
  const [prevInspcId, setPrevInspcId] = useState(null);
  const [nextInspcId, setNextInspcId] = useState(null);
  const [prevGoldenProdId, setPrevGoldenProdId] = useState(null);
  const [nextGoldenProdId, setNextGoldenProdId] = useState(null);
  // const [curSession, setCurSession] = useState(null);
  const [frames, setFrames] = useState({});
  const [selectedFeatureId, setSelectedFeatureId] = useState(null);
  const [selectedLineItem, setSelectedLineItem] = useState(null);
  const [isRetrainComfirmationModalOpened, setIsRetrainComfirmationModalOpened] = useState(false);
  const [isDrawMaskEnabled, setIsDrawMaskEnabled] = useState(false);
  const [curDisplayOptionsBrightness, setCurDisplayOptionsBrightness] = useState(50);
  const [curDisplayOptionsContrast, setCurDisplayOptionsContrast] = useState(50);
  const [curDisplayOptionsSaturation, setCurDisplayOptionsSaturation] = useState(50);
  const [isSharpnessEnabled, setIsSharpnessEnabled] = useState(false);
  const [isMarkAllFeedbackAsGoodModalOpened, setIsMarkAllFeedbackAsGoodModalOpened] = useState(false);
  const [expandedStep, setExpandedStep] = useState(0);
  const [isAdjustPassRateModalOpened, setIsAdjustPassRateModalOpened] = useState(false);
  const [curProductPassRate, setCurProductPassRate] = useState(0);
  const [isCurProductPassRateApproved, setIsCurProductPassRateApproved] = useState(true);
  const [isCurProdcutNGAmountExceeded, setIsCurProdcutNGAmountExceeded] = useState(false);
  const [curPassRateDelta, setCurPassRateDelta] = useState(0);
  const [curIpcIndex, setCurIpcIndex] = useState(0);
  const [curSessionTotalIpc, setCurSessionTotalIpc] = useState(0);
  const [isLiveComponentFilterOpened, setIsLiveComponentFilterOpened] = useState(false);
  const [currentFilterMap, setCurrentFilterMap] = useState({});
  const [displayedTodayIpcTotalCount, setDisplayedTodayIpcTotalCount] = useState(0);
  const [displayedTodayIpcGoodCount, setDisplayedTodayIpcGoodCount] = useState(0);
  // const [isGoldenComponentChecklistOpened, setIsGoldenComponentChecklistOpened] = useState(true);
  const [isSwitchGoldenProductModalOpened, setIsSwitchGoldenProductModalOpened] = useState(false);

  const { data: systemMetadata } = useSelector((state) => systemApi.endpoints.getSystemMetadata.select()(state));
  const isInferenceRunning = useSelector((state) => state.setting.isInferenceRunning);
  const productPassRates = useSelector((state) => state.setting.productPassRates);
  const viewInspectionViewMode = useSelector((state) => state.setting.viewInspectionViewMode);
  // const isStayAtLeatestEnabled = useSelector((state) => state.setting.isStayAtLeatestEnabled);
  const allowedNGAmount = useSelector((state) => state.setting.allowedNGAmount);
  const isContainerWindowLoadingLocked = useSelector((state) => state.setting.isContainerWindowLoadingLocked);
  const curRunningIpcSessionIds = useSelector((state) => state.setting.curRunningIpcSessionIds);
  const isGoldenComponentChecklistOpened = useSelector((state) => state.setting.isGoldenComponentChecklistOpened);
  const isComponentFilterVisible = useSelector((state) => state.setting.isComponentFilterVisible);
  const dailyIpcCountResetTime = useSelector((state) => state.setting.dailyIpcCountResetTime);
  const todayTotalManualChangeOffset = useSelector((state) => state.setting.todayTotalManualChangeOffset);
  const todayGoodManualChangeOffset = useSelector((state) => state.setting.todayGoodManualChangeOffset);
  const prevResetIpcCountDate = useSelector((state) => state.setting.prevResetIpcCountDate);
  const isViewLiveFullScreenEnabled = useSelector((state) => state.setting.isViewLiveFullScreenEnabled);

  if (_.isEmpty(curRunningIpcSessionIds)) window.location.href = '/aoi/home';

  const handleGetAllStepsFeatures = async (systemMetadata, productId) => {
    if (_.isEmpty(systemMetadata)) return;
    const steps = _.sum(_.get(systemMetadata, 'inspection_view_layout'));
    const curResult = {};
    for (let i = 0; i < steps; i++) {
      const res = await lazyGetAllFeatures({
        product_id: productId,
        step: i,
      });
      curResult[`${i}`] = _.get(res, 'data', []);
    }
    setAllStepsFeatures(curResult);
  };

  const getAllSessionStepInfo = async () => {
    const newAllSessionStepInfo = {};
    for (let i = 0; i < getInspectionCountByInspectionViewLayout(_.get(systemMetadata, 'inspection_view_layout')); i++) {
      const res = await getSessionStepInfo({ session_id: Number(ipcProductIdRef.current), step: i });
      if (res.error) {
        handleRequestFailed('getSessionStepInfo', res.error);
        return;
      }
      newAllSessionStepInfo[`${i}`] = _.get(res, 'data');
    }
    // console.log('newAllSessionStepInfo', newAllSessionStepInfo);
    setAllSessionStepInfo(newAllSessionStepInfo);
  };

  const handleGetAllFramesAfterInferenceFinish = async () => {
    if (curSessionInitialized.current) return;
    const tmpFrames = {};
    dispatch(setContainerTransparentLockEnabled(true));
    for (let i = 0; i < getInspectionCountByInspectionViewLayout(_.get(systemMetadata, 'inspection_view_layout')); i++) {
      const res = await getFrame({ product_id: Number(ipcProductIdRef.current), step: i });
      if (_.get(res, 'error')) {
        handleRequestFailed('getFrame', _.get(res, 'error'));
        return;
      }
      if (_.isEmpty(_.get(res, 'data.image.data_uri'))) {
        aoiAlert(translation('notification.error.frameImageUrlEmpty'), ALERT_TYPES.COMMON_ERROR);
        dispatch(setContainerTransparentLockEnabled(false));
        return;
      }
      tmpFrames[i] = _.get(res, 'data.image.data_uri');
    }
    dispatch(setContainerTransparentLockEnabled(false));
    setFrames(tmpFrames);
    curSessionInitialized.current = true;
  };

  const checkIfReadyForRetrain = (modelStatus) => {
    if (_.isEmpty(modelStatus)) return true;
    return _.isEmpty(_.filter(modelStatus, (taskStatus) => !_.includes([
      retrainModelTaskPhaseType.failure,
      retrainModelTaskPhaseType.complete,
      retrainModelTaskPhaseType.invalid
    ], taskStatus.phase)));
  };

  const handleRetrainModel = async (startTrainingAfterSession, pid) => {
    // if (!startTrainingAfterSession) {
    //   const stopInferenceRes = await stopInference();
    //   if (_.get(stopInferenceRes, 'error')) {
    //     handleRequestFailed('stopInference', _.get(res, 'error'));
    //     return;
    //   }
    // }

    // just in case fetch the retrain status
    let modelStatusRes;
    try {
      modelStatusRes = await fetch(`${serverEndpoint}/getModelUpdates`);
    } catch (error) {
      handleRequestFailed('getModelUpdates', error);
      return;
    }
    const modelStatus = await modelStatusRes.json();

    // if (!checkIfReadyForRetrain(modelStatus)) {
    //   aoiAlert(translation('notification.error.serverAlreadyHasARunningRetrainTask'), ALERT_TYPES.COMMON_ERROR);
    //   return;
    // }

    // lock screen
    // dispatch(setContainerWindowLoadingLocked(true));

    const payload = {
      model_types: [retrainModelType.defectModel, retrainModelType.heightModel],
    };
    if (_.isInteger(pid)) payload.golden_product_id = pid;

    const res = await retrainTrigger(payload);
    if (_.get(res, 'error')) {
      handleRequestFailed('retrainModel', _.get(res, 'error'));
      dispatch(setContainerWindowLoadingLocked(false));
      return;
    }

    if (!startTrainingAfterSession) {
      // dispatch(setIsInferenceRunning(false));
      // dispatch(setCurRunningIpcSessionIds([]));
      dispatch(setIsTrainingRuning(true));
      dispatch(setCurTrainingTaskStartTime(new Date().getTime()));
      // props.history.push('/aoi/model-training-in-progress');
      return;
    }

    // // retrain trigger success start get retrain status loop
    // loopCheckRetrainStatus.current = setInterval(async () => {
    //   let modelStatusRes;
    //   try {
    //     modelStatusRes = await fetch(`${serverEndpoint}/getModelUpdates`);
    //   } catch (error) {
    //     handleRequestFailed('getModelUpdates', error);
    //     clearInterval(loopCheckRetrainStatus.current);
    //     loopCheckRetrainStatus.current = null;
    //     dispatch(setContainerWindowLoadingLocked(false));
    //     return;
    //   }
    //   const modelStatus = await modelStatusRes.json();
    //   if (checkIfReadyForRetrain(modelStatus)) {
    //     clearInterval(loopCheckRetrainStatus.current);
    //     loopCheckRetrainStatus.current = null;
    //     dispatch(setContainerWindowLoadingLocked(false));
    //     // search for the task with greatest schedule_id
    //     const latestTask = _.maxBy(modelStatus, (task) => task.schedule_id);
    //     if (_.includes([retrainModelTaskPhaseType.failure, retrainModelTaskPhaseType.invalid], _.get(latestTask, 'phase'))) {
    //       aoiAlert(translation('notification.error.retrainFailed'), ALERT_TYPES.COMMON_ERROR);
    //       return;
    //     }
    //     aoiAlert(translation('notification.success.retrainFinished'), ALERT_TYPES.COMMON_SUCCESS);
    //     return;
    //   }
    // }, 1000);
  };

  const fetchFeatures = async () => {
    if (!_.isInteger(Number(step))) return;
    const res = await getSessionStepInfo({ session_id: Number(ipcProductIdRef.current), step });
    if (_.get(res, 'isError')) {
      // handleRequestFailed('getSessionStepInfo', _.get(res, 'error'));
      autoRefetchSessionInfo.current = setInterval(async () => {
        const res = await getSessionStepInfo({ session_id: Number(ipcProductIdRef.current), step });
        if (_.get(res, 'isError')) {
          // handleRequestFailed('getSessionStepInfo', _.get(res, 'error'));
          return;
        }
        setCurSessionStepInfo(_.get(res, 'data'));
        clearInterval(autoRefetchSessionInfo.current);
        autoRefetchSessionInfo.current = null;
      }, 500);
      return;
    }
    setCurSessionStepInfo(_.get(res, 'data'));
  };

  const handleMarkAllFeedbackAsGood = async (ipcProductId, selectedVariant) => {
    if (_.isEmpty(selectedVariant)) return;
    dispatch(setContainerWindowLoadingLocked(true));
    const res = await submitGoodFeedbackForAll({ product_id: Number(ipcProductId), variant: selectedVariant });
    if (_.get(res, 'error')) {
      handleRequestFailed('submitGoodFeedbackForAll', _.get(res, 'error'));
      dispatch(setContainerWindowLoadingLocked(false));
      return;
    }
    aoiAlert(translation('notification.success.submitGoodFeedbackForAllInspectionInThisProduct'), ALERT_TYPES.COMMON_SUCCESS);
    dispatch(setContainerWindowLoadingLocked(false));
    fetchFeatures();
    return;
  };

  const initErrorPolling = () => {
    if (errorPollingRef.current) {
      clearInterval(errorPollingRef.current);
      errorPollingRef.current = null;
    }
    errorPollingRef.current = setInterval(async () => {
      const res = await lazyGetAllErrorQueue();
      if (_.get(res, 'error')) return;
      if (_.isEmpty(_.get(res, 'data'))) return;
      const msg = _.get(res, 'data', []);

      dispatch(pushMsgToInspectionErrorQueue(msg));
      // for (const m of msg) {
      //   aoiAlert(_.get(m, 'message', ''), ALERT_TYPES.PRESIST_ERROR);
      // }
    }, 1000);
  };

  const initTodayIpcCount = async () => {
    const statusRes = await getInferenceStatus();
    if (statusRes.error) {
      console.error('Failed to get inference status');
      return;
    }
    const inspected = _.get(statusRes, 'data.inspected_count', 0);
    const defects = _.get(statusRes, 'data.defect_count', 0);
    setDisplayedTodayIpcTotalCount(inspected);
    setDisplayedTodayIpcGoodCount(inspected - defects);
  };

  const pollLatestInspection = async (curSessionId) => {
    // const latestIpcRes = await getInspections({ ipc_session_id: curSessionId, limit: 1 });
    const latestIpcRes = await getInspections({ limit: 1 });
    if (_.get(latestIpcRes, 'error')) {
      handleRequestFailed('getInspections', _.get(latestIpcRes, 'error'));
      if (pollLatestIpcRef.current) {
        clearInterval(pollLatestIpcRef.current);
        pollLatestIpcRef.current = null;
      }
      return;
    }

    const latestIpc = _.get(latestIpcRes, 'data.data.0');
    if (_.isEmpty(latestIpc)) return;
    if (_.get(store.getState(), 'setting.isStayAtLeatestEnabled') && Number(_.get(latestIpc, 'product_id', -1)) !== Number(ipcProductIdRef.current)) {
      setIsSharpnessEnabled(false);
      props.history.push(`/aoi/view-inference/${_.get(latestIpc, 'golden_product_id')}/${_.get(latestIpc, 'product_id')}?step=0`);
    } else if (curLatestIpcProductId.current !== _.get(latestIpc, 'product_id') && !_.get(store.getState(), 'setting.isStayAtLeatestEnabled')) {
      // update ipc index and current session info
      const sessionInfoRes = await getSessionInfo(curSessionId);
      if (_.get(sessionInfoRes, 'error')) return;
      setCurSessionTotalIpc(_.get(sessionInfoRes, 'data.total_product_count', 0));
      const neighborInspectionsRes = await getNeighborInspections({ product_id: ipcProductIdRef.current, ipc_session_id: curSessionId });
      if (_.get(neighborInspectionsRes, 'error')) return;
      setCurIpcIndex(_.get(neighborInspectionsRes, 'data.location', 0));
      curLatestIpcProductId.current = _.get(latestIpc, 'product_id');
    }
  };

  const initInspectionList = async (ipcProductId, curSessionId) => {
    const sessionInfoRes = await getSessionInfo(curSessionId);

    if (_.get(sessionInfoRes, 'error')) {
      handleRequestFailed('getSessionInfo', _.get(sessionInfoRes, 'error'));
      return;
    }

    const inspectionInfoRes = await getInspectionInfo(ipcProductId);

    if (_.get(inspectionInfoRes, 'error')) {
      handleRequestFailed('getInspectionInfo', _.get(inspectionInfoRes, 'error'));
      return;
    }

    setCurInspectionInfo(_.get(inspectionInfoRes, 'data'));
    setCurSessionInfo(_.get(sessionInfoRes, 'data'));
    setCurSessionTotalIpc(_.get(sessionInfoRes, 'data.total_product_count', 0));

    if (!curSessionInitialized.current) handleGetAllFramesAfterInferenceFinish();
    const getNeighborInspectionsRes = await getNeighborInspections({ product_id: ipcProductId, ipc_session_id: curSessionId });

    if (_.get(getNeighborInspectionsRes, 'error')) {
      handleRequestFailed('getNeighborInspections', _.get(getNeighborInspectionsRes, 'error'));
      return;
    }

    setCurIpcIndex(_.get(getNeighborInspectionsRes, 'data.location', 0));

    // check if next_product_id or prev_product_id is null
    // next_product_id is null means this is the first inspection
    // prev_product_id is null means this is the last inspection

    if (_.isInteger(_.get(getNeighborInspectionsRes, 'data.older_product_id'))) {
      const prevIpcRes = await getInspectionInfo(_.get(getNeighborInspectionsRes, 'data.older_product_id'));
      if (_.get(prevIpcRes, 'error')) {
        handleRequestFailed('getInspectionInfo', _.get(prevIpcRes, 'error'));
        return;
      }
      setPrevGoldenProdId(_.get(prevIpcRes, 'data.golden_product_id'));
      setPrevInspcId(_.get(getNeighborInspectionsRes, 'data.older_product_id'));
    } else {
      setPrevInspcId(null);
      setPrevGoldenProdId(null);
    }

    if (_.isInteger(_.get(getNeighborInspectionsRes, 'data.newer_product_id'))) {
      const nextIpcRes = await getInspectionInfo(_.get(getNeighborInspectionsRes, 'data.newer_product_id'));
      if (_.get(nextIpcRes, 'error')) {
        handleRequestFailed('getInspectionInfo', _.get(nextIpcRes, 'error'));
        return;
      }
      setNextGoldenProdId(_.get(nextIpcRes, 'data.golden_product_id'));
      setNextInspcId(_.get(getNeighborInspectionsRes, 'data.newer_product_id'));
      dispatch(setStayAtLeatestEnabled(false));
    } else {
      setNextInspcId(null);
      setNextGoldenProdId(null);
      dispatch(setStayAtLeatestEnabled(true));
    }

    if (pollLatestIpcRef.current) {
      clearInterval(pollLatestIpcRef.current);
      pollLatestIpcRef.current = null;
    }

    // if (!_.isInteger(_.get(getNeighborInspectionsRes, 'data.newer_product_id'))) {
    //   // latest ipc init auto polling the latest inspection
    //   pollLatestIpcRef.current = setInterval(() => pollLatestInspection(curSessionId), 500);
    // }
    pollLatestIpcRef.current = setInterval(() => pollLatestInspection(curSessionId), 500);

    setIsCurSessionFinished(true);
  };

  const checkRetrainStatusInInit = async () => {
    let modelStatusRes;
    try {
      modelStatusRes = await fetch(`${serverEndpoint}/getModelUpdates`);
    } catch (error) {
      handleRequestFailed('getModelUpdates', error);
      return;
    }
    const modelStatus = await modelStatusRes.json();

    if (checkIfReadyForRetrain(modelStatus)) return;

    dispatch(setIsTrainingRuning(true));

    // some tasks is in progress so lock screen
    // dispatch(setContainerWindowLoadingLocked(true));

    // loopCheckRetrainStatus.current = setInterval(async () => {
    //   let modelStatusRes;
    //   try {
    //     modelStatusRes = await fetch(`${serverEndpoint}/getModelUpdates`);
    //   } catch (error) {
    //     handleRequestFailed('getModelUpdates', error);
    //     clearInterval(loopCheckRetrainStatus.current);
    //     loopCheckRetrainStatus.current = null;
    //     dispatch(setContainerWindowLoadingLocked(false));
    //     return;
    //   }
    //   const modelStatus = await modelStatusRes.json();

    //   if (checkIfReadyForRetrain(modelStatus)) {
    //     clearInterval(loopCheckRetrainStatus.current);
    //     loopCheckRetrainStatus.current = null;
    //     dispatch(setContainerWindowLoadingLocked(false));
    //     // search for the task with greatest schedule_id
    //     const latestTask = _.maxBy(modelStatus, (task) => task.schedule_id);
    //     if (_.includes([retrainModelTaskPhaseType.failure, retrainModelTaskPhaseType.invalid], _.get(latestTask, 'phase'))) {
    //       aoiAlert(translation('notification.error.retrainFailed'), ALERT_TYPES.COMMON_ERROR);
    //       return;
    //     }
    //     aoiAlert(translation('notification.success.retrainFinished'), ALERT_TYPES.COMMON_SUCCESS);
    //     return;
    //   } else if (!isContainerWindowLoadingLocked) dispatch(setContainerWindowLoadingLocked(true));
    // }, 1000);
  };

  const initFilterMap = async () => {
    const res = await getCurProductComponentlist();
    if (res.error) {
      handleRequestFailed('getCurProductComponentlist', res.error);
      return;
    }

    setCurrentFilterMap(_.get(res, 'data.current_product_options', {}));
  };

  useEffect(() => {
    setIpcProductId(props.match.params.ipcProductId);
    setProductId(props.match.params.productId);
    ipcProductIdRef.current = props.match.params.ipcProductId;
    productIdRef.current = props.match.params.productId;
    curSessionInitialized.current = false;

    setCurSessionStepInfo([]);
    // setIsCurSessionFinished(false);
    // setPrevInspcId(null);
    // setNextInspcId(null);
    // setPrevGoldenProdId(null);
    // setNextGoldenProdId(null);
    // setCurSession(null);
    setFrames({});
    // setSelectedFeatureId(null);
    // setSelectedLineItem(null);
    setIsRetrainComfirmationModalOpened(false);
    setIsDrawMaskEnabled(false);
    // setCurDisplayOptionsBrightness(50);
    // setCurDisplayOptionsContrast(50);
    // setCurDisplayOptionsSaturation(50);
    // setIsSharpnessEnabled(false);
    setIsMarkAllFeedbackAsGoodModalOpened(false);
    setIsAdjustPassRateModalOpened(false);
    // setCurProductPassRate(0);
    // setIsCurProductPassRateApproved(true);

    // reset the page
    if (autoRefetchSessionInfo.current) {
      clearInterval(autoRefetchSessionInfo.current);
      autoRefetchSessionInfo.current = null;
    }
    if (autoRefetchSession.current) {
      clearInterval(autoRefetchSession.current);
      autoRefetchSession.current = null;
    }

    clearInterval(loopCheckRetrainStatus.current);
    loopCheckRetrainStatus.current = null;
    checkRetrainStatusInInit();

    fetchFeatures();
    getAllSessionStepInfo();
    initFilterMap();
    handleGetAllStepsFeatures(systemMetadata, props.match.params.productId);
    initInspectionList(props.match.params.ipcProductId, _.first(curRunningIpcSessionIds));
    setExpandedStep(_.isInteger(Number(step)) ? Number(step) : null);
    initTodayIpcCount();

    return () => {
      if (pollLatestIpcRef.current) {
        clearInterval(pollLatestIpcRef.current);
        pollLatestIpcRef.current = null;
      }
    };
  }, [
    props.match.params.ipcProductId,
    props.match.params.productId
  ]);

  useEffect(() => {
    if (!_.isInteger(Number(step))) {
      getAllSessionStepInfo();
      setExpandedStep(null);
      setSelectedFeatureId(null);
      setSelectedLineItem(null);
    } else {
      fetchFeatures();
      setExpandedStep(Number(step));
    }
    return () => {
      if (autoRefetchSessionInfo.current) {
        clearInterval(autoRefetchSessionInfo.current);
        autoRefetchSessionInfo.current = null;
      }
    };
  }, [step]);

  useEffect(() => {
    if (_.isEmpty(curSessionInfo)) return;

    // const tmp = _.round((_.get(curSessionInfo, 'total_product_count', 0)-_.get(curSessionInfo, 'defective_product_count', 0))/_.get(curSessionInfo, 'total_product_count', 0)*100, 4);
    const tmp = displayedTodayIpcGoodCount === 0 ? 0 : _.round((displayedTodayIpcGoodCount / displayedTodayIpcTotalCount) * 100, 4);

    setCurPassRateDelta(tmp - curProductPassRate);

    setCurProductPassRate(tmp);
    setIsCurProductPassRateApproved(tmp >= productPassRates);
    setIsCurProdcutNGAmountExceeded(displayedTodayIpcTotalCount - displayedTodayIpcGoodCount > allowedNGAmount);
  }, [
    productPassRates,
    allowedNGAmount,
    curSessionInfo,
    displayedTodayIpcGoodCount,
    displayedTodayIpcTotalCount
  ]);

  useEffect(() => {
    initTodayIpcCount();
  }, []);

  useEffect(() => {
    initErrorPolling();

    return () => {
      if (errorPollingRef.current) {
        clearInterval(errorPollingRef.current);
        errorPollingRef.current = null;
      }
    };
  }, []);

  if (isViewLiveFullScreenEnabled) {
    return (
      <div className='relative h-full w-full'>
        <div className='absolute top-0 left-0 w-full h-full z-[1]'>
        <InferenceAnnotation
            goldenProdFeatures={goldenProdFeatures}
            selectedFeatureId={selectedFeatureId}
            setSelectedFeatureId={setSelectedFeatureId}
            selectedLineItem={selectedLineItem}
            dataUri={_.get(frames, step)}
            step={step}
            ipcProductId={ipcProductId}
            goldenProductId={productId}
            sessionStepInfo={curSessionStepInfo}
            setSelectedLineItem={setSelectedLineItem}
            fetchFeatures={fetchFeatures}
            isDrawMaskEnabled={isDrawMaskEnabled}
            setIsDrawMaskEnabled={setIsDrawMaskEnabled}
            curDisplayOptionsBrightness={curDisplayOptionsBrightness}
            setCurDisplayOptionsBrightness={setCurDisplayOptionsBrightness}
            curDisplayOptionsContrast={curDisplayOptionsContrast}
            setCurDisplayOptionsContrast={setCurDisplayOptionsContrast}
            curDisplayOptionsSaturation={curDisplayOptionsSaturation}
            setCurDisplayOptionsSaturation={setCurDisplayOptionsSaturation}
            isSharpnessEnabled={isSharpnessEnabled}
            setIsSharpnessEnabled={setIsSharpnessEnabled}
            curSession={curSessionInfo}
            curIpc={curInspectionInfo}
            drawMaskViewerRef={drawMaskViewerRef}
            goldenProductInfo={goldenProduct}
            redirectToFullView={() => props.history.push(`/aoi/view-inference/${productId}/${ipcProductId}`)}
            isLive
            isGoldenComponentChecklistOpened={isGoldenComponentChecklistOpened}
            viewInspectionViewMode={viewInspectionViewMode}
            isFullScreen={isViewLiveFullScreenEnabled}
          />
        </div>
        <div className='absolute top-6 left-6 z-[2]'>
          <div
            className={`flex p-2 flex-col justify-center items-center gap-4 self-stretch rounded-[8px] border-[1px]`}
            style={{
              background: `${
                isCurProductPassRateApproved ?
                  isViewLiveFullScreenEnabled ? 'rgba(39, 174, 96, 0.75)' : '#27AE60'
                :
                  isViewLiveFullScreenEnabled ? 'rgba(235, 87, 87, 0.75)' : '#EB5757'
                }`,
              borderColor: `${isCurProductPassRateApproved ? '#1CD48C' : '#EB5757'}`,
            }}
          >
            <div className='flex items-center gap-2 justify-center self-stretch'>
              <span
                className='font-source text-[20px] font-semibold overflow-hidden overflow-ellipsis whitespace-nowrap'
                title={_.get(goldenProduct, 'product_name', 'unknown product')}
              >
                {translation('viewInspection.currentProductName')}: {_.get(goldenProduct, 'product_name', 'unknown product')}
              </span>
            </div>
            <div className='flex items-center gap-2'>
              <span className='font-source text-[20px] font-semibold'>
                {translation('viewInspection.todayPassRate')}
              </span>
            </div>
            <div className='flex justify-center items-center gap-2'>
              {curPassRateDelta > 0 &&
                <img src='/img/icn/icn_deltaUp_white.svg' className='w-[8px] h-[7px]' alt='delta' />
              }
              { curPassRateDelta < 0 &&
                <img src='/img/icn/icn_deltaDown_white.svg' className='w-[8px] h-[7px]' alt='delta' />
              }
              { curPassRateDelta === 0 &&
                <div className='flex w-[8px] h-[30px] justify-center items-center'>
                  <div className='w-[8px] h-[1px] bg-[#fff]' />
                </div>
              }
              <span className='font-source text-[20px] font-semibold'>
                {/* {`${curProductPassRate}% ${translation('viewInspection.pass')}`} */}
                {`${curProductPassRate}% ${translation('viewInspection.pass')}`}
              </span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <MainMenuLayout>
      <RetrainConfirmation
        isOpened={isRetrainComfirmationModalOpened}
        setIsOpened={setIsRetrainComfirmationModalOpened}
        handleRetrainTrigger={handleRetrainModel}
        defaultGoldenProductId={Number(productId)}
      />
      <SubmitAllGoodFeedback
        isOpened={isMarkAllFeedbackAsGoodModalOpened}
        setIsOpened={setIsMarkAllFeedbackAsGoodModalOpened}
        productInfo={goldenProduct}
        goldenProdFeatures={goldenProdFeatures}
        handleSubmit={(variant) => handleMarkAllFeedbackAsGood(ipcProductId, variant)}
      />
      <AdjustPassRate
        isOpened={isAdjustPassRateModalOpened}
        setIsOpened={setIsAdjustPassRateModalOpened}
      />
      <SwitchGoldenProduct
        isOpened={isSwitchGoldenProductModalOpened}
        setIsOpened={setIsSwitchGoldenProductModalOpened}
      />
      <div className='relative h-full w-full'>
        {/* <LiveComponentFilter
          isLiveComponentFilterOpened={isLiveComponentFilterOpened}
          setIsLiveComponentFilterOpened={setIsLiveComponentFilterOpened}
          currentFilterMap={currentFilterMap}
          setCurrentFilterMap={setCurrentFilterMap}
        /> */}
        <div className='absolute top-0 left-0 w-full h-full z-[1]'>
        <ViewInspectionLayout
          goldenProdName={_.get(goldenProduct, 'product_name')}
          handleRedirect={(url) => props.history.push(url)}
          step={step}
          setSelectedFeatureId={setSelectedFeatureId}
        setSelectedLineItem={setSelectedLineItem}
        >
          <div className='flex h-[48px] justify-between items-center self-stretch bg-[#ffffff1a] py-1.5 px-8'>
            <div className='flex items-center gap-6'>
              <div className='flex items-center gap-1'>
                <span className='font-source text-[12px] font-semibold'>
                  {translation('viewInspection.curInspectionGoldenProduct')}
                </span>
                <span className='font-source text-[12px] font-normal'>
                  {_.get(goldenProduct, 'product_name')}
                </span>
                <div
                  className='flex px-2 py-1 items-center cursor-pointer hover:bg-[#ffffff1a] rounded-[4px] transition-all duration-300 ease-in-out'
                  onClick={() => {
                    setIsSwitchGoldenProductModalOpened(true);
                  }}
                >
                  <span className='font-source text-[12px] font-normal text-AOI-blue'>
                    {translation('viewInspection.switchGoldenProduct')}
                  </span>
                </div>
              </div>
              <div className='flex items-center gap-1'>
                <span className='font-source text-[12px] font-semibold'>
                  {translation('viewBoards.sn')}
                </span>
                <span className='font-source text-[12px] font-normal'>
                  {_.get(curInspectionInfo, 'product_serial_no', 'product_sn placeholder')}
                </span>
              </div>
            </div> 
            <div className='flex items-center gap-6'>
              <AOIGreenButtonConfig>
                <Button
                  disabled={!isInferenceRunning}
                  onClick={async () => {
                    setSelectedFeatureId(null);
                    setSelectedLineItem(null);

                    dispatch(setContainerWindowLoadingLocked(true));
                    const res = await inferenceTrigger();
                    if (_.get(res, 'error')) {
                      handleRequestFailed('inferenceTrigger', _.get(res, 'error'));
                      dispatch(setContainerWindowLoadingLocked(false));
                      return;
                    }
                    // NOTE: in inference trigger via http's case the new product write and inference result write are not atomic in backend
                    // so we need to refetch the session info after inference trigger returns
                    // await refetchSessionInfo();
                    const sessionInfoRes = await getSessionInfo(_.first(curRunningIpcSessionIds));
                    if (_.get(sessionInfoRes, 'error')) {
                      handleRequestFailed('getSessionInfo', _.get(sessionInfoRes, 'error'));
                      dispatch(setContainerWindowLoadingLocked(false));
                      return;
                    }
                    setCurSessionInfo(_.get(sessionInfoRes, 'data'));
                    dispatch(setContainerWindowLoadingLocked(false));
                    aoiAlert(translation('notification.success.inferenceFinished'), ALERT_TYPES.COMMON_SUCCESS);
                  }}
                >
                  <span className='font-source text-[12px] font-normal'>
                    {translation('viewInspection.inspectionTrigger')}
                  </span>
                </Button>
              </AOIGreenButtonConfig>
              {/* {process.env.NODE_ENV === 'development' &&
                <AOIGreenButtonConfig>
                  <Button
                    onClick={async () => {
                      // check if inference is started
                      const statusRes = await getInferenceStatus();
                      if (_.get(statusRes, 'data.status') !== 'stopped') {
                        aoiAlert(translation('notification.error.inferenceRunningPleaseStopFirst'), ALERT_TYPES.COMMON_ERROR);
                        return;
                      }
                      const res = await replaySession({ session_id: Number(ipcProductId) });
                      if (_.get(res, 'error')) {
                        handleRequestFailed('replaySession', _.get(res, 'error'));
                        return;
                      }
                      aoiAlert(translation('notification.success.replaySession'), ALERT_TYPES.COMMON_SUCCESS);
                      return;
                    }}
                  >
                    <span className='font-source text-[12px] font-normal'>
                      {translation('viewInspection.replaySession')}
                    </span>
                  </Button>
                </AOIGreenButtonConfig>
              } */}
              <AOIGreenButtonConfig>
                <Button
                  onClick={() => {
                    setIsRetrainComfirmationModalOpened(true);
                  }}
                >
                  <span className='font-source text-[12px] font-normal'>
                    {translation('viewInspection.retrainModel')}
                  </span>
                </Button>
              </AOIGreenButtonConfig>
              <div className='flex gap-2 self-stretch items-center'>
                <span className='font-source text-[12px] font-normal'>
                  {`${getInspectionCountByInspectionViewLayout(_.get(systemMetadata, 'inspection_view_layout'))} ${translation('viewBoards.images')}`}
                </span>
              </div>
              {isCurSessionFinished ?
                <RoundLabel>
                  <div className='flex items-center gap-2'>
                    <img src='/img/icn/icn_checkerFlag_white.svg' alt='flag' className='w-[9px] h-[10px] fill-[#fff]' />
                    <span className='font-source text-[12px] font-semibold'>
                      {translation('viewBoards.detectionFinished')}
                    </span>
                  </div>
                </RoundLabel> :
                <RoundLabel bgColor={primaryColor} textColor='#333'>
                <div className='flex items-center gap-2'>
                  <ConfigProvider
                    theme={{
                      components: {
                        Spin: {
                          dotSize: 10
                        },
                      },
                    }}
                  >
                    <Spin />
                  </ConfigProvider>
                  <span className='font-source text-[12px] font-semibold'>
                    {translation('viewInspection.inspectionInprogress')}
                  </span>
                </div>
              </RoundLabel>
              }
              <Fragment>
                <span className='font-source text-[12px] font-semibold'>
                  {`${curIpcIndex} / ${curSessionTotalIpc}`}
                </span>
                <div className='flex items-center gap-2 self-stretch'>
                  <Tooltip
                    title={<span className='font-source text-[12px] font-normal'>{translation('viewInspection.previous')}</span>}
                  >
                    <div
                      className={`flex w-6 h-6 justify-center items-center ${(_.isNull(nextInspcId) || _.isNull(nextGoldenProdId)) ? 'cursor-not-allowed' : 'cursor-pointer'}`}
                      onClick={() => {
                        if (_.isNull(nextInspcId) || _.isNull(nextGoldenProdId)) return;
                        // window.location.href = `/aoi/view-inference/${nextGoldenProdId}/${nextInspcId}`;
                        dispatch(setStayAtLeatestEnabled(false));
                        setIsSharpnessEnabled(false);
                        props.history.push(`/aoi/view-inference/${nextGoldenProdId}/${nextInspcId}${_.isInteger(Number(step)) ? `?step=${step}` : ''}`);
                      }}
                    >
                      <img src='/img/icn/icn_arrowLeft_white.svg' className='w-[6px] h-[12px] shrink' alt='arrowLeft' />
                    </div>
                  </Tooltip>
                  {isCurSessionFinished &&
                    <Tooltip
                      title={<span className='font-source text-[12px] font-normal'>{translation('viewInspection.next')}</span>}
                    >
                      <div
                        className={`flex w-6 h-6 justify-center items-center ${(_.isNull(prevInspcId) || _.isNull(prevGoldenProdId)) ? 'cursor-not-allowed' : 'cursor-pointer'}`}
                        onClick={() => {
                          if (_.isNull(prevInspcId) || _.isNull(prevGoldenProdId)) return;
                          // window.location.href = `/aoi/view-inference/${prevGoldenProdId}/${prevInspcId}`;
                          dispatch(setStayAtLeatestEnabled(false));
                          setIsSharpnessEnabled(false);
                          props.history.push(`/aoi/view-inference/${prevGoldenProdId}/${prevInspcId}${_.isInteger(Number(step)) ? `?step=${step}` : ''}`);
                        }}
                      >
                        <img src='/img/icn/icn_arrowRight_white.svg' className='w-[6px] h-[12px] shrink' alt='arrowRight' />
                      </div>
                    </Tooltip>
                  }
                </div>
              </Fragment>
            </div>
          </div>
          { isCurSessionFinished && !_.isEmpty(curSessionInfo) &&
            <div className='flex px-8 items-start gap-2 flex-1 self-stretch'>
              <div
                // className='flex w-[363px] pr-1 flex-col items-start self-stretch gap-6 overflow-y-auto'
                className='flex w-[269px] pr-1 flex-col items-start self-stretch gap-6 overflow-y-auto'
                style={{ height: 'calc(100vh - 198px)' }}
              >
                {/* current product inference result starts */}
                <div
                  className={`flex py-3 px-6 flex-col justify-center items-center gap-1 rounded-[8px] border-[1px] w-full`}
                  style={{
                    background: `${_.get(curInspectionInfo, 'defect_count') === 0 ? 'rgba(39, 174, 96, 0.75)' : 'rgba(235, 87, 87, 0.75)'}`,
                    borderColor: `${_.get(curInspectionInfo, 'defect_count') === 0 ? '#81F499' : '#eb5757'}`,
                  }}
                >
                  <div className='flex items-center gap-2 justify-center'>
                    <span className='font-source text-[20px] font-semibold'>
                      {`${translation('liveDashboard.currentProduct')} ${_.get(curInspectionInfo, 'defect_count') === 0 ? translation('viewInspection.pass') : translation('viewInspection.defective')}`}
                    </span>
                  </div>
                </div>
                {/* current product inference result ends */}
                {/* gird view selection starts */}
                <div
                  className='flex flex-col items-start self-stretch rounded-[2px] border-[1px] border-gray-2'
                  style={{ background: 'rgba(255, 255, 255, 0.05)' }}
                >
                  <div className='flex flex-col items-start self-stretch'>
                    <div
                      className='flex p-2 justify-between items-center self-stretch'
                      style={{ background: 'rgba(255, 255, 255, 0.04)' }}
                    >
                      <span className='font-source text-[12px] font-semibold'>
                        {translation('common.display')}
                      </span>
                      <div
                        className='flex items-center gap-2 cursor-pointer hover:bg-gray-2 rounded-[2px] p-1 transition-all duration-150 ease-in-out'
                        onClick={() => {
                          if (!_.isEmpty(step)) props.history.push(`/aoi/view-inference/${productId}/${ipcProductId}`);
                        }}
                      >
                        <img src='/img/icn/icn_customGridLayout_blue.svg' alt='grid' className='w-[16px] h-[11px] fill-AOI-blue' />
                        <span className='font-source text-[12px] font-normal text-AOI-blue'>
                          {translation('common.viewAll')}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className='flex flex-col items-center gap-1 self-stretch py-1'>
                    <MultiViewGridSelection
                      layout={_.get(systemMetadata, 'inspection_view_layout')}
                      selectedViewId={Number(step)}
                      onSelectView={(viewId) => props.history.push(`/aoi/view-inference/${productId}/${ipcProductId}?step=${viewId}`)}
                      defectSteps={_.get(curInspectionInfo, 'failed_count_map', {})}
                    />
                    <span className='font-source text-[12px] font-normal'>
                      {_.isInteger(Number(step)) ? `${translation('common.detectionStep')} ${step}` : translation('common.allDetectionSteps')}
                    </span>
                  </div>
                </div>
                {/* <Button
                  onClick={() => {
                    dispatch(setIsComponentFilterVisible(!isComponentFilterVisible));
                  }}
                  style={{ width: '100%' }}
                >
                  <div className='flex items-center gap-1'>
                    <span
                      className='font-source text-[12px] font-normal'
                      style={{ lineHeight: 'normal' }}
                    >
                      {isComponentFilterVisible ? translation('viewInspection.hideComponentFilter') : translation('viewInspection.showComponentFilter')}
                    </span>
                    <img src='/img/icn/icn_arrowRight_blue.svg' className='w-2.5 h-2.5' alt='arrowRight' />
                  </div>
                </Button> */}
                {/* <Button
                  style={{ width: '100%' }}
                  size='small'
                  onClick={() => dispatch(setIsGoldenComponentChecklistOpened(!isGoldenComponentChecklistOpened))}
                >
                  <div className='flex items-center gap-1'>
                    <span className='font-source text-[12px] font-normal'>
                      {translation('viewInspection.showChecklist')}
                    </span>
                    <img src='/img/icn/icn_arrowRight_blue.svg' className='w-2.5 h-2.5' alt='arrowRight' />
                  </div>
                </Button> */}
                {/* gird view selection ends */}
                { viewInspectionViewMode === 'review' &&
                  <div className='flex py-2 flex-col items-start gap-4 self-stretch h-full'>
                    <div className='flex items-center gap-2'>
                      <span className='font-source text-[16px] font-semibold'>
                        {translation('viewInspection.todayInspectionOverview')}
                      </span>
                      {!_.get(store.getState(), 'setting.isStayAtLeatestEnabled') &&
                        <Button
                          size='small'
                          onClick={() => initTodayIpcCount()}
                        >
                          <span className='font-source text-[12px] font-normal'>
                            {translation('common.refresh')}
                          </span>
                        </Button>
                      }
                      <Button
                        size='small'
                        onClick={async () => {
                          await resetSession();
                          initTodayIpcCount();
                        }}
                      >
                        <span className='font-source text-[12px] font-normal'>
                          {translation('common.reset')}
                        </span>
                      </Button>
                    </div>
                    <div
                      className={`flex p-4 flex-col justify-center items-center gap-4 self-stretch rounded-[8px] border-[1px] flex-1`}
                      style={{
                        background: `${isCurProductPassRateApproved ? ' #27AE60' : '#EB5757'}`,
                        borderColor: `${isCurProductPassRateApproved ? '#1CD48C' : '#EB5757'}`,
                      }}
                    >
                      <div className='flex items-center gap-2'>
                        <span className='font-source text-[20px] font-semibold'>
                          {translation('viewInspection.todayPassRate')}
                        </span>
                      </div>
                      <div className='flex justify-center items-center gap-2'>
                        {curPassRateDelta > 0 &&
                          <img src='/img/icn/icn_deltaUp_white.svg' className='w-[8px] h-[7px]' alt='delta' />
                        }
                        { curPassRateDelta < 0 &&
                          <img src='/img/icn/icn_deltaDown_white.svg' className='w-[8px] h-[7px]' alt='delta' />
                        }
                        { curPassRateDelta === 0 &&
                          <div className='flex w-[8px] h-[30px] justify-center items-center'>
                            <div className='w-[8px] h-[1px] bg-[#fff]' />
                          </div>
                        }
                        <span className='font-source text-[20px] font-semibold'>
                          {/* {`${curProductPassRate}% ${translation('viewInspection.pass')}`} */}
                          {`${curProductPassRate}% ${translation('viewInspection.pass')}`}
                        </span>
                      </div>
                    </div>
                    <div className='flex pt-2 pr-4 pb-2 pl-2 items-center gap-2 self-stretch rounded-[4px] bg-[#333]'>
                      <div className='flex w-[32px] h-[32px] justify-center items-center'>
                        <img src='/img/icn/icn_delivery_white.svg' className='w-[18px] h-[18px]' alt='delivery' />
                      </div>
                      <div className='flex justify-between items-center flex-1'>
                        <span className='font-source text-[12px] font-normal'>
                          {translation('viewInspection.todayTotalItems')}
                        </span>
                        <span className='font-source text-[18px] font-normal'>
                          {/* {_.get(curSessionInfo, 'total_product_count', 0)} */}
                          {displayedTodayIpcTotalCount}
                        </span>
                      </div>
                    </div>
                    <div className='flex pt-2 pr-4 pb-2 pl-4 items-center gap-2 self-stretch rounded-[4px] bg-[#333]'>
                      <span className='font-source text-[12px] font-normal'>
                        {translation('viewInspection.todayOkNg')}
                      </span>
                      <div className='flex items-center flex-1 gap-2'>
                        <div className='flex items-center gap-1'>
                          <img src='/img/icn/icn_checkCircle_green.svg' className='w-4 h-4' alt='check-circle' />
                          <span className='font-source text-[18px] font-semibold'>
                            {/* {_.get(curSessionInfo, 'total_product_count', 0) - _.get(curSessionInfo, 'defective_product_count', 0)} */}
                            {displayedTodayIpcGoodCount}
                          </span>
                        </div>
                        <div className='flex items-center gap-1'>
                          <img src='/img/icn/icn_warning_red.svg' className='w-4 h-4' alt='warning' />
                          {/* <span className={`font-source text-[18px] font-semibold ${!isCurProdcutNGAmountExceeded ? 'text-white' : 'text-[#EB5757]'}`}>
                            {displayedTodayIpcTotalCount - displayedTodayIpcGoodCount}
                          </span> */}
                          <span className='font-source text-[18px] font-semibold'>
                            {displayedTodayIpcTotalCount - displayedTodayIpcGoodCount}
                          </span>
                        </div>
                      </div>
                    </div>
                    <Button
                      style={{ width: '100%' }}
                      onClick={() => setIsAdjustPassRateModalOpened(true)}
                    >
                      <img src='/img/icn/icn_ellipsis_white.svg' className='w-4 h-4' alt='check-circle' />
                    </Button>
                  </div>
                }
                { viewInspectionViewMode === 'single' &&
                  <div className='flex flex-col items-start self-stretch'>
                    <div className='flex justify-between items-center content-center self-stretch flex-wrap'>
                      <span className='font-source text-[14px] font-semibold'>
                        {translation('viewInspection.predictionList')}
                      </span>
                      <div className='flex items-center gap-1'>
                        <div className='flex w-3 h-3 flex-col justify-center items-center'>
                          <img src='/img/icn/icn_warning_red.svg' className='w-4 h-[10.5px]' alt='warning' />
                        </div>
                        <span className='font-source text-[12px] font-normal'>
                          {translation('viewInspection.defectsDetected', { totalDefectsCount: String(_.get(curInspectionInfo, 'defect_count', 0)) })}
                        </span>
                      </div>
                    </div>
                    <div className='flex flex-col items-start self-stretch gap-1 py-2'>
                      <span className='font-source text-[10px] font-normal italic text-gray-6'>
                        {translation('viewInspection.submitGoodFeedbackForAll')}
                      </span>
                      <Button
                        style={{ width: '100%' }}
                        onClick={() => setIsMarkAllFeedbackAsGoodModalOpened(true)}
                      >
                        <span className='font-source text-[12px] font-normal'>
                          {translation('viewInspection.submitGoodFeedbackForAllInspectionItems')}
                        </span>
                      </Button>
                    </div>
                    <div
                      className='flex flex-col items-start self-stretch overflow-y-auto'
                      // style={{ height: 'calc(100vh - 465px)' }}
                      style={{ height: 'calc(100vh - 335px - 168px - 80px - 24px)' }}
                    >
                      <CustomCollapse
                        style={{ width: '100%' }}
                        activeKey={[expandedStep]}
                        activeTabKey={expandedStep}
                        isActive
                        onChange={(key) => {
                          if (key.length === 0) {
                            props.history.push(`/aoi/view-inference/${productId}/${ipcProductId}`);
                            setExpandedStep(null);
                            return;
                          }
                          if (key.length === 1) {
                            if (key[0] === expandedStep) return;
                            props.history.push(`/aoi/view-inference/${productId}/${ipcProductId}?step=${key[0]}`);
                          }
                          setExpandedStep(key[1]);
                          props.history.push(`/aoi/view-inference/${productId}/${ipcProductId}?step=${key[1]}`);
                        }}
                        items={_.times(_.sum(_.get(systemMetadata, 'inspection_view_layout')), (index) => (
                          {
                            key: index,
                            label: <div className='flex items-center h-full justify-between'>
                              <span className='font-source text-[12px] font-normal'>
                                {`${translation('viewInspection.inferenceStep')} ${index + 1}`}
                              </span>
                              { _.get(curInspectionInfo, `failed_count_map.${index}`, 0) === 0 ? <></> :
                                <div className='flex px-4 items-center gap-1'>
                                  <div className='flex w-3 h-3 flex-col justify-center items-center'>
                                    <img src='/img/icn/icn_warning_red.svg' className='w-[12px] h-[10px] shrink' alt='warning' />
                                  </div>
                                  <span className='font-source text-[12px] font-normal text-red'>
                                    {_.get(curInspectionInfo, `failed_count_map.${index}`, 0)}
                                  </span>
                                </div>
                              }
                            </div>,
                            children: <FeatureList
                              step={step}
                              ipcProductId={ipcProductId}
                              curSession={curSessionInfo}
                              handleSelectFeature={setSelectedFeatureId}
                              selectedFeatureId={selectedFeatureId}
                              curSessionStepInfo={curSessionStepInfo}
                              goldenProductId={productId}
                              setSelectedLineItem={setSelectedLineItem}
                              selectedLineItem={selectedLineItem}
                              setIsDrawMaskEnabled={setIsDrawMaskEnabled}
                              goldenProdFeatures={goldenProdFeatures}
                            />
                          }
                        ))}
                      />
                    </div>
                  </div>
                }
              </div>
              {/* { isGoldenComponentChecklistOpened &&
                <GoldenComponentChecklist
                  // setIsGoldenComponentChecklistOpened={setIsGoldenComponentChecklistOpened}
                  allSessionStepInfo={allSessionStepInfo}
                  allStepsFeatures={allStepsFeatures}
                />
              } */}
              {/* { viewInspectionViewMode === 'review' && isComponentFilterVisible &&
                <FilterComponentCol
                  currentFilterMap={currentFilterMap}
                  setCurrentFilterMap={setCurrentFilterMap}
                />
              } */}
              <div
                className='flex flex-col items-start flex-1 self-stretch rounded-[2px]'
                style={{ height: 'calc(100vh - 198px)' }}
              >
                { !_.isInteger(Number(step)) &&
                  <AllStepInferenceDisplay
                    systemMetadata={systemMetadata}
                    ipcProductId={ipcProductId}
                    allSessionStepInfo={allSessionStepInfo}
                    goldenProdInfo={goldenProduct}
                    ipcInfo={curInspectionInfo}
                    handleRedirect={(step) => props.history.push(`/aoi/view-inference/${productId}/${ipcProductId}?step=${step}`)}
                    isGoldenComponentChecklistOpened={isGoldenComponentChecklistOpened}
                    viewInspectionViewMode={viewInspectionViewMode}
                    isLive={true}
                    allStepsFeatures={allStepsFeatures}
                  />
                }
                { _.isInteger(Number(step)) && (
                  <InferenceAnnotation
                    goldenProdFeatures={goldenProdFeatures}
                    selectedFeatureId={selectedFeatureId}
                    setSelectedFeatureId={setSelectedFeatureId}
                    selectedLineItem={selectedLineItem}
                    dataUri={_.get(frames, step)}
                    step={step}
                    ipcProductId={ipcProductId}
                    goldenProductId={productId}
                    sessionStepInfo={curSessionStepInfo}
                    setSelectedLineItem={setSelectedLineItem}
                    fetchFeatures={fetchFeatures}
                    isDrawMaskEnabled={isDrawMaskEnabled}
                    setIsDrawMaskEnabled={setIsDrawMaskEnabled}
                    curDisplayOptionsBrightness={curDisplayOptionsBrightness}
                    setCurDisplayOptionsBrightness={setCurDisplayOptionsBrightness}
                    curDisplayOptionsContrast={curDisplayOptionsContrast}
                    setCurDisplayOptionsContrast={setCurDisplayOptionsContrast}
                    curDisplayOptionsSaturation={curDisplayOptionsSaturation}
                    setCurDisplayOptionsSaturation={setCurDisplayOptionsSaturation}
                    isSharpnessEnabled={isSharpnessEnabled}
                    setIsSharpnessEnabled={setIsSharpnessEnabled}
                    curSession={curSessionInfo}
                    curIpc={curInspectionInfo}
                    drawMaskViewerRef={drawMaskViewerRef}
                    goldenProductInfo={goldenProduct}
                    redirectToFullView={() => props.history.push(`/aoi/view-inference/${productId}/${ipcProductId}`)}
                    isLive
                    isGoldenComponentChecklistOpened={isGoldenComponentChecklistOpened}
                    viewInspectionViewMode={viewInspectionViewMode}
                  />
                )}
              </div>
              { !_.isNull(selectedFeatureId) && !_.isNull(selectedLineItem) && 
                <div
                  className='flex flex-col gap-2 items-start self-stretch'
                  style={{ width: '458px', height: 'calc(100vh - 198px)' }}
                >
                  <ReferenceComparison
                    goldenProduct={goldenProduct}
                    goldenProdFeatures={goldenProdFeatures}
                    selectedFeatureId={selectedFeatureId}
                    curSessionStepInfo={curSessionStepInfo}
                    sessionInfo={curSessionInfo}
                    inspectionInfo={curInspectionInfo}
                    goldenProductId={productId}
                    ipcProductId={ipcProductId}
                    selectedLineItem={selectedLineItem}
                    setSelectedLineItem={setSelectedLineItem}
                    step={step}
                    handleReDirect={(path) => props.history.push(path)}
                    curDisplayOptionsBrightness={curDisplayOptionsBrightness}
                    curDisplayOptionsContrast={curDisplayOptionsContrast}
                    curDisplayOptionsSaturation={curDisplayOptionsSaturation}
                    // isSharpnessEnabled={isSharpnessEnabled}5
                    ipcProductIdRef={ipcProductIdRef}
                    setSelectedFeatureId={setSelectedFeatureId}
                    fetchFeatures={fetchFeatures}
                    setIsDrawMaskEnabled={setIsDrawMaskEnabled}
                    drawMaskViewerRef={drawMaskViewerRef}
                    isDrawMaskEnabled={isDrawMaskEnabled}
                  />
                </div>
              }
            </div>
          }
        </ViewInspectionLayout>
        </div>
      </div>
    </MainMenuLayout>
  );
};

const FeatureCollapse = styled(Collapse)`
  border-radius: 0 !important;
  .ant-collapse-content-box {
    padding: 0 !important;
  }
  .ant-collapse-header {
    padding: 4px 8px !important;
    border-radius: 0 !important;
  }
  .ant-collapse-header[aria-expanded="true"] {
    background: rgba(86, 204, 242, 0.10) !important;
    color: #56CCF2 !important;
  }
`;

const FeatureList = (props) => {
  const {
    step,
    handleSelectFeature,
    selectedFeatureId,
    goldenProductId,
    curSessionStepInfo, // session step info
    setSelectedLineItem,
    selectedLineItem,
    setIsDrawMaskEnabled,
    goldenProdFeatures,
  } = props;

  const FeatureCollapseRef = useRef(null);

  const [curStepFeatures, setCurStepFeatures] = useState([]);
  const [selectedFeature, setSelectedFeature] = useState({});

  useEffect(() => {
    const updateFeatures = async () => {
      if (_.isUndefined(step) || _.isEmpty(goldenProdFeatures)) return;
      setCurStepFeatures(goldenProdFeatures);
      setSelectedFeature(_.find(goldenProdFeatures, (f) => f.feature_id === selectedFeatureId));
    };

    updateFeatures();
  }, [step, curSessionStepInfo, selectedFeatureId, goldenProdFeatures]);

  useEffect(() => {
    if (!_.isInteger(Number(selectedFeatureId))) return;
    
    const run = async () => {
      await sleep(330);

      const selectedCollapseItem = _.find(FeatureCollapseRef.current.children, item => {
        return _.includes(item.className, 'ant-collapse-item-active');
      });

      if (selectedCollapseItem) {
        selectedCollapseItem.scrollIntoView({ behavior: 'smooth' }); 
      }
    };

    run();
  }, [selectedFeatureId]);

  return (
    <div className='flex flex-col flex-1 self-stretch'>
      <FeatureCollapse
        ref={FeatureCollapseRef}
        isActive
        activeTabKey={String(selectedFeatureId)}
        style={{ width: '100%' }}
        activeKey={!_.isNull(selectedFeatureId) ? [String(selectedFeatureId)] : []}
        onChange={(key) => {
          setIsDrawMaskEnabled(false);
          if (key.length === 0) {
            handleSelectFeature(null);
            setSelectedLineItem(null);
            return;
          } else if (key.length === 1) {
            // check if there is any line item in cur session step info
            if (!_.find(curSessionStepInfo, (item) => item.feature_id === Number(key[0]))) {
              aoiAlert(translation('notification.error.canNotFindAnyInferenceResultForThisComponent'), ALERT_TYPES.COMMON_ERROR);
              return;
            }
            handleSelectFeature(Number(key[0]));
            // select the first line item
            setSelectedLineItem(`${Number(key[0])}-${
              _.get(
                _.find(curSessionStepInfo, (item) => item.feature_id === Number(key[0])),
                'detail',
                null
              )
            }`);
            return;
          }
          if (!_.find(curSessionStepInfo, (item) => item.feature_id === Number(key[1]))) {
            aoiAlert(translation('notification.error.canNotFindAnyInferenceResultForThisComponent'), ALERT_TYPES.COMMON_ERROR);
            return;
          }
          handleSelectFeature(Number(key[1]));
          setSelectedLineItem(`${Number(key[1])}-${
            _.get(
              _.find(curSessionStepInfo, (item) => item.feature_id === Number(key[1])),
              'detail',
              null
            )
          }`);
        }}
        items={!_.isEmpty(curStepFeatures) ? _.map(curStepFeatures, (f, index) => {
          return {
            key: String(_.get(f, 'feature_id', '0')),
            label: <div className='flex items-center h-full justify-between'>
              <div className='flex items-center gap-2'>
                <div className='h-[14px] w-[14px] rounded-[50%]' style={{ backgroundColor: getColorByStr(_.get(f, 'feature_type')) }} />
                {/* <img src='/img/icn/icn_circleFilled_orange.svg' className='w-[10px] h-[10px]' alt='circle' /> */}
                <span className='font-source text-[12px] font-normal self-stretch'>
                  {`${getFeatureTypeDisplayText(_.get(f, 'feature_scope') === 'global' ? _.get(f, 'feature_type') : _.get(f, 'feature_type').substring(1))}-${_.get(f, 'feature_id', '0')}`}
                </span>
              </div>
              { _.filter(curSessionStepInfo, (item) => item.feature_id === _.get(f, 'feature_id', null) && !item.pass).length > 0 &&
                <div className='flex items-center gap-2'>
                  <div className='flex w-3 h-3 flex-col justify-center items-center'>
                    <img src='/img/icn/icn_warning_red.svg' className='w-4 h-[10.5px] shrink' alt='warning' />
                  </div>
                </div>
              }
            </div>,
            children: <Fragment>
              {_.map(
                _.filter(curSessionStepInfo, (item) => item.feature_id === _.get(f, 'feature_id', null)),
                (info, index) => {
                  if (info.pass) {
                    return <div
                      className={`flex h-[32px] py-1 px-2 justify-center 
                        items-center self-stretch gap-2 cursor-pointer 
                        ${_.isNull(selectedLineItem) && selectedFeatureId === _.get(info, 'feature_id', null) && 'bg-[rgba(255,255,255,0.1)]'}
                        ${!_.isNull(selectedLineItem) && selectedLineItem === `${_.get(info, 'feature_id', null)}-${_.get(info, 'detail', null)}` && 'text-AOI-blue bg-[rgba(255,255,255,0.1)]'}`}
                      key={index}
                      onClick={() => {
                        handleSelectFeature(_.get(info, 'feature_id', null));
                        setSelectedLineItem(`${_.get(info, 'feature_id', null)}-${_.get(info, 'detail', null)}`);
                      }}
                    >
                      <Tooltip
                        placement='right'
                        title={<div className='flex flex-col items-start gap-1'>
                          <span className='font-source text-[12px] font-normal'>
                            {`${getFeatureTypeDisplayText(_.get(f, 'feature_scope') === 'global' ? _.get(f, 'feature_type') : _.get(f, 'feature_type').substring(1))}-${translation(`viewInspection.lineItem.${_.get(info, 'detail', null)}`)}-${translation(`viewInspection.${_.get(info, 'pass') ? 'lineItemGoodResult' : 'lineItemBadResult'}`)}`}
                          </span>
                          {_.get(info, 'feedback', {}) &&
                            <span className='font-source text-[12px] font-normal'>
                              {translation('viewInspection.currentFeedback')}
                              {(_.get(info, 'feedback.correct') === true && _.get(info, 'pass')) ? 
                              translation('viewInspection.lineItemGoodResult') : translation('viewInspection.badFeedback')}
                            </span>
                          }
                        </div>}
                      >
                        <div className='flex gap-2 flex-1 items-center justify-between px-2'>
                          <span className='font-source text-[12px] font-normal overflow-hidden flex-1 whitespace-nowrap text-ellipsis w-[140px]'>
                            {`${getFeatureTypeDisplayText(_.get(f, 'feature_scope') === 'global' ? _.get(f, 'feature_type') : _.get(f, 'feature_type').substring(1))}-${translation(`viewInspection.lineItem.${_.get(info, 'detail', null)}`)}-${translation(`viewInspection.${_.get(info, 'pass') ? 'lineItemGoodResult' : 'lineItemBadResult'}`)}`}
                          </span>
                          {!_.isEmpty(_.get(info, 'feedback', {})) &&
                            <div className='flex w-6 h-6 flex-col justify-center items-center'>
                              <img src='/img/icn/icn_pencil_blue.svg' alt='pencil' className='w-3 h-3' />
                            </div>
                          }
                        </div>
                      </Tooltip>
                    </div>
                  } else {
                    return <div
                      className={`flex h-[32px] py-1 pr-2 pl-4 justify-center 
                        items-center self-stretch gap-2 cursor-pointer 
                        ${_.isNull(selectedLineItem) && selectedFeatureId === _.get(info, 'feature_id', null) && 'bg-[rgba(255,255,255,0.1)]'} 
                        ${!_.isNull(selectedLineItem) && selectedLineItem === `${_.get(info, 'feature_id', null)}-${_.get(info, 'detail', null)}` && 'text-AOI-blue bg-[rgba(255,255,255,0.1)]'}`}
                      key={index}
                      onClick={() => {
                        handleSelectFeature(_.get(info, 'feature_id', null));
                        setSelectedLineItem(`${_.get(info, 'feature_id', null)}-${_.get(info, 'detail', null)}`);
                      }}
                    >
                      <Tooltip
                        placement='right'
                        title={<div className='flex flex-col items-start gap-1'>
                          <span className='font-source text-[12px] font-normal'>
                            {`${getFeatureTypeDisplayText(_.get(f, 'feature_scope') === 'global' ? _.get(f, 'feature_type') : _.get(f, 'feature_type').substring(1))}-${translation(`viewInspection.lineItem.${_.get(info, 'detail', null)}`)}-${translation(`viewInspection.${_.get(info, 'pass') ? 'lineItemGoodResult' : 'lineItemBadResult'}`)}`}
                          </span>
                          {_.get(info, 'feedback', {}) &&
                            <span className='font-source text-[12px] font-normal'>
                              {translation('viewInspection.currentFeedback')}{(_.get(info, 'feedback.correct') === false && !_.get(info, 'pass')) ? translation('viewInspection.lineItemGoodResult') : translation('viewInspection.badFeedback')}
                            </span>
                          }
                        </div>}
                      >
                        <div className='flex items-center gap-2 flex-1 justify-between px-2'>
                          <div className='flex gap-2 items-center'>
                            <div className='flex w-3 h-3 flex-col justify-center items-center'>
                              <img src='/img/icn/icn_warning_red.svg' className='w-4 h-[10.5px] shrink' alt='warning' />
                            </div>
                            <span className='font-source w-[140px] text-[12px] font-normal overflow-hidden flex-1 whitespace-nowrap text-ellipsis'>
                              {`${getFeatureTypeDisplayText(_.get(f, 'feature_scope') === 'global' ? _.get(f, 'feature_type') : _.get(f, 'feature_type').substring(1))}-${translation(`viewInspection.lineItem.${_.get(info, 'detail', null)}`)}-${translation(`viewInspection.${_.get(info, 'pass') ? 'lineItemGoodResult' : 'lineItemBadResult'}`)}`}
                            </span>
                          </div>
                          {!_.isEmpty(_.get(info, 'feedback', {})) &&
                            <div className='flex w-6 h-6 flex-col justify-center items-center'>
                              <img src='/img/icn/icn_pencil_blue.svg' alt='pencil' className='w-3 h-3' />
                            </div>
                          }
                        </div>
                      </Tooltip>
                    </div>
                  }
              })}
            </Fragment>
          }
        }) : []}
      />
    </div>
  );
};

export default InferenceDetail;