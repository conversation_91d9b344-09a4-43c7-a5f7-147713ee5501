import _ from 'lodash';
import ThreeDBaseViewer from './ThreeDBaseViewer';
import * as THREE from 'three';
import { disposePointCloud, generateSingleSpherePoint, getObjectFromScene } from './util';


export default class CameraPreviewViewer extends ThreeDBaseViewer {
  constructor(canvasRef, elementHeight, elementWidth, getCanvasHeightWidth) {
    super(canvasRef, elementHeight, elementWidth, getCanvasHeightWidth);
    this.scene = super.getScene();
    this.trackball = super.getTrackballControls();
    this.camera = super.getCamera();
    this.renderer = super.getRenderer();
    this.sceneCloudId = null;
    this.cloudCenter = null;
    this.curCustomCoordSystemPointIds = [];
  };

  loadScene = ({
    positions,
    colors,
  }) => {
    // start timer
    // console.log(Date.now().valueOf(), 'start load scene');
    const geometry = new THREE.BufferGeometry();
    geometry.setAttribute('position', new THREE.Float32BufferAttribute(positions, 3));
    geometry.setAttribute('color', new THREE.Uint8BufferAttribute(colors, 3));
    geometry.attributes.color.normalized = true;

    // get cloud position center
    const center = new THREE.Vector3();
    geometry.computeBoundingBox();
    geometry.boundingBox.getCenter(center);
    this.cloudCenter = center;

    const material = new THREE.ShaderMaterial({
      uniforms: {
        size: { value: 0.3 },
      },
    vertexShader: `
        uniform float size;
        varying vec3 vColor;
        
        void main() {
            vColor = color.rgb;
            vec4 mvPosition = modelViewMatrix * vec4(position, 1.0);
            gl_PointSize = size * (450.0 / -mvPosition.z);
            gl_Position = projectionMatrix * mvPosition;
        }
      `,
      fragmentShader: `
        varying vec3 vColor;
  
        void main() {
          gl_FragColor = vec4(vColor, 1.0);
        }
      `,
      transparent: true,
      vertexColors: true,
    });

    // const material = new THREE.PointsMaterial({ vertexColors: true });

    const cloud = new THREE.Points(geometry, material);
    this.scene.add(cloud);
    this.sceneCloudId = cloud.id;
    this.intersectedObjIds.push(cloud.id);

    this.trackball.target.set(center.x, center.y, center.z);
    this.getCamera().lookAt(center.x, center.y, center.z);
    // console.log(Date.now().valueOf(), 'end load scene');
  };

  addCustomCoordSystemPoint = (center, index) => {
    const { sphere } = generateSingleSpherePoint(center);
    this.scene.add(sphere);
    sphere.userData = { index };
    this.curCustomCoordSystemPointIds.push(sphere.id);
  };

  clearCustomCoordSystemPoints = () => {
    this.curCustomCoordSystemPointIds.forEach((id) => {
      const point = getObjectFromScene(this.scene, id);
      if (!_.isEmpty(point)) {
        this.scene.remove(point);
      }
    });
    this.curCustomCoordSystemPointIds = [];
  };

  clearCustomCoordSystemPointByIndex = (index) => {
    _.forEach(this.curCustomCoordSystemPointIds, (id) => {
      const point = getObjectFromScene(this.scene, id);
      if (!_.isEmpty(point) && point.userData.index === index) {
        this.scene.remove(point);
      }
    });
  };

  updateCamera = ({
    position,
    lookAt,
    up,
  }) => {
    this.getCamera().position.set(position.x, position.y, position.z);
    this.getCamera().lookAt(lookAt.x, lookAt.y, lookAt.z);
    this.getCamera().up.set(up.x, up.y, up.z);
  };

  resetCameraPosition = () => {
    this.getCamera().position.set(0, 0, 0);
    this.getCamera().up.set(0, -1, 0);
    if (this.cloudCenter) {
      this.getCamera().lookAt(this.cloudCenter);
    }
  }

  clearScene = () => {
    if (_.isInteger(this.sceneCloudId)) {
      disposePointCloud(this.scene, this.sceneCloudId);
      this.sceneCloudId = null;
    }
  };
};