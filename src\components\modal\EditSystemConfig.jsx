import { Button, Collapse, Modal } from 'antd';
import JSONEditor from 'jsoneditor';
import 'jsoneditor/dist/jsoneditor.css';
import React, { Fragment, useEffect, useRef, useState } from 'react';
import { handleRequestFailed, translation } from '../../common/util';
import { useLazyGetAllSystemConfigFilesQuery, useUpdateAllSystemConfigFilesMutation } from '../../services/system';
import styled from 'styled-components';
import _ from 'lodash';
import CustomExpand from '../common/CustomExpand';
import { ALERT_TYPES, aoiAlert } from '../../common/alert';
import { PrimaryButtonConfigProvider } from '../../common/darkModeComponents';
import UpdateSystemConfigRestartReminder from './UpdateSystemConfigRestartReminder';
import { useLazyDiscoverCamerasQuery } from '../../services/camera';
import { parse, parseAllDocuments, stringify } from 'yaml';


const EditSystemConfig = (props) => {
  const {
    isOpened,
    setIsOpened,
  } = props;

  const captureAgentContRef = useRef();
  const captureAgentEidtorRef = useRef();
  const sensorConfigContRef = useRef();
  const sensorConfigEditorRef = useRef();
  const componentDetectorContRef = useRef();
  const componentDetectorEditorRef = useRef();
  const defectConfigContRef = useRef();
  const defectConfigEditorRef = useRef();
  const depthDiffConfigContRef = useRef();
  const depthDiffConfigEditorRef = useRef();
  const inspectionConfigContRef = useRef();
  const inspectionConfigEditorRef = useRef();
  const systemConfigContRef = useRef();
  const systemConfigEditorRef = useRef();
  const plcConfigContRef = useRef();
  const plcConfigEditorRef = useRef();
  const fisConfigContRef = useRef();
  const fisConfigEditorRef = useRef();

  const [modalDimension, setModalDimension] = useState({ width: 0, height: 0 });
  const [json, setJson] = useState(null);
  const [isSystemRestartReminderOpened, setIsSystemRestartReminderOpened] = useState(false);
  const [discoveredCameraNames, setDiscoveredCameraNames] = useState([]);

  const [getAllSystemConfigFiles] = useLazyGetAllSystemConfigFilesQuery();
  const [updateAllSystemConfigFiles] = useUpdateAllSystemConfigFilesMutation();
  const [discoverCameras] = useLazyDiscoverCamerasQuery();

  const refreshDiscoveredCameras = async () => {
    const res = await discoverCameras();
    if (res.error) {
      handleRequestFailed('discoverCameras', res.error);
      return;
    }
    setDiscoveredCameraNames(res.data);
  };

  useEffect(() => {
    if (!isOpened) {
      if (captureAgentEidtorRef.current) captureAgentEidtorRef.current.destroy();
      if (sensorConfigEditorRef.current) sensorConfigEditorRef.current.destroy();
      if (componentDetectorEditorRef.current) componentDetectorEditorRef.current.destroy();
      if (defectConfigEditorRef.current) defectConfigEditorRef.current.destroy();
      if (depthDiffConfigEditorRef.current) depthDiffConfigEditorRef.current.destroy();
      if (inspectionConfigEditorRef.current) inspectionConfigEditorRef.current.destroy();
      if (systemConfigEditorRef.current) systemConfigEditorRef.current.destroy();
      return;
    }
    
    const fetchConfigFiles = async () => {
      const res = await getAllSystemConfigFiles();
      
      // const res = {
      //   data: testAllSystemConfig
      // };

      if (res.data) setJson(res.data);
    };

    fetchConfigFiles();

    return () => {
      if (captureAgentEidtorRef.current) captureAgentEidtorRef.current.destroy();
      if (sensorConfigEditorRef.current) sensorConfigEditorRef.current.destroy();
    };
  }, [isOpened]);

  useEffect(() => {
    const updateModalDimension = () => {
      setModalDimension({
        width: _.min([window.innerWidth - 200, 600]),
        height: window.innerHeight - 200,
      });
    };

    updateModalDimension();
    refreshDiscoveredCameras();

    window.addEventListener('resize', updateModalDimension);

    return () => {
      window.removeEventListener('resize', updateModalDimension);
    };
  }, []);

  return (
    <Fragment>
    <CustomModal
      destroyOnClose
      width={modalDimension.width}
      height={modalDimension.height}
      open={isOpened}
      onCancel={() => setIsOpened(false)}
      title={
        <span className='font-source text-[16px] font-semibold'>
          {translation('editSystemConfig.editSystemConfig')}
        </span>
      }
      footer={null}
    >
      <div
        className={`flex flex-col items-start gap-8 self-stretch px-4 py-6 overflow-y-auto`}
        style={{ height: `${modalDimension.height - 65 - 48}px` }}
      >
        <div className='flex gap-4 self-stretch flex-col'>
          <div className='flex items-center gap-2 self-stretch'>
            <span className='font-source text-[14px] font-semibold'>
              {translation('editSystemConfig.dicoveredCameras')}
            </span>
            <Button
              type='text'
              onClick={() => {
                refreshDiscoveredCameras();
              }}
            >
              <span className='font-source text-[12px] font-normal'>
                {translation('common.refresh')}
              </span>
            </Button>
          </div>
          <div className='flex flex-col gap-1'>
            {_.map(discoveredCameraNames, (cameraName, idx) => (
              <div key={idx} className='flex items-center self-stretch rounded-[4px] p-1'>
                <span className='font-source text-[12px] font-normal'>
                  {cameraName}
                </span>
              </div>
            ))}
          </div>
        </div>
        {/* capture agent starts */}
        <div className='flex gap-4 self-stretch flex-col'>
          <span className='font-source text-[14px] font-semibold'>
            {translation('editSystemConfig.captureAgent')}
          </span>
          <CustomExpand
            title={<span className='font-source text-[12px] font-normal'>
              {translation('editSystemConfig.captureAgentConfig')}
            </span>}
            onExpand={() => {
              setTimeout(() => {
                if (_.isUndefined(captureAgentContRef.current) || _.isEmpty(captureAgentContRef.current)) return;
                if (captureAgentEidtorRef.current) captureAgentEidtorRef.current.destroy();
                captureAgentEidtorRef.current = new JSONEditor(captureAgentContRef.current, {
                  onChangeJSON: (v) => {
                    setJson({
                      ...json,
                      "capture_agent/capture_agent_config.json": JSON.stringify(v),
                    });
                  },
                });
                try {
                  const curJson = JSON.parse(_.get(json, 'capture_agent/capture_agent_config.json', '{}'));
                  captureAgentEidtorRef.current.set(curJson);
                } catch (e) {
                  aoiAlert(translation('notification.error.failToParseThisJsonToObject'), ALERT_TYPES.COMMON_ERROR);
                  captureAgentEidtorRef.current.set({});
                }
              }, 300);
            }}
          >
            <div
              className='w-full h-[400px] border border-[#28587B] rounded-[6px]'
              ref={captureAgentContRef}
            />
          </CustomExpand>
          <CustomExpand
            title={<span className='font-source text-[12px] font-normal'>
              {translation('editSystemConfig.sensorConfig')}
            </span>}
            onExpand={() => {
              setTimeout(() => {
                if (_.isUndefined(sensorConfigContRef.current) || _.isEmpty(sensorConfigContRef.current)) return;
                if (sensorConfigEditorRef.current) sensorConfigEditorRef.current.destroy();
                sensorConfigEditorRef.current = new JSONEditor(sensorConfigContRef.current, {
                  onChangeJSON: (v) => {
                    setJson({
                      ...json,
                      "capture_agent/sensor_config.json": JSON.stringify(v),
                    });
                  },
                });
                try {
                  const curJson = JSON.parse(_.get(json, 'capture_agent/sensor_config.json', '{}'));
                  sensorConfigEditorRef.current.set(curJson);
                } catch (e) {
                  aoiAlert(translation('notification.error.failToParseThisJsonToObject'), ALERT_TYPES.COMMON_ERROR);
                  sensorConfigEditorRef.current.set({});
                }
              }, 300);
            }}
          >
            <div
              className='w-full h-[400px] border border-[#28587B] rounded-[6px]'
              ref={sensorConfigContRef}
            />
          </CustomExpand>
        </div>
        {/* capture agent ends */}

        {/* component detector starts */}
        <div className='flex gap-4 self-stretch flex-col'>
          <span className='font-source text-[14px] font-semibold'>
            {translation('editSystemConfig.componentDetector')}
          </span>
          <CustomExpand
            title={<span className='font-source text-[12px] font-normal'>
              {translation('editSystemConfig.componentDetectorConfig')}
            </span>}
            onExpand={() => {
              setTimeout(() => {
                if (_.isUndefined(componentDetectorContRef.current) || _.isEmpty(componentDetectorContRef.current)) return;
                if (componentDetectorEditorRef.current) componentDetectorEditorRef.current.destroy();
                componentDetectorEditorRef.current = new JSONEditor(componentDetectorContRef.current, {
                  onChangeJSON: (v) => {
                    setJson({
                      ...json,
                      "component_detector/component_detector_config.json": JSON.stringify(v),
                    });
                  },
                });
                try {
                  const curJson = JSON.parse(_.get(json, 'component_detector/component_detector_config.json', '{}'));
                  componentDetectorEditorRef.current.set(curJson);
                } catch (e) {
                  aoiAlert(translation('notification.error.failToParseThisJsonToObject'), ALERT_TYPES.COMMON_ERROR);
                  componentDetectorEditorRef.current.set({});
                }
              }, 300);
            }}
          >
            <div
              className='w-full h-[400px] border border-[#28587B] rounded-[6px]'
              ref={componentDetectorContRef}
            />
          </CustomExpand>
        </div>
        {/* component detector ends */}

        {/* inference agent starts */}
        <div className='flex gap-4 self-stretch flex-col'>
          <span className='font-source text-[14px] font-semibold'>
            {translation('editSystemConfig.inferenceAgent')}
          </span>
          <CustomExpand
            title={<span className='font-source text-[12px] font-normal'>
              {translation('editSystemConfig.defectConfig')}
            </span>}
            onExpand={() => {
              setTimeout(() => {
                if (_.isUndefined(defectConfigContRef.current) || _.isEmpty(defectConfigContRef.current)) return;
                if (defectConfigEditorRef.current) defectConfigEditorRef.current.destroy();
                defectConfigEditorRef.current = new JSONEditor(defectConfigContRef.current, {
                  onChangeJSON: (v) => {
                    setJson({
                      ...json,
                      "inference_agent/config/defect_config.json": JSON.stringify(v),
                    });
                  },
                });
                try {
                  const curJson = JSON.parse(_.get(json, 'inference_agent/config/defect_config.json', '{}'));
                  defectConfigEditorRef.current.set(curJson);
                } catch (e) {
                  aoiAlert(translation('notification.error.failToParseThisJsonToObject'), ALERT_TYPES.COMMON_ERROR);
                  defectConfigEditorRef.current.set({});
                }
              }, 300);
            }}
          >
            <div
              className='w-full h-[400px] border border-[#28587B] rounded-[6px]'
              ref={defectConfigContRef}
            />
          </CustomExpand>
          <CustomExpand
            title={<span className='font-source text-[12px] font-normal'>
              {translation('editSystemConfig.depthDiffConfig')}
            </span>}
            onExpand={() => {
              setTimeout(() => {
                if (_.isUndefined(depthDiffConfigContRef.current) || _.isEmpty(depthDiffConfigContRef.current)) return;
                if (depthDiffConfigEditorRef.current) depthDiffConfigEditorRef.current.destroy();
                depthDiffConfigEditorRef.current = new JSONEditor(depthDiffConfigContRef.current, {
                  onChangeJSON: (v) => {
                    setJson({
                      ...json,
                      "inference_agent/config/depth_diff.json": JSON.stringify(v),
                    });
                  },
                });
                try {
                  const curJson = JSON.parse(_.get(json, 'inference_agent/config/depth_diff.json', '{}'));
                  depthDiffConfigEditorRef.current.set(curJson);
                } catch (e) {
                  aoiAlert(translation('notification.error.failToParseThisJsonToObject'), ALERT_TYPES.COMMON_ERROR);
                  depthDiffConfigEditorRef.current.set({});
                }
              }, 300);
            }}
          >
            <div
              className='w-full h-[400px] border border-[#28587B] rounded-[6px]'
              ref={depthDiffConfigContRef}
            />
          </CustomExpand>
        </div>
        {/* inference agent ends */}

        {/* inspection config starts */}
        <div className='flex gap-4 self-stretch flex-col'>
          <span className='font-source text-[14px] font-semibold'>
            {translation('editSystemConfig.inspection')}
          </span>
          <CustomExpand
            title={<span className='font-source text-[12px] font-normal'>
              {translation('editSystemConfig.inspectionConfig')}
            </span>}
            onExpand={() => {
              setTimeout(() => {
                if (_.isUndefined(inspectionConfigContRef.current) || _.isEmpty(inspectionConfigContRef.current)) return;
                if (inspectionConfigEditorRef.current) inspectionConfigEditorRef.current.destroy();
                inspectionConfigEditorRef.current = new JSONEditor(inspectionConfigContRef.current, {
                  onChangeJSON: (v) => {
                    setJson({
                      ...json,
                      "inspection_config.json": JSON.stringify(v),
                    });
                  },
                });
                try {
                  const curJson = JSON.parse(_.get(json, 'inspection_config.json', '{}'));
                  inspectionConfigEditorRef.current.set(curJson);
                } catch (e) {
                  aoiAlert(translation('notification.error.failToParseThisJsonToObject'), ALERT_TYPES.COMMON_ERROR);
                  inspectionConfigEditorRef.current.set({});
                }
              }, 300);
            }}
          >
            <div
              className='w-full h-[400px] border border-[#28587B] rounded-[6px]'
              ref={inspectionConfigContRef}
            />
          </CustomExpand>
        </div>
        {/* inspection config ends */}

        {/* system config starts */}
        <div className='flex gap-4 self-stretch flex-col'>
          <span className='font-source text-[14px] font-semibold'>
            {translation('editSystemConfig.system')}
          </span>
          <CustomExpand
            title={<span className='font-source text-[12px] font-normal'>
              {translation('editSystemConfig.systemJsonConfig')}
            </span>}
            onExpand={() => {
              setTimeout(() => {
                if (_.isUndefined(systemConfigContRef.current) || _.isEmpty(systemConfigContRef.current)) return;
                if (systemConfigEditorRef.current) systemConfigEditorRef.current.destroy();
                systemConfigEditorRef.current = new JSONEditor(systemConfigContRef.current, {
                  onChangeJSON: (v) => {
                    setJson({
                      ...json,
                      "system_config.json": JSON.stringify(v),
                    });
                  },
                  onEditable: ({ path, field, value }) => {
                    return { [path]: false, [field]: false, [value]: true };
                  },
                });
                try {
                  const curJson = JSON.parse(_.get(json, 'system_config.json', '{}'));
                  systemConfigEditorRef.current.set(curJson);
                } catch (e) {
                  aoiAlert(translation('notification.error.failToParseThisJsonToObject'), ALERT_TYPES.COMMON_ERROR);
                  systemConfigEditorRef.current.set({});
                }
              }, 300);
            }}
          >
            <div
              className='w-full h-[400px] border border-[#28587B] rounded-[6px]'
              ref={systemConfigContRef}
            />
          </CustomExpand>
        </div>
        {/* system config ends */}

        {/* plc config starts */}
        <div className='flex gap-4 self-stretch flex-col'>
          <span className='font-source text-[14px] font-semibold'>
            {translation('editSystemConfig.plcConfig')}
          </span>
          <CustomExpand
            title={<span className='font-source text-[12px] font-normal'>
              {translation('editSystemConfig.plcConfig')}
            </span>}
            onExpand={() => {
              setTimeout(() => {
                if (_.isUndefined(plcConfigContRef.current) || _.isEmpty(plcConfigContRef.current)) return;
                if (plcConfigEditorRef.current) plcConfigEditorRef.current.destroy();
                plcConfigEditorRef.current = new JSONEditor(plcConfigContRef.current, {
                  onChangeJSON: (v) => {
                    let s = '';
                    for (const j of v) {
                      s = s.concat(`---\n${stringify(j)}...\n`);
                    }
                    setJson({
                      ...json,
                      "aoi_comm_config.yaml": s,
                    });
                  },
                  onEditable: ({ path, field, value }) => {
                    return { [path]: false, [field]: false, [value]: true };
                  },
                });
                try {
                  const parsedYaml = parseAllDocuments(_.get(json, 'aoi_comm_config.yaml', ''));
                  const docJson = _.map(parsedYaml, (doc) => doc.toJSON());
                  plcConfigEditorRef.current.set(docJson);
                } catch (e) {
                  console.error(e);
                  aoiAlert(translation('notification.error.failToParseThisYamlToObject'), ALERT_TYPES.COMMON_ERROR);
                  plcConfigEditorRef.current.set({});
                }
              }, 300);
            }}
          >
            <div
              className='w-full h-[400px] border border-[#28587B] rounded-[6px]'
              ref={plcConfigContRef}
            />
          </CustomExpand>
        </div>
        {/* plc config ends */}

        {/* fis config starts */}
        <div className='flex gap-4 self-stretch flex-col'>
          <span className='font-source text-[14px] font-semibold'>
            {translation('editSystemConfig.fisConfig')}
          </span>
          <CustomExpand
            title={<span className='font-source text-[12px] font-normal'>
              {translation('editSystemConfig.fisConfig')}
            </span>}
            onExpand={() => {
              setTimeout(() => {
                if (_.isUndefined(fisConfigContRef.current) || _.isEmpty(fisConfigContRef.current)) return;
                if (fisConfigEditorRef.current) fisConfigEditorRef.current.destroy();
                fisConfigEditorRef.current = new JSONEditor(fisConfigContRef.current, {
                  onChangeJSON: (v) => {
                    let s = '';
                    for (const j of v) {
                      s = s.concat(`---\n${stringify(j)}...\n`);
                    }
                    setJson({
                      ...json,
                      "fis_comm_config.yaml": s,
                    });
                  },
                  onEditable: ({ path, field, value }) => {
                    return { [path]: false, [field]: false, [value]: true };
                  },
                });
                try {
                  const parsedYaml = parseAllDocuments(_.get(json, 'fis_comm_config.yaml', ''));
                  const docJson = _.map(parsedYaml, (doc) => doc.toJSON());
                  fisConfigEditorRef.current.set(docJson);
                } catch (e) {
                  console.error(e);
                  aoiAlert(translation('notification.error.failToParseThisYamlToObject'), ALERT_TYPES.COMMON_ERROR);
                  fisConfigEditorRef.current.set({});
                }
              }, 300);
            }}
          >
            <div
              className='w-full h-[400px] border border-[#28587B] rounded-[6px]'
              ref={fisConfigContRef}
            />
          </CustomExpand>
        </div>
        {/* fis config ends */}
      </div>
      <div className='flex items-center gap-2 py-2'>
        <Button
          style={{ width: '50%' }}
          onClick={() => {
            setIsOpened(false);
          }}
        >
          <span className='font-source text-[12px] font-normal'>
            {translation('common.cancel')}
          </span>
        </Button>
        <PrimaryButtonConfigProvider>
          <Button
            style={{ width: '50%' }}
            onClick={() => setIsSystemRestartReminderOpened(true)}
          >
            <span className='font-source text-[12px] font-normal'>
              {translation('common.save')}
            </span>
          </Button>
        </PrimaryButtonConfigProvider>
      </div>
    </CustomModal>
    <UpdateSystemConfigRestartReminder
      isOpened={isSystemRestartReminderOpened}
      setIsOpened={setIsSystemRestartReminderOpened}
      handleSubmit={() => {
        const update = async (json) => {
          const res = await updateAllSystemConfigFiles(json);
          if (res.error) {
            aoiAlert(translation('notification.error.failToUpdateAllSystemConfigFiles'), ALERT_TYPES.COMMON_ERROR);
            return;
          }
          aoiAlert(translation('notification.success.systemConfigFilesUpdated'), ALERT_TYPES.COMMON_SUCCESS);
          return;
        };

        // validate json before update
        try {
          JSON.parse(JSON.stringify(json));
        } catch (e) {
          aoiAlert(translation('notification.error.failToParseThisJsonToObject'), ALERT_TYPES.COMMON_ERROR);
          return;
        }
        update(json);
      }}
    />
    </Fragment>
  )
};

const CustomModal = styled(Modal)`
  .ant-modal-content {
    width: ${({ width }) => `${width}px`};
    height: ${({ height }) => `${height}px`};
  }
  .ant-modal-body {
    width: 100%;
    height: 100%;
    padding-bottom: 30px;
  }
`;

export default EditSystemConfig;