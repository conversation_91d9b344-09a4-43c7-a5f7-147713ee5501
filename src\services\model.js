import { createApi } from '@reduxjs/toolkit/query/react';
import { baseQuery } from './api';


export const modelApi = createApi({
  reducerPath: 'modelApi',
  baseQuery,
  tagTypes: ['Model'],
  endpoints: (build) => ({
    modelUpdateTrigger: build.mutation({
      query: (body) => ({
        url: '/modelUpdateTrigger',
        method: 'POST',
        body,
      }),
    }),
  }),
});

export const {
  useModelUpdateTriggerMutation,
} = modelApi;