import React from 'react';
import { activeGreen, DarkButton, DarkModal, defaultGreen } from '../../common/darkModeComponents';
import { translation } from '../../common/util';


const CameraPreviewInitCapture = (props) => {
  const {
    isOpened,
    setIsOpened,
    handleCaptureAll,
    handleBackToHome,
  } = props;

  return (
    <DarkModal
      primaryColor={'#57F2C4'}
      open={isOpened}
      // onCancel={() => setIsOpened(false)}
      title={
        <span className='font-source text-[16px] font-semibold'>
          {translation('imageCaptureRequired.userActionRequired')}
        </span>
      }
      closable={false}
      footer={
        <div className='flex flex-col items-start self-stretch gap-2'>
          <DarkButton
            bgColor={defaultGreen}
            hoverBgColor={activeGreen}
            borderColor={defaultGreen}
            hoverTextColor={'#333'}
            hoverBorderColor={activeGreen}
            textColor={'#333'}
            style={{ width: '100%' }}
            onClick={() => {
              handleCaptureAll();
              setIsOpened(false);
            }}
          >
            {translation('common.continue')}
          </DarkButton>
          <DarkButton
            bgColor={defaultGreen}
            hoverBgColor={activeGreen}
            borderColor={defaultGreen}
            hoverTextColor={'#333'}
            hoverBorderColor={activeGreen}
            textColor={'#333'}
            style={{ width: '100%' }}
            onClick={() => {
              // handleBackToHome();
              setIsOpened(false);
            }}
          >
            {translation('common.cancel')}
          </DarkButton>
        </div>
      }
    >
      <div className='flex py-6 px-4 flex-col items-start gap-8 self-stretch'>
        <span className='text-source text-[14px] font-normal'>
          {translation('cameraPreview.positionTheNextPart')}
        </span>
      </div>
    </DarkModal>
  )
};

export default CameraPreviewInitCapture;