import { Button, Select } from 'antd';
import React, { useEffect, useState } from 'react';
import { DarkButton, DarkInput, DarkInputNumber, DarkModal, PrimaryButtonConfigProvider } from '../../common/darkModeComponents';
import { handleRequestFailed, translation } from '../../common/util';
import { useAddProductMutation, useLazyGetInferenceStatusQuery, useLazyGetProductByIdQuery, useRunDetectionPipelineMutation } from '../../services/product';
import _ from 'lodash';
import { ALERT_TYPES, aoiAlert } from '../../common/alert';
import { inputRegularExpression, serverEndpoint } from '../../common/const';


const NewBoardModal = (props) => {
  const { isOpened, setIsOpened, handleRedirect } = props;

  const [boardName, setBoardName] = useState('');
  const [boardLength, setBoardLength] = useState(null);
  const [boardWidth, setBoardWidth] = useState(null);
  const [boardModel, setBoardModel] = useState('');
  const [description, setDescription] = useState('');
  // const [aiModel, setAiModel] = useState('');

  const [createGoldenProduct, { isLoading, isError, error }] = useAddProductMutation();
  const [lazyGetProductById] = useLazyGetProductByIdQuery();
  const [getInferenceStatus] = useLazyGetInferenceStatusQuery();
  const [registerProduct] = useRunDetectionPipelineMutation();

  useEffect(() => {
    if (!isError) return;
    handleRequestFailed('createGoldenBoard', error);
  }, [isError, error]);

  return (
    <DarkModal
      open={isOpened}
      onCancel={() => setIsOpened(false)}
    title={
      <span className='font-source text-[16px] font-semibold'>
        {translation('newBoard.newBoard')}
      </span>
    }
    footer={
      <div className='flex p-2 items-start gap-2 self-stretch justify-center'>
        <Button
          style={{ width: '50%' }}
          onClick={() => setIsOpened(false)}
        >
          <span className='font-source text-[12px] font-semibold'>
            {translation('common.cancel')}
          </span>
        </Button>
        <PrimaryButtonConfigProvider>
          <Button
            style={{ width: '50%' }}
            loading={isLoading}
            onClick={async () => {
              const inferenceStatusRes = await getInferenceStatus();
              if (_.get(inferenceStatusRes, 'error')) {
                handleRequestFailed('getInferenceStatus', inferenceStatusRes.error);
                return;
              }
              if (_.get(inferenceStatusRes, 'data.status') !== 'stopped') {
                aoiAlert(translation('notification.error.stopInferenceFirst'), ALERT_TYPES.COMMON_ERROR);
                return;
              }
              if (_.isEmpty(boardName)) {
                aoiAlert(translation('notification.error.enterAProductName'), ALERT_TYPES.COMMON_ERROR);
                return;
              }
              if (!inputRegularExpression.addProduct.productName.test(boardName)) {
                aoiAlert(translation('notification.error.invalidProductName'), ALERT_TYPES.COMMON_ERROR);
                return;
              }
              // console.log('encodedBoardName', encodedBoardName);
              // manually encode boardName
              // const encodedBoardName = encodeURIComponent(boardName);
              // // manually call post req
              // let res;
              // try {
              //   res = await fetch(`${serverEndpoint}/addProduct?product_name=${encodedBoardName}`, {
              //     method: 'POST',
              //     headers: {
              //       'Content-Type': 'application/x-www-form-urlencoded',
              //     },
              //   });
              // } catch (error) {
              //   handleRequestFailed('createGoldenBoard', error);
              //   return;
              // }
              // const { product_id } = await res.json();

              const res = await createGoldenProduct(boardName);
              if (res.error) {
                handleRequestFailed('createGoldenBoard', res.error);
                return;
              }
              const { product_id } = res.data;

              setIsOpened(false);
              // handleRedirect('/aoi/view-product?isAddProductTriggered=true&newProductId=' + product_id);
              // handleRedirect(`/aoi/camera-preview/${product_id}`);
              handleRedirect(`/aoi/edit-product/${product_id}?editStep=0`);
            }}
          >
            <span className='font-source text-[12px] font-semibold'>
              {translation('common.saveAndCaptureGoldenBoard')}
            </span>
          </Button>
        </PrimaryButtonConfigProvider>
      </div>
    }
    >
      <div className='flex py-6 px-4 flex-col gap-8 items-start self-stretch'>
        <div className='flex flex-col gap-2 items-start self-stretch'>
          <span className='font-source text-[12px] font-normal'>
            {translation('newBoard.pleaseSpecifyTheFollowing')}
          </span>
          <div className='flex flex-col gap-2 items-start self-stretch'>
            <DarkInput
              placeholder={translation('newBoard.boardName')}
              value={boardName}
              onChange={(e) => setBoardName(e.target.value)}
            />
            <div className='flex gap-2 items-center self-stretch'>
              {/* <DarkInputNumber
                style={{ width: '50%' }}
                placeholder={translation('newBoard.boardLength')}
                value={boardLength}
                onChange={(e) => setBoardLength(e)}
                controls={false}
              />
              <DarkInputNumber
                style={{ width: '50%' }}
                placeholder={translation('newBoard.boardWidth')}
                value={boardWidth}
                onChange={(e) => setBoardWidth(e)}
                controls={false}
              /> */}
            </div>
            {/* <DarkInput
              placeholder={translation('newBoard.boardModel')}
              value={boardModel}
              onChange={(e) => setBoardModel(e.target.value)}
            />
            <DarkInput
              placeholder={translation('newBoard.description')}
              value={description}
              onChange={(e) => setDescription(e.target.value)}
            /> */}
          </div>
        </div>
        <div className='flex flex-col gap-2 items-start self-stretch'>
          {/* <span className='font-source text-[12px] font-semibold'>
            {translation('newBoard.aiModel')}
          </span>
          <Select
            style={{ width: '100%' }}
            // value={aiModel}
            // onChange={(value) => setAiModel(value)}
            options={[
              { value: '1', label: '1' },
              { value: '2', label: '2' },
              { value: '3', label: '3' },
            ]}
          /> */}
        </div>
      </div>
    </DarkModal>
  );
};

export default NewBoardModal;