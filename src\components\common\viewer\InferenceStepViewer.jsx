import { fabric } from 'fabric-with-erasing';
import _ from 'lodash';
import React from 'react';
import { baseBboxStrokeWidth, serverEndpoint } from '../../../common/const';
import { debounce, sleep, translation } from '../../../common/util';
import TwoDBaseViwer from './TwoDBaseViewer';
import { ALERT_TYPES, aoiAlert } from '../../../common/alert';


export default class InferenceStepViewer extends TwoDBaseViwer {
  constructor(props) {
    super(props, props.displayCanvasRef);
    this.displayCanvasRef = props.displayCanvasRef;
    this.scene = null; // 2d image
    this.fabricCanvas = null;
    this.sceneOriginalWidth = 0;
    this.sceneOriginalHeight = 0;
    this.curMode = 'select'; // select
    this.curRect = null; // current drawing rect
    this.rects = []; // all drawing rects
    this.sceneLocked = false;
    this.containerRef = React.createRef();
    this.handleWindowResize = React.createRef();
    this.isFullScreen = props.isFullScreen;
  };

  updateCanvasSize = () => {
    if (!this.containerRef.current || !this.fabricCanvas) return;
    const canvasWidth = this.containerRef.current.offsetWidth - 2;
    const canvasHeight = this.containerRef.current.offsetHeight - 2;
    this.fabricCanvas.setWidth(canvasWidth);
    this.fabricCanvas.setHeight(canvasHeight);
    this.fabricCanvas.renderAll();
  };

  componentDidMount() {
    if (!this.displayCanvasRef.current) return;

    // init fabric canvas
    const fabricCanvas = new fabric.Canvas(
      this.displayCanvasRef.current,
      {
        // Disable uniform scaling
        uniformScaling: false,
      }
    );
    this.fabricCanvas = fabricCanvas;
    fabricCanvas.skipOffscreen = true;
    fabricCanvas.contextContainer.imageSmoothingEnabled = true;

    // enable webgl filter
    let filterBackend = null;
    try {
      filterBackend = new fabric.WebglFilterBackend();
      console.log('Use WebGL filter backend');
    } catch (e) {
      console.error('WebGL backend is not supported, using 2d canvas backend');
      filterBackend = new fabric.Canvas2dFilterBackend();
    }
    fabricCanvas.filterBackend = filterBackend;

    if (fabric.isWebglSupported()) {
      console.log('WebGL is supported, increase texture size to 65536');
      fabric.textureSize = 65536; // ow only partial image will be rendered if sharpness is enabled since our image is large
    }

    // fabricCanvas.filterBackend = new fabric.Canvas2dFilterBackend();
    // fabric.textureSize = 65536; // ow only partial image will be rendered if sharpness is enabled since our image is large

    // Enable Panning
    let isPanning = false;
    fabricCanvas.on('mouse:down', (opt) => {
      if (this.sceneLocked) return;

      if (opt.target) {
        isPanning = false;
        return;
      }
      
      isPanning = true;
      fabricCanvas.selection = false;
      fabricCanvas.setCursor('grab');
    });

    fabricCanvas.on('mouse:move', (opt) => {
      if (this.sceneLocked) return;
      if (isPanning && opt && opt.e) {
        fabricCanvas.setCursor('grab');
        const delta = new fabric.Point(opt.e.movementX, opt.e.movementY);
        fabricCanvas.relativePan(delta);
      }
    });

    fabricCanvas.on('mouse:up', (opt) => {
      if (this.sceneLocked) return;

      isPanning = false;
      fabricCanvas.setCursor('default');
    });

    // Enable Zooming
    fabricCanvas.on('mouse:wheel', (opt) => {
      const delta = opt.e.deltaY;
      let zoom = fabricCanvas.getZoom();
      zoom *= 0.999 ** delta;
      // if (zoom > 20) zoom = 20;
      // if (zoom < 0.01) zoom = 0.01;
      fabricCanvas.zoomToPoint({ x: opt.e.offsetX, y: opt.e.offsetY }, zoom);
      opt.e.preventDefault();
      opt.e.stopPropagation();

      if (this.rects.length === 0) return;

      const newWidth = Math.min(baseBboxStrokeWidth, baseBboxStrokeWidth / zoom);
      // update top, left, width, height
      // add stroke width delta to the top left width height
      const strokeWidthDelta = newWidth - this.rects[0].strokeWidth;

      if (strokeWidthDelta === 0) return;

      // update the rect stroke width
      this.rects.forEach((rect) => {
        rect.set({
          strokeWidth: newWidth,
          left: rect.left - strokeWidthDelta,
          top: rect.top - strokeWidthDelta,
          width: rect.width + strokeWidthDelta,
          height: rect.height + strokeWidthDelta,
        });
      });
    });

    this.handleWindowResize.current = debounce(this.updateCanvasSize, 300);

    window.addEventListener('resize', this.handleWindowResize.current);
  }

  componentWillUnmount() {
    // console.log('InferenceStepViewer unmounted');
    if (this.handleWindowResize.current) window.removeEventListener('resize', this.handleWindowResize.current);
  }

  componentDidUpdate(prevProps) {
    if (prevProps.isFullScreen !== this.props.isFullScreen) {
      this.rects.forEach((rect) => {
        rect.set('evented', this.props.isFullScreen ? false : true);
        rect.set('selectable', this.props.isFullScreen ? false : true);
      });
      this.fabricCanvas.renderAll();
    }

    if (prevProps.selectedFeatureId !== this.props.selectedFeatureId) {
      if (_.isNull(this.props.selectedFeatureId)) {
        this.rects.forEach((rect) => {
          // rect.set('stroke', getColorByStr(_.get(rect, 'featureType')));
          rect.set('stroke', rect.get('pass') ? '#81F499' : '#EB5757');
        });
      } else {
        // main purpose is to wait for the layout to be updated
        // ow the right boundary will be pushed to the left
        // 2024/10/08 disable auto zoom to the selected feature for now
        // if (_.isNull(prevProps.selectedFeatureId)) {
        //   this.asyncPanZoomToFeature(this.props.selectedFeatureId);
        // } else {
        //   this.panZoomToFeature(this.props.selectedFeatureId);
        // }

        // const canvasWidth = this.fabricCanvas.getWidth();
        // const canvasHeight = this.fabricCanvas.getHeight();

        // this.panZoomToFeature(this.props.selectedFeatureId);
        // const rect = _.find(this.rects, (r) => String(r.get('featureId')) === String(this.props.selectedFeatureId));
        // let zoom = Math.max(
        //   canvasWidth/ rect.width,
        //   canvasHeight / rect.height,
        // );
        // // zoom = Math.min(zoom, 5);

        // // zoom and pan to the selected feature
        // const rectCenter = new fabric.Point(
        //   rect.left + rect.width / 2,
        //   rect.top + rect.height / 2
        // );

        // this.fabricCanvas.zoomToPoint(rectCenter, zoom);

        // const newRectCenter = fabric.util.transformPoint(rectCenter, this.fabricCanvas.viewportTransform);

        // // Calculate the pan adjustment to center the cropped area
        // const panX = (canvasWidth / 2 - newRectCenter.x) + this.fabricCanvas.viewportTransform[4];
        // const panY = (canvasHeight / 2 - newRectCenter.y) + this.fabricCanvas.viewportTransform[5];

        // // Apply the pan adjustment
        // this.fabricCanvas.viewportTransform = [zoom, 0, 0, zoom, panX, panY];

        // const newWidth = Math.min(baseBboxStrokeWidth, baseBboxStrokeWidth / zoom);
        // const strokeWidthDelta = newWidth - this.rects[0].strokeWidth;

        this.rects.forEach((rect) => {
          // rect.set('stroke', String(rect.get('featureId')) === String(this.props.selectedFeatureId) ? '#56CCF2' : getColorByStr(_.get(rect, 'featureType')));
          rect.set('stroke', String(rect.get('featureId')) === String(this.props.selectedFeatureId) ? '#e2e61c' : rect.get('pass') ? '#81F499' : '#EB5757');

          // rect.set({
          //   strokeWidth: newWidth,
          //   left: rect.left - strokeWidthDelta,
          //   top: rect.top - strokeWidthDelta,
          //   width: rect.width + strokeWidthDelta,
          //   height: rect.height + strokeWidthDelta,
          // });
          // rect.setCoords();
        });

        // Re-render the canvas
        this.fabricCanvas.requestRenderAll();
      }
    }
    
    if (prevProps.curDisplayOptionsBrightness !== this.props.curDisplayOptionsBrightness) this.updateSceneBrightness(this.props.curDisplayOptionsBrightness);
    if (prevProps.curDisplayOptionsContrast !== this.props.curDisplayOptionsContrast) this.updateSceneContrast(this.props.curDisplayOptionsContrast);
    if (prevProps.curDisplayOptionsSaturation !== this.props.curDisplayOptionsSaturation) this.updateSceneSaturation(this.props.curDisplayOptionsSaturation);
    if (prevProps.isSharpnessEnabled !== this.props.isSharpnessEnabled) this.updateSceneSharpness(this.props.isSharpnessEnabled);

    if (
      prevProps.selectedLineItem !== this.props.selectedLineItem ||
      prevProps.selectedFeatureId !== this.props.selectedFeatureId
    ) {
      this.updateCanvasSize();
    }

    if (!_.isEmpty(this.props.curIpc)) {
      const productFeatureBbox = _.find(this.rects, (r) => String(r.get('featureType')) === String('product'));
      if (_.isEmpty(productFeatureBbox)) return;
      // update the storke color based on if the product passed or not
      productFeatureBbox.set('stroke', _.get(this.props.curIpc, 'defect_count') === 0 ? '#81F499' : '#EB5757');
    }

    if (this.fabricCanvas) this.fabricCanvas.renderAll();
  }

  asyncPanZoomToFeature = async (featureId) => {
    await sleep(500);
    this.panZoomToFeature(featureId);
  };

  updateSceneBrightness = (brightness) => {
    if (this.scene) {
      // brightness is a value between 0 to 100 so convert to -1 to 1
      if (_.isNumber(_.get(this.scene, 'filters[0]'))) {
        this.scene.filters[0].brightness = (brightness - 50) / 50;
        this.scene.applyFilters();
      } else {
        this.scene.filters = [new fabric.Image.filters.Brightness({ brightness: (brightness - 50) / 50 })];
        this.scene.applyFilters();
      }
      this.fabricCanvas.renderAll();
    }
  };

  updateSceneContrast = (contrast) => {
    if (this.scene) {
      // contrast is a value between 0 to 100 so convert to -1 to 1
      if (_.isNumber(_.get(this.scene, 'filters[1]'))) {
        this.scene.filters[1].contrast = (contrast - 50) / 50;
        this.scene.applyFilters();
      } else {
        this.scene.filters = [new fabric.Image.filters.Brightness({ brightness: 0 }), new fabric.Image.filters.Contrast({ contrast: (contrast - 50) / 50 })];
        // this.scene.filters = [new fabric.Image.filters.Contrast({ contrast: (contrast - 50) / 50 })];
        this.scene.applyFilters();
      }
      this.fabricCanvas.renderAll();
    }
  };

  updateSceneSaturation = (saturation) => {
    if (this.scene) {
      // saturation is a value between 0 to 100 so convert to -1 to 1
      if (_.isNumber(_.get(this.scene, 'filters[2]'))) {
        this.scene.filters[2].saturation = (saturation - 50) / 50;
        this.scene.applyFilters();
      } else {
        this.scene.filters = [new fabric.Image.filters.Brightness({ brightness: 0 }), new fabric.Image.filters.Contrast({ contrast: 0 }), new fabric.Image.filters.Saturation({ saturation: (saturation - 50) / 50 })];
        // this.scene.filters = [new fabric.Image.filters.Saturation({ saturation: (saturation - 50) / 50 })];
        this.scene.applyFilters();
      }
      this.fabricCanvas.renderAll();
    }
  };

  updateSceneSharpness = (enabled) => {
    if (this.scene) {
      // remove previous sharpness filter
      this.scene.filters = this.scene.filters.filter((filter) => !(filter instanceof fabric.Image.filters.Convolute));
      if (enabled) {
        this.scene.filters.push(new fabric.Image.filters.Convolute({
          matrix: [ 0, -1, 0, -1, 5, -1, 0, -1, 0 ]
        }));
        
      }
      this.scene.applyFilters();
      this.fabricCanvas.renderAll();
    }
  };

  loadSceneAndFeatures = async (dataUri, features, callback) => {
    let res;
    try {
      res = await fetch(`${serverEndpoint}/data?data_uri=${dataUri}`);
    } catch (e) {
      console.error(e);
    }
    const blob = await res.blob();
    const reader = new FileReader();
    reader.readAsDataURL(blob);
    // reader.onload = (event) => {
    //   this.loadScene(
    //     event.target.result,
    //     this.containerRef.current.offsetWidth - 2,
    //     this.containerRef.current.offsetHeight - 2,
    //     features,
    //   );
    // };
    const dataUrl = await new Promise((resolve) => {
      reader.onload = (event) => resolve(event.target.result);
    });
    this.loadScene(
      dataUrl,
      this.containerRef.current.offsetWidth - 2,
      this.containerRef.current.offsetHeight - 2,
      features,
      callback,
    );
  };

  loadScene = (dataUrl, canvasWidth, canvasHeight, features, callback) => {
    if (!this.fabricCanvas || !this.displayCanvasRef.current) return;

    fabric.Image.fromURL(dataUrl, (img) => {
      img.set({
        selectable: false,
        evented: false,
      })
      this.sceneOriginalWidth = img.width;
      this.sceneOriginalHeight = img.height;
      const { fabricCanvas } = this;
      fabricCanvas.setWidth(canvasWidth);
      fabricCanvas.setHeight(canvasHeight);
      // img.scaleToHeight(canvasHeight);
      // img.scaleToWidth(canvasWidth);

      // if previous scene exists move the new scene to bottom
      // and the new scene will be on top
      if (this.scene) {
        this.scene.moveTo(0);
      }

      fabricCanvas.add(img);

      // now remove the previous scene to avoid flashing effect
      if (this.scene) {
        fabricCanvas.remove(this.scene);
        this.scene = null;
      }

      this.scene = img;

      // if (_.isNumber(this.props.curDisplayOptionsBrightness)) this.updateSceneBrightness(this.props.curDisplayOptionsBrightness);
      // if (_.isNumber(this.props.curDisplayOptionsContrast)) this.updateSceneContrast(this.props.curDisplayOptionsContrast);
      // if (_.isNumber(this.props.curDisplayOptionsSaturation)) this.updateSceneSaturation(this.props.curDisplayOptionsSaturation);

      this.scene.filters = [
        new fabric.Image.filters.Brightness({ brightness: _.isNumber(this.props.curDisplayOptionsBrightness) ? (this.props.curDisplayOptionsBrightness - 50) / 50 : 0 }),
        new fabric.Image.filters.Contrast({ contrast: _.isNumber(this.props.curDisplayOptionsContrast) ? (this.props.curDisplayOptionsContrast - 50) / 50 : 0 }),
        new fabric.Image.filters.Saturation({ saturation: _.isNumber(this.props.curDisplayOptionsSaturation) ? (this.props.curDisplayOptionsSaturation - 50) / 50 : 0 }),
      ];

      if (this.props.isSharpnessEnabled) {
        this.scene.filters.push(
          new fabric.Image.filters.Convolute({
            matrix: [0, -1, 0, -1, 5, -1, 0, -1, 0],
          })
        );
      }

      // console.log('apply filter start', Date.now());

      this.scene.applyFilters();

      // console.log('apply filter end', Date.now());

      if (!_.isEmpty(features)) {
        this.loadDefects(features);
      }

      this.resetView();
      this.updateSceneZIndex();

      // if product feature type exists then pan zoom to the product feature type
      const productFeature = _.find(this.rects, (r) => String(r.get('featureType')) === String('product'));
      if (productFeature) {
        this.panZoomToFeature(productFeature.get('featureId'));
      }

      callback && callback();
    });
  };

  // should be loadFeatures now we load all inference results(g and ng)
  loadDefects = (features) => {
    if (!this.fabricCanvas || _.isEmpty(features)) return;

    const {
      setSelectedFeatureId,
      setSelectedLineItem,
      sessionStepInfo,
    } = this.props;

    // dispose previous rects
    this.rects.forEach((rect) => {
      this.fabricCanvas.remove(rect);
    });
    this.rects = [];

    let zoom = this.fabricCanvas.getZoom();
    zoom *= 0.999 ** 0;
    // if (zoom > 20) zoom = 20;
    // if (zoom < 0.01) zoom = 0.01;
    const newStrokeWidth = Math.min(baseBboxStrokeWidth, baseBboxStrokeWidth / zoom);
    // const strokeWidthDelta = newStrokeWidth - baseBboxStrokeWidth;

    features.forEach((f) => {
      const { feature, pass } = f;
      const pMin = _.get(feature, 'roi.points[0]');
      const pMax = _.get(feature, 'roi.points[1]');
      // this position is based on the original image size
      // hence we need to scale it to the current scene size
      let newLeft = 0;
      let newTop = 0;
      let newWidth = 0;
      let newHeight = 0;
      if (_.isNumber(this.sceneOriginalHeight) && _.isNumber(this.sceneOriginalWidth) && this.scene) {
        newLeft = pMin.x * this.scene.scaleX;
        newTop = pMin.y * this.scene.scaleY;
        newWidth = (pMax.x - pMin.x) * this.scene.scaleX;
        newHeight = (pMax.y - pMin.y) * this.scene.scaleY;
      } else {
        newLeft = pMin.x;
        newTop = pMin.y;
        newWidth = pMax.x - pMin.x;
        newHeight = pMax.y - pMin.y;
      }

      // since we are using width 5 and backend will include the pMax point so...
      newLeft -= baseBboxStrokeWidth;
      newTop -= baseBboxStrokeWidth;
      newWidth += baseBboxStrokeWidth + 1;
      newHeight += baseBboxStrokeWidth + 1;

      // if (strokeWidthDelta > 0) {
      //   newLeft -= strokeWidthDelta;
      //   newTop -= strokeWidthDelta;
      //   newWidth += strokeWidthDelta;
      //   newHeight += strokeWidthDelta;
      // }

      let color;
      if (!_.isEmpty(this.props.curIpc) && _.get(feature, 'feature_type') === 'product') {
        color = _.get(this.props.curIpc, 'defect_count') === 0 ? '#81F499' : '#EB5757';
      } else {
        color = pass ? '#81F499' : '#EB5757';
      }

      const rect = new fabric.Rect({
        left: newLeft,
        top: newTop,
        width: newWidth,
        height: newHeight,
        fill: 'transparent',
        // stroke: getColorByStr(_.get(feature, 'feature_type')),
        stroke: color,
        strokeWidth: newStrokeWidth,
        selectable: false,
        strokeUniform: true, // Ensure stroke width remains consistent when scaling
        hoverCursor: 'pointer',
        evented: this.isFullScreen ? false : true,
        selectable: this.isFullScreen ? false : true,
      });
      rect.perPixelTargetFind = true;
      rect.targetFindTolerance = 10;
      rect.set('featureId', _.get(feature, 'feature_id'));
      rect.set('featureType', _.get(feature, 'feature_type'));
      rect.set('pass', pass);
      rect.setCoords();
      this.fabricCanvas.add(rect);
      this.rects.push(rect);
      rect.on('mousedown', () => {
        if (!_.find(sessionStepInfo, (s) => String(s.feature_id) === String(rect.get('featureId')))) {
          aoiAlert(translation('notification.error.canNotFindAnyInferenceResultForThisComponent'), ALERT_TYPES.COMMON_ERROR);
          return;
        }
        setSelectedFeatureId(rect.get('featureId'));
        setSelectedLineItem(`${rect.get('featureId')}-${
          _.get(
            _.find(sessionStepInfo, (item) => item.feature_id === Number(rect.get('featureId'))),
            'detail',
            null
          )
        }`);
      });
    });
    this.updateSceneZIndex();
  };

  updateSceneZIndex = () => {
    if (this.scene) {
      this.scene.moveTo(1);
    }
    this.rects.forEach((rect, index) => {
      rect.moveTo(2);
    });
    
  };

  lockScene = () => this.sceneLocked = true;

  unlockScene = () => this.sceneLocked = false;

  clearScene = () => {
    if (this.fabricCanvas) {
      this.fabricCanvas.clear();
    }
    this.scene = null;
    this.rect = [];
  };

  updateSceneSize = (width, height) => {
    if (this.fabricCanvas) {
      this.fabricCanvas.setWidth(width);
      this.fabricCanvas.setHeight(height);
    }
  };

  getSceneSize = () => {
    return {
      width: this.fabricCanvas ? this.fabricCanvas.getWidth() : 0,
      height: this.fabricCanvas ? this.fabricCanvas.getHeight() : 0,
    };
  };

  panZoomToFeature = (featureId) => {
    const canvasWidth = this.fabricCanvas.getWidth();
    const canvasHeight = this.fabricCanvas.getHeight();
    const rect = _.find(this.rects, (r) => String(r.get('featureId')) === String(featureId));
    let zoom = Math.min(
      canvasWidth/ rect.width,
      canvasHeight / rect.height,
    );
    // leave 2 pixel margin
    zoom -= 2 / Math.min(rect.width, rect.height);
    // zoom -= zoom * 0.1;
    // zoom = Math.min(zoom, 5);

    // zoom and pan to the selected feature
    const rectCenter = new fabric.Point(
      rect.left + rect.width / 2,
      rect.top + rect.height / 2
    );

    this.fabricCanvas.zoomToPoint(rectCenter, zoom);

    const newRectCenter = fabric.util.transformPoint(rectCenter, this.fabricCanvas.viewportTransform);

    // Calculate the pan adjustment to center the cropped area
    const panX = (canvasWidth / 2 - newRectCenter.x) + this.fabricCanvas.viewportTransform[4];
    const panY = (canvasHeight / 2 - newRectCenter.y) + this.fabricCanvas.viewportTransform[5];

    // Apply the pan adjustment
    this.fabricCanvas.viewportTransform = [zoom, 0, 0, zoom, panX, panY];

    const newWidth = Math.min(baseBboxStrokeWidth, baseBboxStrokeWidth / zoom);
    const strokeWidthDelta = newWidth - this.rects[0].strokeWidth;

    this.rects.forEach((rect) => {
      // rect.set('stroke', String(rect.get('featureId')) === String(this.props.selectedFeatureId) ? '#56CCF2' : getColorByStr(_.get(rect, 'featureType')));
      // rect.set('stroke', String(rect.get('featureId')) === String(this.props.selectedFeatureId) ? '#56CCF2' : rect.get('pass') ? '#81F499' : '#EB5757');
      rect.set('stroke', String(rect.get('featureId')) === String(this.props.selectedFeatureId) ? '#e2e61c' : rect.get('pass') ? '#81F499' : '#EB5757');

      rect.set({
        strokeWidth: newWidth,
        left: rect.left - strokeWidthDelta,
        top: rect.top - strokeWidthDelta,
        width: rect.width + strokeWidthDelta,
        height: rect.height + strokeWidthDelta,
      });
      rect.setCoords();
    });

    // Re-render the canvas
    this.fabricCanvas.requestRenderAll();
  };

  resetView = () => {
    if (!this.fabricCanvas || !this.scene) return;
    const canvasWidth = this.fabricCanvas.getWidth();
    const canvasHeight = this.fabricCanvas.getHeight();
    const sceneWidth = this.scene.width * this.scene.scaleX;
    const sceneHeight = this.scene.height * this.scene.scaleY;
    const scaleX = canvasWidth / sceneWidth;
    const scaleY = canvasHeight / sceneHeight;
    const zoom = Math.min(scaleX, scaleY);

    const panX = (canvasWidth - sceneWidth * zoom) / 2;
    const panY = (canvasHeight - sceneHeight * zoom) / 2;

    this.fabricCanvas.viewportTransform = [zoom, 0, 0, zoom, panX, panY];

    // update rects stroke width
    if (this.rects.length > 0) {
      const newStrokeWidth = Math.min(baseBboxStrokeWidth, baseBboxStrokeWidth / zoom);
      const strokeWidthDelta = newStrokeWidth - this.rects[0].strokeWidth;
      this.rects.forEach((rect) => {
        rect.set({
          strokeWidth: newStrokeWidth,
          left: rect.left - strokeWidthDelta,
          top: rect.top - strokeWidthDelta,
          width: rect.width + strokeWidthDelta,
          height: rect.height + strokeWidthDelta,
        });
        rect.setCoords();
      });
    }
    this.fabricCanvas.requestRenderAll();
  };

  render() {
    return (
      <div className='w-full h-full' ref={this.containerRef}>
        {super.render()}
      </div>
    );
  }
};