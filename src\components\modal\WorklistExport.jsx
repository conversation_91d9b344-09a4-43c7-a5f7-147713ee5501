import { Button, Modal, Radio } from 'antd';
import React, { useState } from 'react';
import { handleRequestFailed, sleep, toLocalISOString, translation } from '../../common/util';
import { useExportInspectionMutation, useExportSessionMutation } from '../../services/system';
import _ from 'lodash';
import { serverEndpoint } from '../../common/const';
import { PrimaryButtonConfigProvider } from '../../common/darkModeComponents';


const WorklistExport = (props) => {
  const {
    isOpened,
    setIsOpened,
    exportMode, // 'inspection'/'session'
    selectedGoldenProdId,
    startTimeInSession,
    endTimeInSession,
    startTimeInInspection,
    endTimeInInspection,
    ipcSessionId,
    inspectionPagination,
    sessionPagination,
    searchSerialNumber,
    onlyFeedbackProvided,
    onlyDefectiveItems,
  } = props;

  const [scope, setScope] = useState('current'); // 'current'/'all'
  const [generateExportUri] = useExportInspectionMutation();
  const [generateExportSessionUri] = useExportSessionMutation();

  const handleExport = async () => {
    const payload = {};

    if (scope === 'current' && exportMode === 'inspection') {
      payload.page = inspectionPagination.current - 1;
      payload.limit = inspectionPagination.pageSize;
    }

    if (scope === 'current' && exportMode === 'session') {
      payload.page = sessionPagination.current - 1;
      payload.limit = sessionPagination.pageSize;
    }

    if (_.isInteger(selectedGoldenProdId)) {
      payload.golden_product_id = selectedGoldenProdId;
    }

    if (exportMode === 'inspection') {
      if (startTimeInInspection) payload.start_datetime = toLocalISOString(new Date(startTimeInInspection));
      if (endTimeInInspection) payload.end_datetime = toLocalISOString(new Date(endTimeInInspection));
      if (onlyFeedbackProvided) payload.feedback = true;
      if (onlyDefectiveItems) payload.defect = true;
      if (_.isInteger(ipcSessionId)) payload.ipc_session_id = ipcSessionId;
      if (!_.isEmpty(searchSerialNumber)) payload.serial_no = searchSerialNumber;
    }

    if (exportMode === 'session') {
      if (startTimeInSession) payload.start_datetime = toLocalISOString(new Date(startTimeInSession));
      if (endTimeInSession) payload.end_datetime = toLocalISOString(new Date(endTimeInSession));
    }

    if (exportMode === 'inspection') {
      const res = await generateExportUri(payload);

      if (res.error) {
        handleRequestFailed('exportFailed', res.error);
        return;
      }

      const link = document.createElement('a');
      link.href = `${serverEndpoint}/file?file_uri=${_.get(res, 'data.data_uri')}`;
      link.download = 'capture_data.zip';
      link.click();
      link.remove();
    }

    if (exportMode === 'session') {
      const exportSessionRes = await generateExportSessionUri(payload);
      if (exportSessionRes.error) {
        handleRequestFailed('exportFailed', exportSessionRes.error);
        return;
      }
      const link2 = document.createElement('a');
      link2.href = `${serverEndpoint}/file?file_uri=${_.get(exportSessionRes, 'data.data_uri')}`;
      link2.download = 'capture_data.zip';
      link2.click();
      link2.remove();
    }

    setIsOpened(false);
  };
  
  return (
    <Modal
      open={isOpened}
      onCancel={() => setIsOpened(false)}
      title={
        <div className='flex flex-col flex-1 items-start gap-1'>
          <span className='font-source text-[16px] font-semibold'>
            {translation('worklist.worklistExport')}
          </span>
          <span className='font-source text-[12px] font-normal'>
            {translation('worklist.useWorklistFilters')}
          </span>
        </div>
      }
      footer={null}
    >
      <div className='flex py-4 flex-col flex-1 items-start self-stretch gap-6 border-t-[1px] border-AOI-blue'>
        <div className='flex gap-2 flex-col items-start self-stretch'>
          <span className='font-source text-[14px] font-semibold'>
            {translation('worklist.scope')}
          </span>
          <Radio.Group
            value={scope}
            onChange={(e) => setScope(e.target.value)}
            options={[
              {
                value: 'current',
                label: translation('worklist.currentPage'),
              },
              {
                value: 'all',
                label: translation('worklist.allPages'),
              }
            ]}
          />
          <PrimaryButtonConfigProvider>
            <Button
              style={{ width: '100%' }}
              onClick={() => handleExport()}
            >
              <span className='font-source text-[12px] font-normal'>
                {translation('worklist.export')}
              </span>
            </Button>
          </PrimaryButtonConfigProvider>
        </div>
      </div>
    </Modal>
  )
};

export default WorklistExport;