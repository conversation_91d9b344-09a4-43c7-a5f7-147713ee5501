import { Button, Checkbox, Modal } from 'antd';
import React, { useState } from 'react';
import { translation } from '../../common/util';
import { useDispatch } from 'react-redux';
import { setProductVariationDefineModalDisabled } from '../../actions/setting';


const ProductVariationDefineDescription = (props) => {
  const {
    isOpened,
    setIsOpened,
  } = props;

  const dispatch = useDispatch();

  const [noNeedToRemind, setNoNeedToRemind] = useState(false);

  return (
    <Modal
      open={isOpened}
      closable={false}
      title={
        <span className='font-source text-[16px] font-semibold'>
          {translation('includeDefinedComponentsModal.includeDefined')}
        </span>
      }
      footer={
        <Button
          onClick={() => {
            if (noNeedToRemind) dispatch(setProductVariationDefineModalDisabled(true));
            setIsOpened(false);
          }}
        >
          {translation('includeDefinedComponentsModal.gotIt')}
        </Button>
      }
    >
      <div className='flex flex-col py-6 px-4 items-start gap-8 self-stretch'>
        <span className='font-source text-[14px] font-normal'>
          {translation('includeDefinedComponentsModal.theNewVariationWillInclude')}
        </span>
        <div className='flex py-2 gap-2 items-center self-stretch'>
          <Checkbox checked={noNeedToRemind} onChange={() => setNoNeedToRemind(!noNeedToRemind)} />
          <span className='font-source text-[14px] font-normal'>
            {translation('includeDefinedComponentsModal.noNeedToRemind')}
          </span>
        </div>
      </div>
    </Modal>
  );
};

export default ProductVariationDefineDescription;