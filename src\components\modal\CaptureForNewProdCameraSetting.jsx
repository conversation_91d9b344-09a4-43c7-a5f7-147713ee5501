import { Button, Modal } from 'antd';
import React from 'react';
import { translation } from '../../common/util';


const CaptureForNewProdCameraSetting = (props) => {
  const {
    isOpened,
    setIsOpened,
    handleCapture,
  } = props;

  return (
    <Modal
      open={isOpened}
      onCancel={() => setIsOpened(false)}
      title={
        <span className='font-source text-[16px] font-semibold'>
          {translation('imageCaptureRequired.userActionRequired')}
        </span>
      }
      footer={
        <div className='flex flex-col items-start self-stretch gap-2'>
          <Button
            style={{ width: '100%' }}
            onClick={() => {
              handleCapture();
              setIsOpened(false);
            }}
          >
            <span className='font-source text-[12px] font-normal'>
              {translation('common.capture')}
            </span>
          </Button>
          <Button
            style={{ width: '100%' }}
            onClick={() => {
              setIsOpened(false);
            }}
          >
            <span className='font-source text-[12px] font-normal'>
              {translation('newProduct.proceedWithoutCapture')}
            </span>
          </Button>
        </div>
      }
    >
      <div className='flex py-6 px-4 flex-col items-start gap-8 self-stretch'>
        <span className='text-source text-[14px] font-normal'>
          {translation('cameraPreview.positionTheNextPart')}
        </span>
      </div>
    </Modal>
  );
};

export default CaptureForNewProdCameraSetting;