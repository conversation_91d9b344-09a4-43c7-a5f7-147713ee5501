import { Modal } from 'antd';
import React from 'react';
import { translation } from '../../common/util';


const ComponentList = (props) => {
  const {
    isOpened,
    setIsOpened,
    sessionId,
  } = props;
  
  return (
    <Modal
      open={isOpened}
      onCancel={() => setIsOpened(false)}
      title={
        <span className='font-source text-[16px] font-semibold'>
          {translation('worklist.componentList')}
        </span>
      }
      footer={null}
    >
      <div className='flex flex-col items-start gap-4 flex-1 self-stretch'>
        <div className='flex flex-col py-2 px-4 items-start gap-1 self-stretch rounded-[4px] border-gray-2 border-[1px]'>
          <span className='font-source text-[14px] font-normal'>
            {`${translation('worklist.inspection')} #${sessionId}`}
          </span>
          <div className='flex items-center gap-4 flex-1'>
            <span className='font-source text-[12px] font-semibold text-gray-4'>
              
            </span>
          </div>
        </div>
        <div className='flex h-[331px] flex-col items-start self-stretch overflow-y-auto'>
        </div>
      </div>
    </Modal>
  );
};

export default ComponentList;