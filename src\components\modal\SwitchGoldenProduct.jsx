import { But<PERSON>, Modal, Select } from 'antd';
import React, { useEffect, useState } from 'react';
import { PrimaryButtonConfigProvider } from '../../common/darkModeComponents';
import { translation } from '../../common/util';
import { useGetAllProductsQuery, useSetGoldenMutation } from '../../services/product';
import { ALERT_TYPES, aoiAlert } from '../../common/alert';
import _ from 'lodash';
import { useDispatch } from 'react-redux';
import { setContainerWindowLoadingLocked } from '../../actions/setting';


const SwitchGoldenProduct = (props) => {
  const {
    isOpened,
    setIsOpened,
  } = props;
  
  const dispatch = useDispatch();

  const [selectedProductId, setSelectedProductId] = useState(null);

  const { data: allProducts, refetch: refetchAllProducts } = useGetAllProductsQuery();
  const [setGolden] = useSetGoldenMutation();

  const handleSubmit = async (selectedProductId) => {
    if (!_.isInteger(selectedProductId)) {
      aoiAlert(translation('notification.error.pleaseSelectAProduct'), ALERT_TYPES.COMMON_ERROR);
      return;
    }

    dispatch(setContainerWindowLoadingLocked(true));

    const { error } = await setGolden({ product_id: selectedProductId });
    if (error) {
      dispatch(setContainerWindowLoadingLocked(false));
      aoiAlert(translation('notification.error.failedToSetGoldenProduct'), ALERT_TYPES.COMMON_ERROR);
      return;
    }

    dispatch(setContainerWindowLoadingLocked(false));
    aoiAlert(translation('notification.success.setGoldenProductSuccess'), ALERT_TYPES.COMMON_SUCCESS);

    setIsOpened(false);
    return;
  };

  useEffect(() => {
    if (isOpened) {
      setSelectedProductId(null);
      refetchAllProducts();
    }
  }, [isOpened]);

  return (
    <Modal
      open={isOpened}
      onCancel={() => setIsOpened(false)}
      title={
        <span className='font-source text-[16px] font-semibold'>
          {translation('viewInspection.switchGoldenProduct')}
        </span>
      }
      footer={
        <div className='flex p-4 gap-2 slef-stretch items-start justify-center'>
          <Button
            style={{ width: '50%' }}
            onClick={() => setIsOpened(false)}
          >
            <span className='font-source text-[12px] font-normal'>
              {translation('common.cancel')}
            </span>
          </Button>
          <PrimaryButtonConfigProvider>
            <Button
              style={{ width: '50%' }}
              onClick={() => {
                handleSubmit(selectedProductId);
              }}
            >
              <span className='font-source text-[12px] font-normal'>
                {translation('common.update')}
              </span>
            </Button>
          </PrimaryButtonConfigProvider>
        </div>
      }
    >
      <div className='flex py-6 px-4 justify-between self-stretch items-center'>
        <span className='font-source text-[12px] font-normal'>
          {translation('viewInspection.selectAGoldenProduct')}
        </span>
        <Select
          style={{ width: '50%' }}
          value={selectedProductId}
          onChange={(value) => setSelectedProductId(value)}
          options={allProducts?.map((product) => ({
            label: product.product_name,
            value: Number(product.product_id),
          })) || []}
        />
      </div>
    </Modal>
  );
};

export default SwitchGoldenProduct;