import React, { Fragment, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { setInspectionErrorQueue, setIsInspectionErrorQueueOpened } from '../../actions/setting';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from 'antd';
import { translation } from '../../common/util';
import { RedPrimaryButtonConfigProvider } from '../../common/darkModeComponents';
import { maxFisMessageCount } from '../../common/const';
import _ from 'lodash';


const NotificationCol = (props) => {
  const dispatch = useDispatch();

  const [containerHeight, setContainerHeight] = useState(0);

  const inspectionErrorQueue = useSelector((state) => state.setting.inspectionErrorQueue);
  const isInspectionErrorQueueOpened = useSelector((state) => state.setting.isInspectionErrorQueueOpened);

  useEffect(() => {
    setContainerHeight(window.innerHeight);
  }, []);

  return (
    <Fragment>
      <div className={`absolute h-full z-[10002] ${isInspectionErrorQueueOpened ? 'w-[360px]' : 'w-[0px]'} bg-gray-2 
      bg-opacity-65 transition-all duration-300 top-0 right-0`}>
        <div className={`flex flex-col h-full w-full ${!isInspectionErrorQueueOpened && 'hidden'} self-stretch`}>
          <div className='flex h-[60px] items-center justify-between px-4 w-full border-b-2 border-b-gray-1'>
            <RedPrimaryButtonConfigProvider>
              <Button
                type='text'
                onClick={() => dispatch(setIsInspectionErrorQueueOpened(false))}
              >
                <span className='font-source text-[12px] font-normal text-red'>
                  {translation('common.close')}
                </span> 
              </Button>
            </RedPrimaryButtonConfigProvider>
            <Button
              type='text'
              onClick={() => {
                dispatch(setInspectionErrorQueue([]));
              }}
            >
              <span className='font-source text-[12px] font-normal text-white'>
                {translation('common.clearAll')}
              </span>
            </Button>
          </div>
          <div
            className={`flex flex-col overflow-y-auto gap-2 w-full px-4 py-2`}
            style={{ height: `${containerHeight - 60}px` }}
          >
            {inspectionErrorQueue.slice(0, maxFisMessageCount).map((error, index) => (
              // <Card
              //   style={{ width: '100%' }}
              // >
              //   <span className='font-source text-[12px] font-normal'>
              //     {_.get(error, 'message', '')}
              //   </span>
              // </Card>
              <Alert
                key={index}
                message={
                  <span className='font-source text-[12px] font-normal'>
                    {_.get(error, 'message', '')}
                  </span>
                }
                type='error'
              />
            ))}
          </div>
        </div>
      </div>
      <div className={`absolute h-full z-[10001] w-full ${!isInspectionErrorQueueOpened && 'hidden'}`} onClick={() => dispatch(setIsInspectionErrorQueueOpened(false))} />
    </Fragment>
  );
};

export default NotificationCol;