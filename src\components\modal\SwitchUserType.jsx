import { Button, Input, Modal } from 'antd';
import React, { useState } from 'react';
import { useDispatch } from 'react-redux';
import { translation } from '../../common/util';
import { PrimaryButtonConfigProvider } from '../../common/darkModeComponents';
import { setUserType } from '../../actions/setting';
import { ALERT_TYPES, aoiAlert } from '../../common/alert';

const SwitchUserType = (props) => {
  const { isOpened, setIsOpened } = props;
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const dispatch = useDispatch();

  const handleSubmit = () => {
    if (username === 'icc22k662' && password === '000') {
      dispatch(setUserType('admin'));
      setIsOpened(false);
      setUsername('');
      setPassword('');
    } else {
      aoiAlert(translation('notification.error.invalidUsernameOrPassword'), ALERT_TYPES.COMMON_ERROR);
    }
  };

  return (
    <Modal
      open={isOpened}
      onCancel={() => setIsOpened(false)}
      title={<span className='font-source text-[16px] font-semibold'>
        {translation('mainMenu.swapToAdminUser')}
      </span>}
      footer={
        <div className='flex p-4 gap-2 slef-stretch items-start justify-center'>
          <Button style={{ width: '50%' }} onClick={() => setIsOpened(false)}>
            <span className='font-source text-[12px] font-normal'>
              {translation('common.cancel')}
            </span>
          </Button>
          <PrimaryButtonConfigProvider>
            <Button style={{ width: '50%' }} onClick={handleSubmit}>
              <span className='font-source text-[12px] font-normal'>
                {translation('common.confirm')}
              </span>
            </Button>
          </PrimaryButtonConfigProvider>
        </div>
      }
    >
      <div className='flex flex-col gap-4 py-6 px-4 self-stretch'>
        <Input
          placeholder={translation('login.username')}
          value={username}
          onChange={(e) => setUsername(e.target.value)}
        />
        <Input.Password
          placeholder={translation('login.password')}
          value={password}
          onChange={(e) => setPassword(e.target.value)}
        />
      </div>
    </Modal>
  );
};

export default SwitchUserType;
