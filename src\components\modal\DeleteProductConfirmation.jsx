import { Button, Modal } from 'antd';
import React from 'react';
import { useDeleteProductMutation } from '../../services/product';
import { ALERT_TYPES, aoiAlert } from '../../common/alert';
import { handleRequestFailed, translation } from '../../common/util';
import { activeGreen, DarkButton, defaultGreen, RedPrimaryButtonConfigProvider } from '../../common/darkModeComponents';
import _ from 'lodash';


const DeleteProductConfirmation = (props) => {
  const {
    isOpened,
    setIsOpened,
    selectedProductId,
    selectedProductName,
  } = props;

  const [deleteProduct] = useDeleteProductMutation();
  
  return (
    <Modal
      title={
        <span className='font-source text-[16px] font-semibold'>
          {translation('deleteProduct.title')}
        </span>
      }
      open={isOpened}
      onCancel={() => setIsOpened(false)}
      footer={
        <div className='flex flex-col items-start self-stretch gap-2'>
          <RedPrimaryButtonConfigProvider>
            <Button
              style={{ width: '100%' }}
              onClick={async () => {
                setIsOpened(false);
                const res = await deleteProduct(selectedProductId);
                if (_.get(res, 'isError')) {
                  handleRequestFailed('deleteProduct', _.get(res, 'error'));
                  return;
                }
                aoiAlert(translation('notification.success.deleteProduct', { productName: selectedProductName }), ALERT_TYPES.COMMON_SUCCESS);
              }}
            >
              <span className='font-source text-[14px] font-semibold'>
                {translation('common.delete')}
              </span>
            </Button>
          </RedPrimaryButtonConfigProvider>
          <Button
            style={{ width: '100%' }}
            onClick={() => {
              setIsOpened(false)
            }}
          >
            {translation('common.cancel')}
          </Button>
        </div>
      }
    >
      <div className='flex py-6 px-4 flex-col items-start gap-8 self-stretch'>
        <span className='text-source text-[14px] font-normal'>
          {`${translation('deleteProduct.deleteProduct')} ${selectedProductName}?`}
        </span>
      </div>
    </Modal>
  );
};

export default DeleteProductConfirmation;