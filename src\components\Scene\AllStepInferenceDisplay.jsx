import React, { useEffect, useRef, useState } from 'react';
import { translation } from '../../common/util';
import _ from 'lodash';
import StaticStepFeatureListViewer from '../common/viewer/StaticStepFeatureListViewer';


const AllStepInferenceDisplay = (props) => {
  const {
    ipcProductId,
    goldenProdInfo,
    systemMetadata,
    allSessionStepInfo,
    ipcInfo,
    handleRedirect,
    isGoldenComponentChecklistOpened,
    viewInspectionViewMode,
    isLive,
    // missing backend's selected variant for this product, so we can not locate the 'product' feature in the features list
    // so we can not auto pan zoom to the show the product\
    // allStepsFeatures,
  } = props;

  const viewInspectionViewModeRef = useRef(null);
  const isGoldenComponentChecklistOpenedRef = useRef(null);

  const [manualRefreshCount, setManualRefreshCount] = useState(0);

  useEffect(() => {
    if (!isLive) return;

    const handleInspectionViewModeChange = async (viewInspectionViewMode) => {
      if (_.isNull(viewInspectionViewModeRef.current)) {
        viewInspectionViewModeRef.current = viewInspectionViewMode;
        return;
      }
      // const { width, height } = viewerRef.current.getSceneSize();
      // const { width: maskWidth, height: maskHeight } = drawMaskViewerRef.current.getSceneSize();
      // if (viewInspectionViewModeRef.current === 'review' && viewInspectionViewMode === 'single') {
      //   viewerRef.current.updateSceneSize(width + 280, height);
      //   drawMaskViewerRef.current.updateSceneSize(maskWidth + 280, maskHeight);
      // } else if (viewInspectionViewModeRef.current === 'single' && viewInspectionViewMode === 'review') {
      //   viewerRef.current.updateSceneSize(width - 280, height);
      //   drawMaskViewerRef.current.updateSceneSize(maskWidth - 280, maskHeight);
      // }
      setManualRefreshCount(manualRefreshCount + 1);
      viewInspectionViewModeRef.current = viewInspectionViewMode;
    };

    handleInspectionViewModeChange(viewInspectionViewMode);
  }, [viewInspectionViewMode]);

  useEffect(() => {
    if (!isLive) return;

    const handleGoldenComponentChecklistOpenedChange = async (isGoldenComponentChecklistOpened) => {
      if (_.isNull(isGoldenComponentChecklistOpenedRef.current)) {
        isGoldenComponentChecklistOpenedRef.current = isGoldenComponentChecklistOpened;
        return;
      }

      // const { width, height } = viewerRef.current.getSceneSize();
      // const { width: maskWidth, height: maskHeight } = drawMaskViewerRef.current.getSceneSize();
      // if (isGoldenComponentChecklistOpenedRef.current && !isGoldenComponentChecklistOpened) {
      //   viewerRef.current.updateSceneSize(width + 338 + 8, height);
      //   drawMaskViewerRef.current.updateSceneSize(maskWidth + 338 + 8, maskHeight);
      // } else if (!isGoldenComponentChecklistOpenedRef.current && isGoldenComponentChecklistOpened) {
      //   viewerRef.current.updateSceneSize(width - 338 - 8, height);
      //   drawMaskViewerRef.current.updateSceneSize(maskWidth - 338 - 8, maskHeight);
      // }

      setManualRefreshCount(manualRefreshCount + 1);

      isGoldenComponentChecklistOpenedRef.current = isGoldenComponentChecklistOpened
    };

    handleGoldenComponentChecklistOpenedChange(isGoldenComponentChecklistOpened);
  }, [isGoldenComponentChecklistOpened]);

  return (
    <div
      className='flex p-2 flex-col gap-2 items-start flex-1 self-stretch rounded-[2px]'
      style={{ background: 'rgba(255, 255, 255, 0.10)' }}
    >
      <div className='flex items-center justify-between w-full'>
        <div className='flex items-center gap-2 self-stretch'>
          {/* <img src='/img/icn/icn_customGridLayout_white.svg' alt='grid' className='w-[16px] h-[11px] fill-[#fff]' /> */}
          <span className='font-source text-[12px] font-normal'>
            {/* {translation('viewBoards.fullProduct', { productName: _.get(productInfo, 'product_name') })} */}
            {`${_.get(goldenProdInfo, 'product_name')} - ${translation('common.allDetectionSteps')}`}
          </span>
        </div>
        <div className='flex justify-center items-center gap-[3px]'>
          <div
            className={`duration-300 cursor-not-allowed flex w-[32px] h-[32px] py-1 px-0.5 flex-col justify-center items-center rounded-[2px] gap-2.5`}
          >
            <img src='/img/icn/icn_carouselLayout_white.svg' className='w-[16.8px] h-[14px] fill-white' alt='carousel' />
          </div>
          <div
            className={`duration-300 hover:ease-in hover:bg-gray-1 cursor-pointer flex w-[32px] h-[32px] py-1 px-0.5 flex-col justify-center items-center rounded-[2px] gap-2.5 bg-gray-1`}
          >
            <img src='/img/icn/icn_gridLayout_white.svg' className='w-[14px] h-[14px] fill-white' alt='grid' />
          </div>
        </div>
      </div>
      <div
        className='flex flex-col w-full h-full gap-1 self-stretch'
        key={manualRefreshCount}
      >
        {!_.isEmpty(systemMetadata) && !_.isEmpty(goldenProdInfo) && _.map(_.get(systemMetadata, 'inspection_view_layout'), (cols, row) => (
          <div
            className={`grid gap-x-1 self-stretch h-full`}
            style={{ gridTemplateColumns: `repeat(${cols}, 1fr)` }}
            key={row}
          >
            {_.times(cols, (col) => {
              const curStep = _.sum(_.slice(_.get(systemMetadata, 'inspection_view_layout'), 0, row)) + col;
              return <StaticStepFeatureListViewer
                imageUri={_.get(ipcInfo, `color_map_uris.${curStep}`, '')}
                onClick={() => {
                  handleRedirect(curStep);
                }}
                features={_.get(allSessionStepInfo, `${curStep}`, [])}
                displayCanvasRef={null}
                colorByFeatureType={false}
                curIpc={ipcInfo}
                // productFeature={_.find(_.get(allStepsFeatures, `${curStep}`, []), f => f.feature_type === 'product')}
              />
            })}
          </div>
        ))}
      </div>
    </div>
  );
};

export default AllStepInferenceDisplay;