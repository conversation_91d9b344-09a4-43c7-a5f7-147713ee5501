import React, { Fragment, useEffect, useRef, useState } from 'react';
import MainMenuLayout from '../layout/MainMenuLayout';
import { Button, Select, DatePicker, ConfigProvider, Tabs, Pagination, Checkbox, Dropdown, InputNumber, Slider, ColorPicker, Input, Tooltip, Switch } from 'antd';
import { backendAutoGenTimeToDisplayString, backendTimestampToDisplayString, convertFeedbackCorrectnessToChoice, generateFeatureTypeDisplayText, handleRequestFailed, isRevaluateTimeLaterThanLatestRetrainFinishTime, mapRange, sleep, toLocalISOString, translation } from '../../common/util';
import { AOIGreenPrimaryButtonConfig } from '../../common/darkModeComponents';
import i18n from '../../i18n';
import { customZhCNDatePickerLocale, defectDetection, feedbackMaskRequiredAgent, heightDiff, retrainModelType, serverEndpoint } from '../../common/const';
import enUS from 'antd/es/calendar/locale/en_US';
import styled from 'styled-components';
import _, { set } from 'lodash';
import { useGetProductByIdQuery, useLazyLineItemResultsQuery, useLineItemResultsQuery, useReevaluateLineItemMutation } from '../../services/product';
import { useGetCustomFeaturesQuery, useLazyGetAllFeaturesByProductIdAndStepQuery } from '../../services/feature';
import { systemApi } from '../../services/system';
import { useDispatch, useSelector } from 'react-redux';
import HeightDiffComparison from '../Scene/HeightDiffComparison';
import HeightDiffStacked from '../Scene/HeightDiffStacked';
import StaticImageTag from '../common/viewer/StaticImageTag';
import { useAnnotateFeatureMutation, useCancelAnnotationMutation } from '../../services/session';
import { ALERT_TYPES, aoiAlert } from '../../common/alert';
import RetrainConfirmation from '../modal/RetrainConfirmation';
import { setContainerWindowLoadingLocked, setCurTrainingTaskStartTime, setIsTrainingRuning } from '../../actions/setting';
import { useModelUpdateTriggerMutation } from '../../services/model';
import ComponentReviewDrawMaskViewer from '../common/viewer/ComponentReviewDrawMaskViewer';
import SubmitNGFeedbackScoreLowerThanMaxOk from '../modal/SubmitNGFeedbackScoreLowerThanMaxOk';


const { RangePicker } = DatePicker;

const ComponentReview = (props) => {
  const dispatch = useDispatch();

  const {
    goldenProductId
  } = props.match.params;

  const listContainerRef = useRef();

  const drawMaskCanvasRef = useRef();
  const drawMaskViewerRef = useRef();
  const ngConfirmCallbackRef = useRef();

  const [componentIpcResult, setComponentIpcResult] = useState([]);
  const [selectedFeatureId, setSelectedFeatureId] = useState(null);
  const [selectedDetail, setSelectedDetail] = useState(null);
  const [featureTypeFilterOptions, setFeatureTypeFilterOptions] = useState([]); // feature type
  const [featureTypeFilter, setFeatureTypeFilter] = useState(null);
  const [featureIdFilterOptions, setFeatureIdFilterOptions] = useState([]); // feature id
  const [featureIdFilter, setFeatureIdFilter] = useState(null);
  const [predResFilter, setPredResFilter] = useState(null); // null or 'all' means no filter
  const [feedbackFilter, setFeedbackFilter] = useState(null); // null or 'all' means no filter
  const [isDrawMaskEnabled, setIsDrawMaskEnabled] = useState(false);
  const [leftListHeight, setLeftListHeight] = useState(0);
  const [leftListWidth, setLeftListWidth] = useState(0);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 0,
    total: 0,
  });
  const [ipcStartTime, setIpcStartTime] = useState(null);
  const [ipcEndTime, setIpcEndTime] = useState(null);
  const [selectedComponent, setSelectedComponent] = useState(null);
  const [totalCount, setTotalCount] = useState(0);
  const [selectedComponentLineItem, setSelectedComponentLineItem] = useState(null);
  const [isIpcCloudVisible, setIsIpcCloudVisible] = useState(true);
  const [isGoldenCloudVisible, setIsGoldenCloudVisible] = useState(true);
  const [pointCloudDisplayedView, setPointCloudDisplayedView] = useState('top');
  const [activeHeightDiffComparisonView, setActiveHeightDiffComparisonView] = useState('separate'); // separate, stacked
  const [allStepsFeatures, setAllStepsFeatures] = useState([]);
  const [selectedFeature, setSelectedFeature] = useState(null);
  const [curDisplayOptionsBrightness, setCurDisplayOptionsBrightness] = useState(50);
  const [curDisplayOptionsContrast, setCurDisplayOptionsContrast] = useState(50);
  const [curDisplayOptionsSaturation, setCurDisplayOptionsSaturation] = useState(50);
  const [selectedProductId, setSelectedProductId] = useState(null);
  const [isRetrainComfirmationModalOpened, setIsRetrainComfirmationModalOpened] = useState(false);
  const [isSharpnessEnabled, setIsSharpnessEnabled] = useState(false);
  const [selectedStep, setSelectedStep] = useState(null);
  const [selectedLineItemParsedErrorDetail, setSelectedLineItemParsedErrorDetail] = useState(null);
  const [selectedLineItemReevaluatedParsedErrorDetail, setSelectedLineItemReevaluatedParsedErrorDetail] = useState(null);

  const [drawingMode, setDrawingMode] = useState('pan'); // pencil, eraser, pan
  const [pencilStrokeWidth, ********************] = useState(10.0);
  const [pencilStrokeColor, setPencilStrokeColor] = useState('#F2C94C');
  const [isNGFeedbackConfirmationModalOpened, setIsNGFeedbackConfirmationModalOpened] = useState(false);
  const [ngConfirmPayload, setNgConfirmPayload] = useState(null);

  const { data: goldenProduct } = useGetProductByIdQuery(goldenProductId);
  // const { data: featureTypes } = useGetCustomFeaturesQuery({ product_id: goldenProductId });
  const [getLintItemResults] = useLazyLineItemResultsQuery();
  const [lazyGetAllFeatures] = useLazyGetAllFeaturesByProductIdAndStepQuery();
  const [annotateFeature] = useAnnotateFeatureMutation();
  const [retrainTrigger] = useModelUpdateTriggerMutation();
  const [cancelAnnotation] = useCancelAnnotationMutation();
  const [reevaluateTrigger] = useReevaluateLineItemMutation();

  const { data: systemMetadata } = useSelector((state) => systemApi.endpoints.getSystemMetadata.select()(state));
  const latestRetrainFinishTimeByModelType = useSelector((state) => state.setting.latestRetrainFinishTimeByModelType);

  const handleGetAllStepsFeatures = async (systemMetadata, productId) => {
    if (_.isEmpty(systemMetadata)) return;
    const steps = _.sum(_.get(systemMetadata, 'inspection_view_layout'));
    const curResult = [];
    for (let i = 0; i < steps; i++) {
      const res = await lazyGetAllFeatures({
        product_id: productId,
        step: i,
      });
      if (res.error) {
        handleRequestFailed('getAllComponents', res.error);
        return;
      }
      curResult.push(...res.data);
    }
    setAllStepsFeatures(curResult);
    setFeatureTypeFilterOptions(
      _.map(_.uniqBy(curResult, 'feature_type'), i => ({
        label: <span className='font-source text-[12px] font-normal'>
          {generateFeatureTypeDisplayText(i.feature_type)}
        </span>,
        value: i.feature_type,
      })),
    );

    const initFeatureIdFilterOptions = async (curResult) => {
      const res = [];
      for (const f of curResult) {
        let curImgRes;
        try {
          curImgRes = await fetch(`${serverEndpoint}/data?data_uri=${_.get(f, 'cropped_color_map_uri')}`);
        } catch (error) {
          console.error('Failed to fetch feature image', error);
          continue;
        }

        const curImg = await curImgRes.blob();
        const reader = new FileReader();
        reader.readAsDataURL(curImg);
        const curDataUrl = await new Promise((resolve) => {
          reader.onloadend = () => {
            resolve(reader.result);
          };
        });

        res.push({
          label: <div className='flex items-center gap-2'>
            <img className='w-[40px] h-[40px] object-contain' src={curDataUrl} alt='feature' />
            <span className='font-source text-[12px] font-normal'>{`${generateFeatureTypeDisplayText(_.get(f, 'feature_type'))}${_.get(f, 'feature_id')}`}</span>
          </div>,
          value: _.get(f, 'feature_id'),
          featureType: `${generateFeatureTypeDisplayText(_.get(f, 'feature_type'))}${_.get(f, 'feature_id')}`,
        })
      }
      setFeatureIdFilterOptions(res);
    };

    initFeatureIdFilterOptions(curResult);
    // get all features
  };

  const getAllSessionStepInfoByGoldenProductId = async (
    query,
    selectedProductId,
    allStepsFeatures,
    selectedDetail,
    pagination,
    selectedStep,
    selectedFeatureId,
  ) => {
    const res = await getLintItemResults(query);

    if (res.error) {
      handleRequestFailed('getAllComponents', res.error);
      console.error('error', res.error.message);
      return;
    }

    setComponentIpcResult(_.get(res.data, 'line_item_results', []));
    setPagination({
      ...pagination,
      current: _.get(query, 'page', 0) + 1,
      total: _.get(res.data, 'total_count', 0),
    });
    setTotalCount(_.get(res.data, 'total_count', 0));

    // if (!_.isEmpty(selectedProductId) && _.find(_.keys(_.get(res.data, 'line_item_results', {})), i => _.startsWith(String(i), String(selectedProductId)))) {
    if (!_.isEmpty(selectedProductId) && _.find(_.keys(_.get(res.data, 'line_item_results', {})), i => String(i) === `${selectedProductId}_${selectedStep}_${selectedFeatureId}`)) {
      // const firstKey = _.find(_.keys(_.get(res.data, 'line_item_results', {})), i => _.startsWith(i, selectedProductId));
      const firstKey = _.find(_.keys(_.get(res.data, 'line_item_results', {})), i => String(i) === `${selectedProductId}_${selectedStep}_${selectedFeatureId}`);
      setSelectedComponent(_.get(res.data, `line_item_results.${firstKey}`, []));
      setSelectedFeature(_.find(allStepsFeatures, f => f.feature_id === _.get(_.first(_.get(res.data, `line_item_results.${firstKey}`, [])), 'feature_id')));
      // setSelectedFeatureId(_.get(_.first(_.get(res.data, `line_item_results.${firstKey}`, [])), 'feature_id'));
      if (_.isEmpty(selectedDetail)) {
        setSelectedDetail(_.get(_.first(_.get(res.data, `line_item_results.${firstKey}`, [])), 'detail'));
        setSelectedComponentLineItem(_.first(_.get(res.data, `line_item_results.${firstKey}`, [])));
        try {
          setSelectedLineItemParsedErrorDetail(JSON.parse(_.get(_.first(_.get(res.data, `line_item_results.${firstKey}`, [])), 'error', '{}')).error_detail);
        } catch (error) {
          console.error('Failed to parse error detail', error);
          setSelectedLineItemParsedErrorDetail(null);
        }
        if (!_.isEmpty(_.get(res.data, `line_item_results.${firstKey}.[0].reevaluation_result.error`, '{}'))) {
          try {
            setSelectedLineItemReevaluatedParsedErrorDetail(JSON.parse(_.get(_.first(_.get(res.data, `line_item_results.${firstKey}`, [])), 'reevaluation_result.error', '{}')).error_detail);
          } catch (error) {
            console.error('Failed to parse reevaluated error detail', error);
            setSelectedLineItemReevaluatedParsedErrorDetail(null);
          }
        }
      } else {
        setSelectedComponentLineItem(_.find(_.get(res.data, `line_item_results.${firstKey}`, []), i => i.detail === selectedDetail));
        setSelectedDetail(_.get(_.find(_.get(res.data, `line_item_results.${firstKey}`, []), i => i.detail === selectedDetail), 'detail'));
        try {
          setSelectedLineItemParsedErrorDetail(JSON.parse(_.get(_.find(_.get(res.data, `line_item_results.${firstKey}`, []), i => i.detail === selectedDetail), 'error', '{}')).error_detail);
        } catch (error) {
          console.error('Failed to parse error detail', error);
          setSelectedLineItemParsedErrorDetail(null);
        }

        if (!_.isEmpty(_.get(_.find(_.get(res.data, `line_item_results.${firstKey}`, []), i => i.detail === selectedDetail), 'reevaluation_result.error', '{}'))) {
          try {
            setSelectedLineItemReevaluatedParsedErrorDetail(JSON.parse(_.get(_.find(_.get(res.data, `line_item_results.${firstKey}`, []), i => i.detail === selectedDetail), 'reevaluation_result.error', '{}')).error_detail);
          } catch (error) {
            console.error('Failed to parse reevaluated error detail', error);
            setSelectedLineItemReevaluatedParsedErrorDetail(null);
          }
        }
      }
    }
  };

  const handleCancelFeedback = async ({
    selectedComponentLineItem,
    selectedFeature,
    ipcProductId,
    query,
    selectedDetail,
    pagination,
    step,
    featureId,
    allStepsFeatures,
  }) => {
    if (_.isEmpty(selectedComponentLineItem)) return;
    setIsDrawMaskEnabled(false);
    const res = await cancelAnnotation({
      // variant: _.get(selectedComponentLineItem, 'variant', 'abcd'),
      variant: _.get(_.find(allStepsFeatures, f => f.feature_id === Number(featureId)), 'variant', ''),
      product_id: Number(ipcProductId),
      step: Number(step),
      feature_id: Number(featureId),
      line_item_name: _.get(selectedComponentLineItem, 'detail'),
    });
    if (_.get(res, 'error')) {
      handleRequestFailed('cancelAnnotation', _.get(res, 'error'));
      return;
    }
    aoiAlert(translation('notification.success.cancelAnnotation'), ALERT_TYPES.COMMON_SUCCESS);
    getAllSessionStepInfoByGoldenProductId(
      query,
      ipcProductId,
      allStepsFeatures,
      selectedDetail,
      pagination,
      step,
      featureId,
    );
  };

  const handleFeedbackGoodClick = async ({
    selectedComponentLineItem,
    selectedFeature,
    ipcProductId,
    query,
    allStepsFeatures,
    selectedDetail,
    pagination,
    selectedStep,
    selectedFeatureId,
  }) => {
    if (_.isEmpty(selectedFeature) || _.isEmpty(selectedComponentLineItem)) return;

    if ((_.get(selectedComponentLineItem, 'feedback.correct') === true && _.get(selectedComponentLineItem, 'pass')) || (_.get(selectedComponentLineItem, 'feedback.correct') === false && !_.get(selectedComponentLineItem, 'pass'))) {
      aoiAlert(translation('notification.warning.correctFeedbackProvided'), ALERT_TYPES.COMMON_WARNING);
      return;
    }

    setIsDrawMaskEnabled(false);
    const res = await annotateFeature({
      variant: _.get(selectedFeature, 'variant'),
      product_id: Number(ipcProductId),
      step: _.get(selectedFeature, 'step'),
      feature_id: _.get(selectedFeature, 'feature_id'),
      line_item_name: _.get(selectedComponentLineItem, 'detail'),
      is_inference_correct: _.get(selectedComponentLineItem, 'pass') === true,
    });
    if (_.get(res, 'error')) {
      handleRequestFailed('annotateFeature', _.get(res, 'error'));
      return;
    }
    aoiAlert(translation('notification.success.inferenceAnnotation'), ALERT_TYPES.COMMON_SUCCESS);
    getAllSessionStepInfoByGoldenProductId(
      query,
      ipcProductId,
      allStepsFeatures,
      selectedDetail,
      pagination,
      selectedStep,
      selectedFeatureId,
    );
  };

  const handleFeedbackNotGoodClick = async ({
    selectedComponentLineItem,
    selectedFeature,
    ipcProductId,
    query,
    allStepsFeatures,
    selectedDetail,
    pagination,
    selectedStep,
    selectedFeatureId,
    goldenProductId,
  }) => {
    if (_.isEmpty(selectedFeature) || _.isEmpty(selectedComponentLineItem)) return;

    if ((_.get(selectedComponentLineItem, 'feedback.correct') === false && _.get(selectedComponentLineItem, 'pass')) || (_.get(selectedComponentLineItem, 'feedback.correct') === true && !_.get(selectedComponentLineItem, 'pass'))) {
      aoiAlert(translation('notification.warning.defectFeedbackProvided'), ALERT_TYPES.COMMON_WARNING);
      return;
    }

    // if (_.includes(feedbackMaskRequiredAgent, _.get(selectedComponentLineItem, 'detail'))) {
    //   aoiAlert(translation('notification.info.maskReqiredForFeedback'), ALERT_TYPES.COMMON_INFO);
    //   setIsDrawMaskEnabled(true);
    //   return;
    // }

    // check the max score amoung this feature's all training training example which has good feedback
    const goodFeedback = await getLintItemResults({
      golden_product_id: Number(goldenProductId),
      feedback: true,
      feature_id: Number(selectedFeatureId),
      has_feedback: true,
    });

    let goodFeedbackMaxScore = 0;

    // console.log('goodFeedback', goodFeedback);
    // console.log('selectedStepInfo', selectedStepInfo);

    if (!_.isEmpty(_.get(goodFeedback, 'data.line_item_results', {}))) {
      for (const key of _.keys(_.get(goodFeedback, 'data.line_item_results', []))) {
        const twoDResult = _.find(_.get(goodFeedback, `data.line_item_results.${key}`, {}), r => r.detail === defectDetection);
        if (!twoDResult) continue;
        goodFeedbackMaxScore = _.max([goodFeedbackMaxScore, twoDResult.confidence]);
      }
    }

    // console.log('goodFeedbackMaxScore', goodFeedbackMaxScore);
    // console.log('selectedStepInfo score', _.get(selectedComponentLineItem, 'confidence', -1));

    // check if this record's confidence is lower than the max score
    if (_.get(selectedComponentLineItem, 'confidence', -1) !== -1 && goodFeedbackMaxScore > 0 && _.get(selectedComponentLineItem, 'confidence') < goodFeedbackMaxScore) {
      setNgConfirmPayload({
        variant: _.get(selectedFeature, 'variant'),
        product_id: Number(ipcProductId),
        step: _.get(selectedFeature, 'step'),
        feature_id: _.get(selectedFeature, 'feature_id'),
        line_item_name: _.get(selectedComponentLineItem, 'detail'),
        is_inference_correct: _.get(selectedComponentLineItem, 'pass') === false,
      });

      const callback = () => {
        getAllSessionStepInfoByGoldenProductId(
          query,
          ipcProductId,
          allStepsFeatures,
          selectedDetail,
          pagination,
          selectedStep,
          selectedFeatureId,
        );
      };

      ngConfirmCallbackRef.current = callback;
      setIsNGFeedbackConfirmationModalOpened(true);

      return;
    }

    const res = await annotateFeature({
      variant: _.get(selectedFeature, 'variant'),
      product_id: Number(ipcProductId),
      step: _.get(selectedFeature, 'step'),
      feature_id: _.get(selectedFeature, 'feature_id'),
      line_item_name: _.get(selectedComponentLineItem, 'detail'),
      is_inference_correct: _.get(selectedComponentLineItem, 'pass') === false,
    });

    if (_.get(res, 'error')) {
      handleRequestFailed('annotateFeature', _.get(res, 'error'));
      return;
    }

    aoiAlert(translation('notification.success.inferenceAnnotation'), ALERT_TYPES.COMMON_SUCCESS);
    getAllSessionStepInfoByGoldenProductId(
      query,
      ipcProductId,
      allStepsFeatures,
      selectedDetail,
      pagination,
      selectedStep,
      selectedFeatureId,
    );
  };

  const handleSaveMaskAndFeedback = async ({
    selectedComponentLineItem,
    selectedFeatureId,
    ipcProductId,
    selectedFeature,
    step,
    drawMaskViewerRef,
    systemMetadata,
    goldenProductId,
    query,
    allStepsFeatures,
    selectedDetail,
    selectedStep,
    pagination,
  }) => {
    if (_.isEmpty(selectedComponentLineItem)) return;
    if (drawMaskViewerRef.current) {
      const drawnMaskBase64 = drawMaskViewerRef.current.outputDrawnMaskAsBase64();
      if (_.isEmpty(drawnMaskBase64)) {
        aoiAlert(translation('notification.warning.emptyMask'), ALERT_TYPES.COMMON_WARNING);
        return;
      }
      // console.log('drawnMaskBase64', drawnMaskBase64);
      // console.log('height', _.get(selectedFeature, 'roi.points.1.y') - _.get(selectedFeature, 'roi.points.0.y'),);
      // console.log('width', _.get(selectedFeature, 'roi.points.1.x') - _.get(selectedFeature, 'roi.points.0.x'),);
  
      const res = await annotateFeature({
        variant: _.get(selectedFeature, 'variant'),
        product_id: Number(ipcProductId),
        step: Number(step),
        feature_id: Number(selectedFeatureId),
        is_inference_correct: _.get(selectedComponentLineItem, 'pass') === false,
        line_item_name: _.get(selectedComponentLineItem, 'detail'),
        mask_data: drawnMaskBase64,
        mask_height: _.get(selectedFeature, 'roi.points.1.y') - _.get(selectedFeature, 'roi.points.0.y'),
        mask_width: _.get(selectedFeature, 'roi.points.1.x') - _.get(selectedFeature, 'roi.points.0.x'),
      });
      if (_.get(res, 'error')) {
        handleRequestFailed('annotateFeature', _.get(res, 'error'));
        return;
      }
      aoiAlert(translation('notification.success.inferenceAnnotation'), ALERT_TYPES.COMMON_SUCCESS);
    }
    setIsDrawMaskEnabled(false);

    // handleGetAllStepsFeatures(systemMetadata, goldenProductId);

    getAllSessionStepInfoByGoldenProductId(
      query,
      ipcProductId,
      allStepsFeatures,
      selectedDetail,
      pagination,
      selectedStep,
      selectedFeatureId,
    );
  };

  // const handleSaveMaskAndFeedback = async ({
  //   selectedComponentLineItem,
  //   selectedFeatureId,
  //   ipcProductId,
  //   selectedFeature,
  //   step,
  //   drawMaskViewerRef,
  //   systemMetadata,
  //   goldenProductId,
  //   query,
  //   allStepsFeatures,
  //   selectedDetail,
  //   selectedStep,
  //   pagination,
  // }) => {
  //   if (_.isEmpty(selectedComponentLineItem)) return;
  //   if (drawMaskViewerRef.current) {
  //     const drawnMaskBase64 = drawMaskViewerRef.current.outputDrawnMaskAsBase64();
  //     if (_.isEmpty(drawnMaskBase64)) {
  //       aoiAlert(translation('notification.warning.emptyMask'), ALERT_TYPES.COMMON_WARNING);
  //       return;
  //     }
  //     // console.log('drawnMaskBase64', drawnMaskBase64);
  //     // console.log('height', _.get(selectedFeature, 'roi.points.1.y') - _.get(selectedFeature, 'roi.points.0.y'),);
  //     // console.log('width', _.get(selectedFeature, 'roi.points.1.x') - _.get(selectedFeature, 'roi.points.0.x'),);
  
  //     const res = await annotateFeature({
  //       variant: _.get(selectedFeature, 'variant'),
  //       product_id: Number(ipcProductId),
  //       step: Number(step),
  //       feature_id: Number(selectedFeatureId),
  //       is_inference_correct: _.get(selectedComponentLineItem, 'pass') === false,
  //       line_item_name: _.get(selectedComponentLineItem, 'detail'),
  //       mask_data: drawnMaskBase64,
  //       mask_height: _.get(selectedFeature, 'roi.points.1.y') - _.get(selectedFeature, 'roi.points.0.y'),
  //       mask_width: _.get(selectedFeature, 'roi.points.1.x') - _.get(selectedFeature, 'roi.points.0.x'),
  //     });
  //     if (_.get(res, 'error')) {
  //       handleRequestFailed('annotateFeature', _.get(res, 'error'));
  //       return;
  //     }
  //     aoiAlert(translation('notification.success.inferenceAnnotation'), ALERT_TYPES.COMMON_SUCCESS);
  //   }
  //   setIsDrawMaskEnabled(false);

  //   await sleep(500);

  //   // handleGetAllStepsFeatures(systemMetadata, goldenProductId);

  //   getAllSessionStepInfoByGoldenProductId(
  //     query,
  //     ipcProductId,
  //     allStepsFeatures,
  //     selectedDetail,
  //     pagination,
  //     selectedStep,
  //     selectedFeatureId,
  //   );
  // };

  const handleRetrainModel = async (startTrainingAfterSession, pid) => {
    const payload = {
      model_types: [retrainModelType.defectModel, retrainModelType.heightModel],
    };
    if (_.isInteger(pid)) payload.golden_product_id = pid;
    const res = await retrainTrigger(payload);
    if (_.get(res, 'error')) {
      handleRequestFailed('retrainModel', _.get(res, 'error'));
      dispatch(setContainerWindowLoadingLocked(false));
      return;
    }

    dispatch(setIsTrainingRuning(true));
    dispatch(setCurTrainingTaskStartTime(new Date().getTime()));
    return;
  };

  const generateCurrentQuery = () => {
    const newQuery = {};

    newQuery.golden_product_id = Number(_.get(goldenProduct, 'product_id', 0));
    newQuery.page = pagination.current - 1;
    newQuery.limit = pagination.pageSize;

    if (ipcStartTime) newQuery.start_datetime = toLocalISOString(new Date(ipcStartTime));
    if (ipcEndTime) newQuery.end_datetime = toLocalISOString(new Date(ipcEndTime));

    if (_.isBoolean(predResFilter)) newQuery.pass = predResFilter;
    if (_.isBoolean(feedbackFilter)) newQuery.feedback = feedbackFilter;
    if (_.isString(feedbackFilter) && feedbackFilter === 'np') newQuery.has_feedback = false;

    if (!_.isEmpty(featureTypeFilter)) newQuery.component = featureTypeFilter;
    if (_.isInteger(featureIdFilter)) newQuery.feature_id = featureIdFilter;

    return newQuery;
  };

  const generateReevaluateQuery = () => {
    const newQuery = {};

    newQuery.golden_product_id = Number(_.get(goldenProduct, 'product_id', 0));
    // newQuery.page = pagination.current - 1;
    // newQuery.limit = pagination.pageSize;

    if (ipcStartTime) newQuery.start_datetime = toLocalISOString(new Date(ipcStartTime));
    if (ipcEndTime) newQuery.end_datetime = toLocalISOString(new Date(ipcEndTime));

    if (_.isBoolean(predResFilter)) newQuery.pass = predResFilter;
    if (_.isBoolean(feedbackFilter)) newQuery.feedback_pass = feedbackFilter;
    if (_.isString(feedbackFilter) && feedbackFilter === 'np') newQuery.has_feedback = false;

    if (_.isInteger(featureIdFilter)) newQuery.feature_id = featureIdFilter;

    return newQuery;
  };

  const handleEvaluateTrigger = async ({
    query,
    selectedProductId,
    allStepsFeatures,
    selectedDetail,
    pagination,
    selectedStep,
    selectedFeatureId,
    refetchQuery,
  }) => {
    dispatch(setContainerWindowLoadingLocked(true));

    const res = await reevaluateTrigger(query);
    
    if (_.get(res, 'error')) {
      handleRequestFailed('reevaluateTrigger', _.get(res, 'error'));
      dispatch(setContainerWindowLoadingLocked(false));
      return;
    }

    dispatch(setContainerWindowLoadingLocked(false));
    aoiAlert(translation('notification.success.datasetReevaluated'), ALERT_TYPES.COMMON_SUCCESS);

    getAllSessionStepInfoByGoldenProductId(
      refetchQuery,
      selectedProductId,
      allStepsFeatures,
      selectedDetail,
      pagination,
      selectedStep,
      selectedFeatureId,
    );
  };

  useEffect(() => {
    if (_.isEmpty(goldenProduct) || pagination.pageSize === 0 || _.isUndefined(goldenProduct)) return;

    const newQuery = generateCurrentQuery();
    
    getAllSessionStepInfoByGoldenProductId(
      {
        ...newQuery,
        limit: pagination.pageSize,
        page: 0,
      },
      selectedProductId,
      allStepsFeatures,
      selectedDetail,
      pagination,
      selectedStep,
      selectedFeatureId,
    );
  }, [
    goldenProduct,
    featureTypeFilter,
    predResFilter,
    feedbackFilter,
    // pagination.current,
    // pagination.pageSize,
    ipcStartTime,
    ipcEndTime,
    featureIdFilter,
  ]);

  useEffect(() => {
    if (_.isEmpty(goldenProduct) || _.isUndefined(goldenProduct)) return;
    handleGetAllStepsFeatures(systemMetadata, _.get(goldenProduct, 'product_id'));
  }, [goldenProduct]);

  useEffect(() => {
    if (_.isEmpty(selectedComponentLineItem) || _.isEmpty(allStepsFeatures)) return;
    if (!drawMaskViewerRef.current || !drawMaskCanvasRef.current) return;
    if (!isDrawMaskEnabled) {
      drawMaskViewerRef.current.handleChangeMode('pan');
      setDrawingMode('pan');
    }
    if (!isDrawMaskEnabled || !selectedComponentLineItem) return;

    const loadCroppedFrame = async (allStepsFeatures, selectedComponentLineItem) => {
      const selectedFeature = _.find(allStepsFeatures, (feature) => String(feature.feature_id) === String(_.get(selectedComponentLineItem, 'feature_id')));

      if (!selectedFeature) {
        aoiAlert(translation('notification.error.selectedFeatureNotFound'), ALERT_TYPES.COMMON_ERROR);
        return;
      }

      drawMaskViewerRef.current.clearScene();
      drawMaskViewerRef.current.loadCroppedFrame(
        _.get(selectedComponentLineItem, 'cropped_color_map_uri'),
        selectedFeature,
        _.get(selectedComponentLineItem, 'feedback.defect_mask_uri'),
      );
    };

    loadCroppedFrame(allStepsFeatures, selectedComponentLineItem);
  }, [isDrawMaskEnabled, selectedComponentLineItem, allStepsFeatures]);

  useEffect(() => {
    const setPageSize = () => {
      if (!listContainerRef.current) return;

      const height = listContainerRef.current.clientHeight - 16 - 32; // 32 for pagination
      const width = listContainerRef.current.clientWidth - 16 - 16; // another 16 for scrollbar
      setLeftListHeight(height);
      setLeftListWidth(width);

      // console.log(Math.floor(height / 135));
      // console.log(Math.floor(width / 110));
      // console.log(Math.floor(height / 135) * Math.floor(width / 110));

      const pageSize = Math.floor(height / 135) * Math.floor(width / 110);

      if (pageSize === 0) return;

      setPagination({
        ...pagination,
        pageSize,
      });

      if (_.isUndefined(goldenProduct)) return;

      const newQuery = generateCurrentQuery();
      getAllSessionStepInfoByGoldenProductId(
        {
          ...newQuery,
          limit: pageSize,
        },
        selectedProductId,
        allStepsFeatures,
        selectedDetail,
        {
          ...pagination,
          pageSize,
        },
        selectedStep,
        selectedFeatureId,
      );
    };

    setPageSize();

    window.addEventListener('resize', setPageSize);

    return () => {
      window.removeEventListener('resize', setPageSize);
    };
  }, []);

  return (
    <Fragment>
      <SubmitNGFeedbackScoreLowerThanMaxOk
        isOpened={isNGFeedbackConfirmationModalOpened}
        setIsOpened={setIsNGFeedbackConfirmationModalOpened}
        ngConfirmPayload={ngConfirmPayload}
        callbackRef={ngConfirmCallbackRef}
      />
      <RetrainConfirmation
        isOpened={isRetrainComfirmationModalOpened}
        setIsOpened={setIsRetrainComfirmationModalOpened}
        handleRetrainTrigger={handleRetrainModel}
        defaultGoldenProductId={Number(goldenProductId)}
      />
    <MainMenuLayout>
      <div className='flex flex-col flex-1 rounded-[2px] bg-[#131313]'>
        <div className='flex py-2 flex-col items-start gap-2 self-stretch'>
          <div className='flex h-[48px] py-2 px-8 justify-between items-center self-stretch'>
            <div className='flex items-center gap-3'>
              <div
                className='flex w-6 h-6 cursor-pointer justify-center items-center gap-2.5 hover:bg-gray-1 rounded-[2px]'
                onClick={() => props.history.push('/aoi/manage-product')}
              >
                <img src='/img/icn/icn_arrowLeft_white.svg' alt='back' className='w-[8px] h-[16px] shrink' />
              </div>
              <span className='font-source text-[20px] font-semibold'>
                {translation('componentReview.componentReview')}
              </span>
            </div>
            <AOIGreenPrimaryButtonConfig>
              <Button
                onClick={() => setIsRetrainComfirmationModalOpened(true)}
              >
                <span className='font-source text-[12px] font-semibold'>
                  {translation('componentReview.retrainModelNow')}
                </span>
              </Button>
            </AOIGreenPrimaryButtonConfig>
          </div>
        </div>
        <div className='flex flex-col flex-1 self-stretch'>
          <div className='flex py-2 px-8 gap-6 items-center self-stretch rounded-[2px] bg-[#ffffff0d]'>
            <div className='flex gap-6'>
              <div className='flex gap-1 items-center'>
                <div className='flex items-center gap-4'>
                  <img src='/img/icn/icn_filter_white.svg' className='w-3 h-3' alt='filter' />
                  {/* <Select
                    style={{ width: '150px' }}
                    popupMatchSelectWidth={false}
                    
                  />
                  <Select
                    style={{ width: '150px' }}
                    popupMatchSelectWidth={false}
                    
                  /> */}
                  <div className='flex gap-2 items-center'>
                    <span className='font-source text-[12px] font-normal'>
                      {translation('componentReview.filterByFeatureType')}
                    </span>
                    {/* <Select
                      size='small'
                      style={{ width: '150px' }}
                      popupMatchSelectWidth={false}
                      // mode='tags'
                      allowClear
                      options={featureTypeFilterOptions}
                      value={featureTypeFilter}
                      onChange={(value) => setFeatureTypeFilter(value)}
                      showSearch
                    /> */}
                    <Select
                      size='small'
                      style={{ width: '150px', height: '48px' }}
                      popupMatchSelectWidth={false}
                      options={featureIdFilterOptions}
                      value={featureIdFilter}
                      onChange={(value) => setFeatureIdFilter(value)}
                      allowClear
                      showSearch
                      optionFilterProp='featureType'
                    />
                  </div>
                  <div className='flex gap-2 items-center'>
                    <span className='font-source text-[12px] font-normal'>
                      {translation('componentReview.filterByInferenceResult')}
                    </span>
                    <Select
                      size='small'
                      style={{ width: '150px' }}
                      popupMatchSelectWidth={false}
                      options={[
                        {
                          label: <span className='font-source text-[12px] font-normal'>
                            {translation('common.all')}
                          </span>,
                          value: null,
                        },
                        {
                          label: <span className='font-source text-[12px] font-normal'>
                            {translation('componentReview.good')}
                          </span>,
                          value: true,
                        },
                        {
                          label: <span className='font-source text-[12px] font-normal'>
                            {translation('componentReview.notGood')}
                          </span>,
                          value: false,
                        },
                      ]}
                      value={predResFilter}
                      onChange={(value) => setPredResFilter(value)}
                    />
                  </div>
                  <div className='flex gap-2 items-center'>
                    <div className='flex items-center gap-1'>
                      <span className='font-source text-[12px] font-normal'>
                        {translation('componentReview.filterByFeedback')}
                      </span>
                      <div className='flex self-stretch items-center justify-center pb-0.5'>
                        <Tooltip
                          title={translation('componentReview.filterByFeedbackTooltip')}
                          placement='top'
                        >
                          <img src='/img/icn/icn_info_white.svg' className='w-3 h-3' alt='info' />
                        </Tooltip>
                      </div>
                    </div>
                    <Select
                      size='small'
                      style={{ width: '150px' }}
                      popupMatchSelectWidth={false}
                      options={[
                        {
                          label: <span className='font-source text-[12px] font-normal'>
                            {translation('common.all')}
                          </span>,
                          value: null,
                        },
                        {
                          label: <span className='font-source text-[12px] font-normal'>
                            {translation('componentReview.good')}
                          </span>,
                          value: true,
                        },
                        {
                          label: <span className='font-source text-[12px] font-normal'>
                            {translation('componentReview.notGood')}
                          </span>,
                          value: false,
                        },
                        {
                          label: <span className='font-source text-[12px] font-normal'>
                            {translation('componentReview.notProvided')}
                          </span>,
                          value: 'np',
                        }
                      ]}
                      value={feedbackFilter}
                      onChange={(value) => setFeedbackFilter(value)}
                    />
                  </div>
                </div>
              </div>
              <div className='flex gap-2 items-center'>
                <span className='font-source text-[12px] font-normal'>
                  {translation('componentReview.inspectionTask')}
                </span>
                <RangePicker
                  size='small'
                  locale={i18n.language === 'cn' ? customZhCNDatePickerLocale : enUS}
                  showTime
                  onCalendarChange={(value) => {
                    setIpcStartTime(_.get(value, '0', null));
                    setIpcEndTime(_.get(value, '1', null));
                  }}
                  value={[ipcStartTime, ipcEndTime]}
                />
              </div>
            </div>
            <Button
              type='text'
              onClick={() => {
                setFeatureTypeFilter(null);
                setPredResFilter(null);
                setFeedbackFilter(null);
                setIpcStartTime(null);
                setIpcEndTime(null);
              }}
            >
              <span className='font-source text-[12px] font-normal text-white'>
                {translation('common.clearFilter')}
              </span>
            </Button>
          </div>
          <div className='flex px-8 py-2 gap-2 flex-1 items-start self-stretch'>
            <div className='flex w-[477px] flex-col items-start gap-2 self-stretch rounded-[2px] bg-[#ffffff0d]'>
              <div className='flex flex-col flex-1 self-stretch'>
                <div className='flex items-center justify-between p-2 border-b-[1px] border-b-gray-1 h-[49px]'>
                  <span className='font-source text-[12px] font-normal'>
                    {`${translation('componentReview.foundResults')} ${totalCount}`}
                  </span>
                  {/* <Select
                    style={{ width: '214px' }}
                    popupMatchSelectWidth={false}
                    options={[
                      {
                        label: <span className='font-source text-[12px] font-normal'>
                          {translation('componentReview.sortByIpcTime')}
                        </span>,
                        value: 'ipcTime',
                      },
                    ]}
                    value='ipcTime'
                  /> */}
                  <div className='flex gap-1 items-center'>
                    <Button
                      size='small'
                      onClick={() => {
                        handleEvaluateTrigger({
                          query: generateReevaluateQuery(),
                          selectedProductId,
                          allStepsFeatures,
                          selectedDetail,
                          pagination,
                          selectedStep,
                          selectedFeatureId,
                          refetchQuery: generateCurrentQuery(),
                        });
                      }}
                    >
                      <span className='font-source text-[12px] font-normal'>
                        {translation('componentReview.reevaluateWithNewModel', { recordCount: String(totalCount) })}
                      </span>
                    </Button>
                  </div>
                </div>
                <div
                  className='flex flex-1 self-stretch p-2'
                  ref={listContainerRef}
                >
                  <div
                    className='flex gap-2 flex-1 items-start content-start self-stretch flex-wrap overflow-y-auto'
                    style={{
                      height: `${leftListHeight}px`,
                      width: `${leftListWidth}px`,
                    }}
                  >
                    {_.map(_.keys(componentIpcResult), k => {
                      const curC = _.get(componentIpcResult, k, []);
                      const firstLineItem = _.first(curC);
                      return <SingleComponentCard
                        key={k}
                        imageUri={_.get(firstLineItem, 'cropped_color_map_uri')}
                        displayFeatureType={generateFeatureTypeDisplayText(firstLineItem.feature_type)}
                        displayProductId={_.get(_.split(k, '_'), '0')}
                        // timestamp={i.timestamp}
                        selected={selectedStep === _.get(_.split(k, '_'), '1') && selectedFeatureId === _.get(_.split(k, '_'), '2') && selectedProductId === _.get(_.split(k, '_'), '0')}
                        reevaluateResult={_.get(firstLineItem, 'reevaluation_result', {})}
                        lineItemObject={firstLineItem}
                        onClick={() => {
                          setIsDrawMaskEnabled(false);
                          setDrawingMode('pan');
                          setSelectedStep(_.get(_.split(k, '_'), '1'));
                          setSelectedFeatureId(_.get(_.split(k, '_'), '2'));
                          setSelectedDetail(_.get(firstLineItem, 'detail'));
                          setSelectedComponentLineItem(firstLineItem);
                          try {
                            setSelectedLineItemParsedErrorDetail(JSON.parse(_.get(firstLineItem, 'error', '{}')).error_detail);
                          } catch (error) {
                            console.error('Failed to parse error detail', error);
                            setSelectedLineItemParsedErrorDetail(null);
                          }
                          if (_.get(firstLineItem, 'reevaluation_result.error')) {
                            try {
                              setSelectedLineItemReevaluatedParsedErrorDetail(JSON.parse(_.get(firstLineItem, 'reevaluation_result.error', '{}')).error_detail);
                            } catch (error) {
                              console.error('Failed to parse reevaluated error detail', error);
                              setSelectedLineItemReevaluatedParsedErrorDetail(null);
                            }
                          }
                          setSelectedComponent(curC);
                          setSelectedFeature(_.find(allStepsFeatures, f => f.feature_id === firstLineItem.feature_id));
                          setSelectedProductId(_.get(_.split(k, '_'), '0'));
                        }}
                      />;
                    })}
                  </div>
                </div>
                <div className='w-full flex items-center'>
                  <Pagination
                    style={{ width: '100%' }}
                    align='center'
                    current={pagination.current}
                    pageSize={pagination.pageSize}
                    total={pagination.total}
                    hideOnSinglePage
                    showSizeChanger={false}
                    onChange={(page) => {
                      setPagination({
                        ...pagination,
                        current: page,
                      });
                      if (_.isEmpty(goldenProduct) || pagination.pageSize === 0 || _.isUndefined(goldenProduct)) return;

                      const newQuery = generateCurrentQuery();
                      
                      getAllSessionStepInfoByGoldenProductId(
                        {
                          ...newQuery,
                          page: page - 1,
                        },
                        selectedProductId,
                        allStepsFeatures,
                        selectedDetail,
                        {
                          ...pagination,
                          current: page,
                        },
                        selectedStep,
                        selectedFeatureId,
                      );
                    }}
                  />
                </div>
              </div>
            </div>
            <div className='flex flex-col self-stretch flex-1'>
              {!_.isEmpty(selectedComponent) &&
              <Fragment>
              <div className='flex pt-1 pr-4 pl-4 items-center self-stretch h-[40px] w-full gap-4'>
                <ConfigProvider
                  theme={{
                    components: {
                      Tabs: {
                        cardBg: '#131313',
                        colorBgContainer: 'rgba(86, 204, 242, 0.16)',
                        cardHeight: '36',
                        cardPadding: '8px 12px',
                        colorPrimary: '#fff',
                        itemHoverColor: '#fff',
                        itemColor: '#E0E0E0',
                      },
                    },
                  }}
                >
                  <CustomTabs
                    style={{ height: '100%', width: '100%' }}
                    type='card'
                    activeKey={selectedDetail}
                    onChange={(k) => {
                      setSelectedDetail(k);
                      setSelectedComponentLineItem(_.find(selectedComponent, i => i.detail === k));
                      try {
                        setSelectedLineItemParsedErrorDetail(JSON.parse(_.get(_.find(selectedComponent, i => i.detail === k), 'error', '{}')).error_detail);
                      } catch (error) {
                        console.error('Failed to parse error detail', error);
                        setSelectedLineItemParsedErrorDetail(null);
                      }
                      if (_.get(_.find(selectedComponent, i => i.detail === k), 'reevaluation_result.error')) {
                        try {
                          setSelectedLineItemReevaluatedParsedErrorDetail(JSON.parse(_.get(_.find(selectedComponent, i => i.detail === k), 'reevaluation_result.error', '{}')).error_detail);
                        } catch (error) {
                          console.error('Failed to parse reevaluated error detail', error);
                          setSelectedLineItemReevaluatedParsedErrorDetail(null);
                        }
                      }
                    }}
                    items={_.map(selectedComponent, l => ({
                      label: <div className='flex items-center gap-1 self-stretch justify-center'>
                        { !l.pass &&
                          <div className='flex w-3 h-3 flex-col justify-center items-center'>
                            <img src='/img/icn/icn_warning_red.svg' className='w-4 h-[10.5px] shrink' alt='warning' />
                          </div>
                        }
                        <span
                          className={`font-source text-[12px] font-${selectedDetail === l.detail ? 'semibold' : 'normal'}`}
                          // style={{ color: selectedDetail === i.detail ? '#fff' : '#E0E0E0' }}
                        >
                          {translation(`viewInspection.lineItem.${l.detail}`)}
                        </span>
                      </div>,
                      key: l.detail,
                      children: null
                    }))}
                  />
                </ConfigProvider>
                <Button
                  onClick={() => {
                    // create a new query with golden_product_id, step, target_product_id
                    const newQuery = {
                      golden_product_id: Number(_.get(goldenProduct, 'product_id', 0)),
                      step: Number(selectedStep),
                      target_product_id: Number(selectedProductId),
                      // variant: _.get(selectedFeature, 'variant'),
                      feature_id: Number(selectedFeatureId),
                    };

                    handleEvaluateTrigger({
                      query: newQuery,
                      selectedProductId,
                      allStepsFeatures,
                      selectedDetail,
                      pagination,
                      selectedStep,
                      selectedFeatureId,
                      refetchQuery: generateCurrentQuery(),
                    });
                  }}
                  size='small'
                >
                  <span className='font-source text-[12px] font-normal'>
                    {translation('componentReview.reevaluateWithNewModelForThisRecord')}
                  </span>
                </Button>
              </div>
              { !_.isEmpty(selectedDetail) && !_.isEmpty(selectedComponentLineItem) &&
                <Fragment>
                <div className='flex py-2 px-4 justify-between items-start self-stretch border-[1px] border-gray-1 bg-[#56ccf229] '>
                  <div className='flex py-[3px] flex-col items-start gap-2 flex-1'>
                    <div className='flex items-center gap-6 self-stretch'>
                      <span className='font-source text-[14px] font-semibold w-[194px]'>
                        {translation('viewInspection.AIPrediction')}
                      </span>
                      <div className='flex flex-col gap-1 self-stretch'>
                        {!_.isEmpty(_.get(selectedComponentLineItem, 'reevaluation_result', {})) && isRevaluateTimeLaterThanLatestRetrainFinishTime(selectedComponentLineItem, latestRetrainFinishTimeByModelType) &&
                          <div className='flex items-center gap-2 self-stretch'>
                            <div className='flex items-center gap-1 self-stretch'>
                              <span className='font-source text-[12px] font-normal'>
                                {generateFeatureTypeDisplayText(_.get(selectedComponentLineItem, 'feature_type'))}
                              </span>
                              <span className='font-source text-[12px] font-normal'>
                                -
                              </span>
                              <div className='flex items-center gap-1 self-stretch py-[2px]'>
                                { _.get(selectedComponentLineItem, 'reevaluation_result.pass', false) ?
                                  <Fragment>
                                    <img src='/img/icn/icn_checkCircle_green.svg' className='w-4 h-[10.5px] shrink' alt='warning' />
                                    <span className='font-source text-[12px] font-normal'>
                                      {translation(`viewInspection.annotateFeedbackOptions.${selectedDetail}.good`)}
                                    </span>
                                  </Fragment>
                                :
                                  <Fragment>
                                    <img src='/img/icn/icn_warning_red.svg' className='w-4 h-[10.5px] shrink' alt='warning' />
                                    <span className='font-source text-[12px] font-normal'>
                                    {translation(`viewInspection.annotateFeedbackOptions.${selectedDetail}.notGood`)}
                                    </span>
                                  </Fragment>
                                }
                              </div>
                            </div>
                            <span className='font-source text-[12px] font-normal text-AOI-blue'>
                              -
                            </span>
                            <span className='font-source text-[12px] font-normal text-AOI-blue italic'>
                              {translation('viewInspection.reevaluatedAt', { displayTime: backendTimestampToDisplayString(_.get(selectedComponentLineItem, 'reevaluation_result.updated_at', '')) })}
                            </span>
                          </div>
                        }
                        <div className='flex items-center gap-2 self-stretch'>
                          <div className='flex items-center gap-1 self-stretch'>
                            <span className='font-source text-[12px] font-normal'>
                              {generateFeatureTypeDisplayText(_.get(selectedComponentLineItem, 'feature_type'))}
                            </span>
                            <span className='font-source text-[12px] font-normal'>
                              -
                            </span>
                            <div className='flex items-center gap-1 self-stretch py-[2px]'>
                              { _.get(selectedComponentLineItem, 'pass') ?
                                <Fragment>
                                  <img src='/img/icn/icn_checkCircle_green.svg' className='w-4 h-[10.5px] shrink' alt='warning' />
                                  <span className='font-source text-[12px] font-normal'>
                                    {translation(`viewInspection.annotateFeedbackOptions.${selectedDetail}.good`)}
                                  </span>
                                </Fragment>
                              :
                                <Fragment>
                                  <img src='/img/icn/icn_warning_red.svg' className='w-4 h-[10.5px] shrink' alt='warning' />
                                  <span className='font-source text-[12px] font-normal'>
                                  {translation(`viewInspection.annotateFeedbackOptions.${selectedDetail}.notGood`)}
                                  </span>
                                </Fragment>
                              }
                            </div>
                          </div>
                          <span className='font-source text-[12px] font-normal text-AOI-blue'>
                            -
                          </span>
                          <span className='font-source text-[12px] font-normal text-AOI-blue italic'>
                            {translation('viewInspection.byModelUsedDuringInspection')}
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className='flex pl-[218px] items-center self-stretch gap-1 flex-1 flex-col'>
                      <div className='flex gap-2 self-stretch items-center self-stretch'>
                        {!_.isEmpty(selectedLineItemReevaluatedParsedErrorDetail) &&
                          <span className='font-source text-[12px] font-normal text-AOI-blue italic'>
                            {translation('common.reevaluationResult')}:
                          </span>
                        }
                        {!_.isEmpty(selectedLineItemReevaluatedParsedErrorDetail) && _.isNumber(_.get(selectedLineItemReevaluatedParsedErrorDetail, 'ai_deviation_score')) && selectedDetail === defectDetection &&
                          <div className='flex items-center gap-1 self-stretch'>
                            <span className='font-source text-[12px] font-normal'>
                              {translation('common.ai_deviation_score')}:
                            </span>
                            {_.get(selectedLineItemReevaluatedParsedErrorDetail, 'ai_deviation_score') === -1 ?
                              <span className='font-source text-[12px] font-normal'>
                                {translation('common.modelNotFound')}
                              </span>
                            :
                              <span className='font-source text-[12px] font-normal'>
                                {_.get(selectedLineItemReevaluatedParsedErrorDetail, 'ai_deviation_score', 0)}
                              </span>
                            }
                          </div>
                        }
                        {!_.isEmpty(selectedLineItemReevaluatedParsedErrorDetail) && _.isNumber(_.get(selectedLineItemReevaluatedParsedErrorDetail, 'ai_deviation_threshold')) && selectedDetail === defectDetection &&
                          <div className='flex items-center gap-1 self-stretch'>
                            <span className='font-source text-[12px] font-normal'>
                              {translation('common.ai_deviation_threshold')}:
                            </span>
                            {_.get(selectedLineItemReevaluatedParsedErrorDetail, 'ai_deviation_threshold') === -1 ?
                              <span className='font-source text-[12px] font-normal'>
                                {translation('common.modelNotFound')}
                              </span>
                            :
                              <span className='font-source text-[12px] font-normal'>
                                {_.get(selectedLineItemReevaluatedParsedErrorDetail, 'ai_deviation_threshold', 0)}
                              </span>
                            }
                          </div>
                        }
                        {!_.isEmpty(selectedLineItemReevaluatedParsedErrorDetail) && _.isNumber(_.get(selectedLineItemReevaluatedParsedErrorDetail, 'height_difference', null)) && selectedDetail === heightDiff &&
                          <div className='flex items-center gap-1 self-stretch'>
                            <span className='font-source text-[12px] font-normal'>
                              {translation('common.height_difference')} (mm):
                            </span>
                            <span className='font-source text-[12px] font-normal'>
                              {_.get(selectedLineItemReevaluatedParsedErrorDetail, 'height_difference', 0)}
                            </span>
                          </div>
                        }
                        {!_.isEmpty(selectedLineItemReevaluatedParsedErrorDetail) && _.isNumber(_.get(selectedLineItemReevaluatedParsedErrorDetail, 'height_difference_threshold')) && selectedDetail === heightDiff &&
                          <div className='flex items-center gap-1 self-stretch'>
                            <span className='font-source text-[12px] font-normal'>
                              {translation('common.height_difference_threshold')} (mm):
                            </span>
                            <span className='font-source text-[12px] font-normal'>
                              {_.get(selectedLineItemReevaluatedParsedErrorDetail, 'height_difference_threshold', 0)}
                            </span>
                          </div>
                        }
                        {/* {!_.isEmpty(selectedLineItemReevaluatedParsedErrorDetail) && _.isNumber(_.get(selectedLineItemReevaluatedParsedErrorDetail, 'tolerance')) && selectedDetail === heightDiff &&
                          <div className='flex items-center gap-1 self-stretch'>
                            <span className='font-source text-[12px] font-normal'>
                              {translation('common.tolerance')}:
                            </span>
                            <span className='font-source text-[12px] font-normal'>
                              {_.get(selectedLineItemReevaluatedParsedErrorDetail, 'tolerance', 0)}
                            </span>
                          </div>
                        } */}
                      </div>
                      <div className='flex items-center gap-1 self-stretch'>
                        {!_.isEmpty(selectedLineItemParsedErrorDetail) &&
                          <span className='font-source text-[12px] font-normal text-AOI-blue italic'>
                            {translation('common.inspectionResult')}:
                          </span>
                        }
                        {!_.isEmpty(selectedLineItemParsedErrorDetail) && _.isNumber(_.get(selectedLineItemParsedErrorDetail, 'ai_deviation_score')) && selectedDetail === defectDetection &&
                          <div className='flex items-center gap-1 self-stretch'>
                            <span className='font-source text-[12px] font-normal'>
                              {translation('common.ai_deviation_score')}:
                            </span>
                            {_.get(selectedLineItemParsedErrorDetail, 'ai_deviation_score') === -1 ?
                              <span className='font-source text-[12px] font-normal'>
                                {translation('common.modelNotFound')}
                              </span>
                            :
                              <span className='font-source text-[12px] font-normal'>
                                {_.get(selectedLineItemParsedErrorDetail, 'ai_deviation_score', 0)}
                              </span>
                            }
                          </div>
                        }
                        {!_.isEmpty(selectedLineItemParsedErrorDetail) && _.isNumber(_.get(selectedLineItemParsedErrorDetail, 'ai_deviation_threshold')) && selectedDetail === defectDetection &&
                          <div className='flex items-center gap-1 self-stretch'>
                            <span className='font-source text-[12px] font-normal'>
                              {translation('common.ai_deviation_threshold')}:
                            </span>
                            {_.get(selectedLineItemParsedErrorDetail, 'ai_deviation_threshold') === -1 ?
                              <span className='font-source text-[12px] font-normal'>
                                {translation('common.modelNotFound')}
                              </span>
                            :
                              <span className='font-source text-[12px] font-normal'>
                                {_.get(selectedLineItemParsedErrorDetail, 'ai_deviation_threshold', 0)}
                              </span>
                            }
                          </div>
                        }
                        {!_.isEmpty(selectedLineItemParsedErrorDetail) && _.isNumber(_.get(selectedLineItemParsedErrorDetail, 'height_difference', null)) && selectedDetail === heightDiff &&
                          <div className='flex items-center gap-1 self-stretch'>
                            <span className='font-source text-[12px] font-normal'>
                              {translation('common.height_difference')} (mm):
                            </span>
                            <span className='font-source text-[12px] font-normal'>
                              {_.get(selectedLineItemParsedErrorDetail, 'height_difference', 0)}
                            </span>
                          </div>
                        }
                        {!_.isEmpty(selectedLineItemParsedErrorDetail) && _.isNumber(_.get(selectedLineItemParsedErrorDetail, 'height_difference_threshold')) && selectedDetail === heightDiff &&
                          <div className='flex items-center gap-1 self-stretch'>
                            <span className='font-source text-[12px] font-normal'>
                              {translation('common.height_difference_threshold')} (mm):
                            </span>
                            <span className='font-source text-[12px] font-normal'>
                              {_.get(selectedLineItemParsedErrorDetail, 'height_difference_threshold', 0)}
                            </span>
                          </div>
                        }
                        {/* {!_.isEmpty(selectedLineItemParsedErrorDetail) && _.isNumber(_.get(selectedLineItemParsedErrorDetail, 'tolerance')) && selectedDetail === heightDiff &&
                          <div className='flex items-center gap-1 self-stretch'>
                            <span className='font-source text-[12px] font-normal'>
                              {translation('common.tolerance')}:
                            </span>
                            <span className='font-source text-[12px] font-normal'>
                              {_.get(selectedLineItemParsedErrorDetail, 'tolerance', 0)}
                            </span>
                          </div>
                        } */}
                      </div>
                    </div>
                    <div className='flex items-center gap-6 self-stretch'>
                      <span className='font-source text-[14px] font-normal w-[194px]'>
                        {translation('viewInspection.feedback')}
                      </span>
                      <Select
                        style={{ width: '90px' }}
                        popupMatchSelectWidth={false}
                        size='small'
                        options={[
                          {
                            label: <div className='flex items-center gap-1 self-stretch'>
                              <img src='/img/icn/icn_checkCircle_green.svg' className='w-4 h-[10.5px] shrink' alt='good' />
                              <span className='font-source text-[12px] font-normal'>
                                {translation(`viewInspection.annotateFeedbackOptions.${selectedDetail}.good`)}
                              </span>
                            </div>,
                            value: true, 
                          },
                          {
                            label: <div className='flex items-center gap-1 self-stretch'>
                              <img src='/img/icn/icn_warning_red.svg' className='w-4 h-[10.5px] shrink' alt='notGood' />
                              <span className='font-source text-[12px] font-normal'>
                                {translation(`viewInspection.annotateFeedbackOptions.${selectedDetail}.notGood`)}
                              </span>
                            </div>,
                            value: false,
                          },
                          {
                            label: <span className='font-source text-[12px] font-normal'>
                              {translation('viewInspection.annotateFeedbackOptions.notProvided')}
                            </span>,
                            value: 'np',
                          }
                        ]}
                        value={_.isEmpty(_.get(selectedComponentLineItem, 'feedback'))
                          ?'np'
                          :convertFeedbackCorrectnessToChoice(
                            _.get(selectedComponentLineItem, 'feedback.correct'),
                            _.get(selectedComponentLineItem, 'pass')
                          )
                        }
                        onChange={(value) => {
                          const newQuery = generateCurrentQuery();
                          if (value === 'np') {
                            handleCancelFeedback({
                              selectedComponentLineItem,
                              selectedFeature,
                              ipcProductId: selectedProductId,
                              query: newQuery,
                              selectedDetail,
                              pagination,
                              step: selectedStep,
                              featureId: selectedFeatureId,
                              allStepsFeatures,
                            });
                            return;
                          }
                          if (value === true) {
                            handleFeedbackGoodClick({
                              selectedComponentLineItem,
                              selectedFeature,
                              ipcProductId: selectedProductId,
                              query: newQuery,
                              allStepsFeatures,
                              selectedDetail,
                              pagination,
                              selectedStep,
                              selectedFeatureId,
                            });
                          } else {
                            handleFeedbackNotGoodClick({
                              selectedComponentLineItem,
                              selectedFeature,
                              ipcProductId: selectedProductId,
                              query: newQuery,
                              allStepsFeatures,
                              selectedDetail,
                              pagination,
                              selectedStep,
                              selectedFeatureId,
                              goldenProductId: _.get(goldenProduct, 'product_id'),
                            });
                          }
                        }}
                      />
                      { !isDrawMaskEnabled &&
                      !_.isEmpty(_.get(selectedComponentLineItem, 'feedback')) &&
                      !_.isEmpty(_.get(selectedComponentLineItem, 'feedback.defect_mask_uri')) &&
                        <Button
                          size='small'
                          disabled={isDrawMaskEnabled}
                          onClick={() => {
                            setIsDrawMaskEnabled(true);
                          }}
                        >
                          <div className='flex items-center gap-1'>
                            <img src='/img/icn/icn_pencil_blue.svg' alt='pencil' className='w-3 h-3' />
                            <span className='font-source text-[12px] font-normal text-AOI-blue'>
                              {translation('viewInspection.editPrevMask')}
                            </span>
                          </div>
                        </Button>
                      }
                      { isDrawMaskEnabled &&
                        <Fragment>
                          <Button
                            size='small'
                            onClick={() => {
                              const newQuery = generateCurrentQuery();
                              handleSaveMaskAndFeedback({
                                selectedComponentLineItem,
                                selectedFeatureId,
                                ipcProductId: selectedProductId,
                                selectedFeature,
                                step: selectedStep,
                                drawMaskViewerRef,
                                systemMetadata,
                                goldenProductId,
                                query: newQuery,
                                allStepsFeatures,
                                selectedDetail,
                                selectedStep,
                                pagination,
                              });
                            }}
                          >
                            <span className='font-source text-[12px] font-semibold'>
                              {translation('viewInspection.submitAsFeedback')}
                            </span>
                          </Button>
                          <Button
                              size='small'
                              onClick={() => {
                                setIsDrawMaskEnabled(false);
                              }}
                            >
                              <span className='font-source text-[12px] font-semibold'>
                                {translation('common.cancel')}
                              </span>
                            </Button>
                        </Fragment>
                      }
                    </div>
                  </div>
                </div>
                <div className='flex pt-2 pb-6 flex-col gap-2 items-start flex-1 self-stretch'>
                  <div className='flex flex-col justify-center items-center self-stretch'>
                    <div className='flex py-1 px-2 items-center gap-2 self-stretch'>
                      <div className='flex w-6 h-6 flex-col justify-center items-center'>
                        <img src='/img/icn/icn_swap_white.svg' className='w-[17px] h-[12px] shrink' alt='swap' />
                      </div>
                      <span className='font-source text-[12px] font-semibold'>
                        {translation('viewInspection.referenceComparison')}
                      </span>
                    </div>
                  </div>
                  <div className='flex px-4 flex-col items-start flex-1 self-stretch'>
                    { selectedDetail === heightDiff &&
                      <Fragment>
                        <div className='flex py-2 flex-col items-start gap-0.5 self-stretch'>
                          <span className='font-source text-[12px] font-normal'>
                            {translation('common.pointCloud')}
                          </span>
                          <div className='flex flex-col items-start gap-0.5 self-stretch'>
                            <div className='p-1 flex justify-between items-center self-stretch '>
                              <div className='flex items-center gap-2'>
                                <div
                                  className='flex w-6 h-6 flex-col justify-center items-center cursor-pointer hover:bg-gray-1 rounded-[2px] gap-2.5 transition-all duration-300'
                                  onClick={() => setIsIpcCloudVisible(!isIpcCloudVisible)}
                                >
                                  { isIpcCloudVisible ?
                                    <img src='/img/icn/icn_visible_white.svg' className='w-[14px] h-[9.5px] shrink' alt='visible' />
                                    :
                                    <img src='/img/icn/icn_invisible_white.svg' className='w-[14px] h-[11.5px] shrink' alt='invisible' />
                                  }
                                </div>
                                <div className='w-[14px] h-[14px] rounded-[2px] border-[1px] border-gray-3 bg-default-yellow' />
                                <span className='font-source text-[12px] font-normal'>
                                  {translation('viewInspection.sample')}
                                </span>
                              </div>
                            </div>
                            <div className='p-1 flex justify-between items-center self-stretch '>
                              <div className='flex items-center gap-2'>
                                <div
                                  className='flex w-6 h-6 flex-col justify-center items-center cursor-pointer hover:bg-gray-1 rounded-[2px] gap-2.5 transition-all duration-300'
                                  onClick={() => setIsGoldenCloudVisible(!isGoldenCloudVisible)}
                                >
                                  { isGoldenCloudVisible ?
                                  <img src='/img/icn/icn_visible_white.svg' className='w-[14px] h-[9.5px] shrink' alt='visible' />
                                  :
                                  <img src='/img/icn/icn_invisible_white.svg' className='w-[14px] h-[11.5px] shrink' alt='invisible' />
                                  }
                                </div>
                                <div className='w-[14px] h-[14px] rounded-[2px] border-[1px] border-gray-3 bg-default-purple-2' />
                                <span className='font-source text-[12px] font-normal'>
                                  {translation('common.goldenProduct')}
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div className='flex py-1 flex-col items-start gap-2.5 self-stretch flex-1'>
                          <div className='flex items-center gap-2.5 self-stretch'>
                            <div className='flex items-center gap-1'>
                              <div
                                className='flex w-[32px] h-[24px] justify-center items-center gap-2.5 rounded-[2px] hover:bg-gray-2 cursor-pointer transition-all duration-300'
                                onClick={() => {
                                  setPointCloudDisplayedView('top');
                                  setIsIpcCloudVisible(true);
                                  setIsGoldenCloudVisible(true);
                                  setActiveHeightDiffComparisonView('stacked');
                                }}
                              >
                                { activeHeightDiffComparisonView === 'stacked' ?
                                  <img src='/img/icn/icn_overlay_blue.svg' className='w-[13.8px] h-[14px] shrink' alt='overlay' />
                                  :
                                  <img src='/img/icn/icn_overlay_white.svg' className='w-[13.8px] h-[14px] shrink' alt='overlay' />
                                }
                              </div>
                              <div
                                className='flex w-[32px] h-[24px] justify-center items-center gap-2.5 rounded-[2px] hover:bg-gray-2 cursor-pointer transition-all duration-300'
                                onClick={() => {
                                  setPointCloudDisplayedView('top');
                                  setIsIpcCloudVisible(true);
                                  setIsGoldenCloudVisible(true);
                                  setActiveHeightDiffComparisonView('separate');
                                }}
                              >
                                { activeHeightDiffComparisonView === 'separate' ?
                                  <img src='/img/icn/icn_separateHorizontally_blue.svg' className='w-[15px] h-[12px] shrink' alt='separate' />
                                  :
                                <img src='/img/icn/icn_horizontallySeparate_white.svg' className='w-[15px] h-[12px] shrink' alt='separate' />
                                }
                              </div>
                            </div>
                            <Select
                              popupMatchSelectWidth={false}
                              style={{ width: '120px' }}
                              value={pointCloudDisplayedView}
                              onChange={(value) => setPointCloudDisplayedView(value)}
                              options={[
                                // {
                                //   label: <div className='flex h-[26px] py-1 px-3 items-center gap-2 self-stretch'>
                                //     <img src='/img/icn/icn_perspective_white.svg' className='w-[12px] h-[12px] shrink' alt='perspective' />
                                //     <span className='font-source text-[12px] font-normal'>
                                //       {translation('viewInspection.perspective')}
                                //     </span>
                                //   </div>,
                                //   value: 'perspective',
                                // },
                                {
                                  label: <div className='flex h-[26px] py-1 px-3 items-center gap-2 self-stretch'>
                                    <img src='/img/icn/icn_topView_white.svg' className='w-[12px] h-[12px] shrink' alt='top' />
                                    <span className='font-source text-[12px] font-normal'>
                                      {translation('viewInspection.top')}
                                    </span>
                                  </div>,
                                  value: 'top',
                                },
                                {
                                  label: <div className='flex h-[26px] py-1 px-3 items-center gap-2 self-stretch'>
                                    <img src='/img/icn/icn_frontView_white.svg' className='w-[12px] h-[12px] shrink' alt='front' />
                                    <span className='font-source text-[12px] font-normal'>
                                      {translation('viewInspection.front')}
                                    </span>
                                  </div>,
                                  value: 'front',
                                },
                                {
                                  label: <div className='flex h-[26px] py-1 px-3 items-center gap-2 self-stretch'>
                                    <img src='/img/icn/icn_backView_white.svg' className='w-[12px] h-[12px] shrink' alt='back' />
                                    <span className='font-source text-[12px] font-normal'>
                                      {translation('viewInspection.back')}
                                    </span>
                                  </div>,
                                  value: 'back',
                                },
                                {
                                  label: <div className='flex h-[26px] py-1 px-3 items-center gap-2 self-stretch'>
                                    <img src='/img/icn/icn_leftView_white.svg' className='w-[12px] h-[12px] shrink' alt='left' />
                                    <span className='font-source text-[12px] font-normal'>
                                      {translation('viewInspection.left')}
                                    </span>
                                  </div>,
                                  value: 'left',
                                },
                                {
                                  label: <div className='flex h-[26px] py-1 px-3 items-center gap-2 self-stretch'>
                                    <img src='/img/icn/icn_rightView_white.svg' className='w-[12px] h-[12px] shrink' alt='right' />
                                    <span className='font-source text-[12px] font-normal'>
                                      {translation('viewInspection.right')}
                                    </span>
                                  </div>,
                                  value: 'right',
                                },
                              ]}
                            />
                            {/* <Button
                              onClick={() => setPointCloudDisplayedView('top')}
                            >
                              <span className='font-source text-[12px] font-normal'>
                                {translation('common.resetView')}
                              </span>
                            </Button> */}
                          </div>
                          <div className='flex flex-col flex-1 items-center justify-center h-full w-full'>
                            { activeHeightDiffComparisonView === 'separate' &&
                              <HeightDiffComparison
                                goldenCloudUri={_.get(selectedFeature, 'cropped_point_cloud_uri', '')}
                                ipcCloudUri={_.get(selectedComponentLineItem, 'cropped_point_cloud_uri', '')}
                                // inspectionInfo={inspectionInfo}
                                goldenProduct={goldenProduct}
                                isIpcCloudVisible={isIpcCloudVisible}
                                isGoldenCloudVisible={isGoldenCloudVisible}
                                setIsIpcCloudVisible={setIsIpcCloudVisible}
                                setIsGoldenCloudVisible={setIsGoldenCloudVisible}
                                pointCloudDisplayedView={pointCloudDisplayedView}
                              />
                            }
                            { activeHeightDiffComparisonView === 'stacked' &&
                              <HeightDiffStacked
                                goldenCloudUri={_.get(selectedFeature, 'cropped_point_cloud_uri', '')}
                                ipcCloudUri={_.get(selectedComponentLineItem, 'cropped_point_cloud_uri', '')}
                                // inspectionInfo={inspectionInfo}
                                goldenProduct={goldenProduct}
                                isIpcCloudVisible={isIpcCloudVisible}
                                isGoldenCloudVisible={isGoldenCloudVisible}
                                setIsIpcCloudVisible={setIsIpcCloudVisible}
                                setIsGoldenCloudVisible={setIsGoldenCloudVisible}
                                pointCloudDisplayedView={pointCloudDisplayedView}
                              />
                            }
                          </div>
                        </div>
                      </Fragment>
                    }
                    { selectedDetail === defectDetection &&
                      <div className='flex flex-1 self-stretch items-start gap-2'>
                        <div className='flex px-4 flex-col items-center gap-2 self-stretch flex-1'>
                          <div className='flex justify-between items-center w-full'>
                            <span className='font-source text-[12px] font-normal self-stretch'>
                              {translation('viewInspection.sample')}
                            </span>
                          </div>
                          <div className='flex flex-col flex-1 self-stretch items-start justify-center h-full'>
                            <div className='relative w-full h-full'>
                              {/* <img
                                src={inferenceProductCroppedSrc}
                                className='object-contain absolute top-0 left-0 z-[1]'
                                alt='sample'
                                style={{
                                  height: 'calc(100vh - 464px)',
                                  width: '100%',
                                  filter: `brightness(${curDisplayOptionsBrightness <= 50 ? curDisplayOptionsBrightness*2 : mapRange(curDisplayOptionsBrightness, 50, 100, 100, 800)}%) 
                                  contrast(${curDisplayOptionsContrast <= 50 ? (curDisplayOptionsContrast)*2 : mapRange(curDisplayOptionsContrast, 50, 100, 100, 800)}%) 
                                  saturate(${curDisplayOptionsSaturation <= 50 ? (curDisplayOptionsSaturation)*2 : mapRange(curDisplayOptionsSaturation, 50, 100, 100, 800)}%)`
                                }}
                              /> */}
                              { isDrawMaskEnabled &&
                                <Fragment>
                                  <div className='absolute top-0 left-0 z-[3] w-full h-full bg-[#000]'>
                                    <ComponentReviewDrawMaskViewer
                                      displayCanvasRef={drawMaskCanvasRef}
                                      ref={drawMaskViewerRef}
                                      drawingMode={drawingMode}
                                      pencilStrokeWidth={pencilStrokeWidth}
                                      pencilStrokeColor={pencilStrokeColor}
                                      curDisplayOptionsBrightness={curDisplayOptionsBrightness}
                                      curDisplayOptionsContrast={curDisplayOptionsContrast}
                                      curDisplayOptionsSaturation={curDisplayOptionsSaturation}
                                      isSharpnessEnabled={isSharpnessEnabled}
                                    />
                                  </div>
                                  <div className={`absolute bottom-4 left-4 z-[4]`}>
                                    <div
                                      className='flex w-[232px] py-2 px-4 flex-col items-start gap-2 rounded-[4px]'
                                      style={{
                                        background: 'rgba(0, 0, 0, 0.70)',
                                        boxShadow: '0px 0px 20px 0px rgba(0, 0, 0, 0.25)'
                                      }}
                                    >
                                      <div className='flex items-start gap-1 self-stretch'>
                                        <div className='flex p-0.5 items-center'>
                                          <img className='w-4 h-4' src='/img/icn/icn_info_white.svg' alt='info' />
                                        </div>
                                        <div className='flex items-center gap-1 self-stretch flex-wrap'>
                                          <span className='font-source text-[12px] font-bold'>
                                            {drawingMode === 'pencil' && translation('viewInspection.pencilTool')}
                                            {drawingMode === 'eraser' && translation('viewInspection.eraserTool')}
                                            {drawingMode === 'pan' && translation('viewInspection.panTool')}
                                          </span>
                          
                                          <span className='font-source text-[12px] font-normal'>
                                            {drawingMode === 'pencil' && translation('viewInspection.pencilToolDesc')}
                                            {drawingMode === 'eraser' && translation('viewInspection.eraserToolDesc')}
                                            {drawingMode === 'pan' && translation('viewInspection.panToolDesc')}
                                          </span>
                                        </div>
                                      </div>
                                      <div className='flex px-6 items-start gap-2 self-stretch'>
                                        <div
                                          className={`flex w-8 h-8 items-center justify-center cursor-pointer ${drawingMode === 'pan' ? 'bg-[#2D2D2D]' : 'bg-[#000000]'} rounded-[4px]`}
                                          onClick={() => setDrawingMode('pan')}
                                        >
                                          {drawingMode === 'pan' ?
                                            <img className='w-3 h-3' src='/img/icn/icn_cursorPointe_blue.svg' alt='pencil' />
                                            : <img className='w-3 h-3' src='/img/icn/icn_cursorPointer_white.svg' alt='pencil' />
                                          }
                                        </div>
                                        <div
                                          className={`flex w-8 h-8 items-center justify-center cursor-pointer ${drawingMode === 'pencil' ? 'bg-[#2D2D2D]' : 'bg-[#000000]'} rounded-[4px]`}
                                          onClick={() => setDrawingMode('pencil')}
                                        >
                                          {drawingMode === 'pencil' ?
                                            <img className='w-3 h-3' src='/img/icn/icn_pencil_blue.svg' alt='pencil' />
                                            : <img className='w-3 h-3' src='/img/icn/icn_pencil_white.svg' alt='pencil' />
                                          }
                                        </div>
                                        <div
                                          className={`flex w-8 h-8 items-center justify-center cursor-pointer ${drawingMode === 'eraser' ? 'bg-[#2D2D2D]' : 'bg-[#000000]'} rounded-[4px]`}
                                          onClick={() => setDrawingMode('eraser')}
                                        >
                                          {drawingMode === 'eraser' ?
                                            <img className='w-3 h-3' src='/img/icn/icn_erase_blue.svg' alt='eraser' />
                                            : <img className='w-3 h-3' src='/img/icn/icn_erase_white.svg' alt='eraser' />
                                          }
                                        </div>
                                        <div className='flex w-8 h-8 items-center justify-center cursor-pointer'
                                          onClick={() => {
                                            if (drawMaskViewerRef.current) drawMaskViewerRef.current.resetView();
                                          }}
                                        >
                                          <img className='w-3 h-3' src='/img/icn/icn_resetZoom_white.svg' alt='pencil' />
                                        </div>
                                        <Dropdown
                                          overlay={<InferenceImageDisplayOptions
                                            curDisplayOptionsBrightness={curDisplayOptionsBrightness}
                                            setCurDisplayOptionsBrightness={setCurDisplayOptionsBrightness}
                                            curDisplayOptionsContrast={curDisplayOptionsContrast}
                                            setCurDisplayOptionsContrast={setCurDisplayOptionsContrast}
                                            curDisplayOptionsSaturation={curDisplayOptionsSaturation}
                                            setCurDisplayOptionsSaturation={setCurDisplayOptionsSaturation}
                                            isSharpnessEnabled={isSharpnessEnabled}
                                            setIsSharpnessEnabled={setIsSharpnessEnabled}
                                          />}
                                          placement='topLeft'
                                        >
                                          <div className='flex w-8 h-8 items-center justify-center'>
                                            <img className='w-3 h-3' src='/img/icn/icn_setting_white.svg' alt='reset' />
                                          </div>
                                        </Dropdown>
                                      </div>
                                      <div className='w-full h-[1px] bg-[#333]' />
                                      <span className='font-source text-[12px] font-semibold self-stretch'>
                                        {translation('viewInspection.stroke')}
                                      </span>
                                      <div className='flex flex-col items-start self-stretch'>
                                        <div className='flex py-0.5 items-center gap-2 self-stretch'>
                                          <div className='flex w-6 h-6 items-center justify-center gap-2.5'>
                                            <img src='/img/icn/icn_zond_white.svg' alt='zond' className='w-3 h-3' />
                                          </div>
                                          <InputNumber
                                            controls={false}
                                            min={0.01}
                                            step={0.01}
                                            max={100}
                                            size='small'
                                            style={{ width: '84px' }}
                                            value={pencilStrokeWidth}
                                            onChange={(value) => ********************(value)}
                                          />
                                          <Slider
                                            min={0.01}
                                            max={100}
                                            step={0.01}
                                            style={{ width: '84px' }}
                                            value={pencilStrokeWidth}
                                            onChange={(value) => ********************(value)}
                                          />
                                        </div>
                                        <div className='flex py-0.5 items-center gap-2 self-stretch'>
                                          <div className='flex w-6 h-6 items-center justify-center gap-2.5'>
                                            <ColorPicker
                                              size='small'
                                              value={pencilStrokeColor}
                                              onChange={(color) => {
                                                setPencilStrokeColor(color.toHexString());
                                              }}
                                            />
                                          </div>
                                          <Input
                                            size='small'
                                            style={{ width: '76px' }}
                                            disabled
                                            value={pencilStrokeColor}
                                          />
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </Fragment>
                              }
                              <StaticImageTag
                                dataUri={_.get(selectedComponentLineItem, 'cropped_color_map_uri', '')}
                                className='object-contain absolute top-0 left-0 z-[2]'
                                alt='sample'
                                style={{
                                  // height: 'calc(100vh - 464px - 26px - 26px - 54px - 24px)',
                                  height: 'calc(100vh - 464px - 26px - 26px - 26px)',
                                  // height: 'calc(100% - 50px)',
                                  width: '100%',
                                  filter: `brightness(${curDisplayOptionsBrightness <= 50 ? curDisplayOptionsBrightness*2 : mapRange(curDisplayOptionsBrightness, 50, 100, 100, 800)}%) 
                                  contrast(${curDisplayOptionsContrast <= 50 ? (curDisplayOptionsContrast)*2 : mapRange(curDisplayOptionsContrast, 50, 100, 100, 800)}%) 
                                  saturate(${curDisplayOptionsSaturation <= 50 ? (curDisplayOptionsSaturation)*2 : mapRange(curDisplayOptionsSaturation, 50, 100, 100, 800)}%)`
                                }}
                              />
                              {/* {showDlOutputMask && !_.isEmpty(defectMapSrc) &&
                                <img
                                  src={defectMapSrc}
                                  className='object-contain absolute top-0 left-0 z-[2]'
                                  style={{
                                    height: 'calc((100vh - 388px - 200px)/2)',
                                    width: '100%',
                                  }}
                                  alt='defect'
                                />
                              } */}
                            </div>
                          </div>
                          {/* <div className='flex flex-col gap-1 self-stretch items-start'>
                            <div className='flex py-1 gap-2 items-center self-stretch'>
                              <Checkbox onChange={(e) => setShowDlOutputMask(e.target.checked)} />
                              <span className='font-source text-[12px] font-normal'>
                                {translation('viewInspection.displayDefectHeatmap')}
                              </span>
                            </div>
                            <div className='flex py-1 flex-col items-start self-stretch gap-1'>
                              <div
                                className='h-1 self-stretch w-full rounded-[2px]'
                                style={{ background: 'linear-gradient(90deg, #FF0606 0%, #F2FF00 40%, rgba(255, 255, 255, 0.00) 98.5%)' }}
                              />
                              <div className='flex self-stretch items-center justify-between'>
                                <span className='font-source text-[12px] font-normal'>
                                  {translation('viewInspection.highlyDefective')}
                                </span>
                                <span className='font-source text-[12px] font-normal'>
                                  {translation('viewInspection.notDefective')}
                                </span>
                              </div>
                            </div>
                          </div> */}
                        </div>
                        <div className='flex px-4 flex-col items-center flex-1 gap-2 self-stretch'>
                          <div className='flex justify-between items-center w-full'>
                            <span className='font-source text-[12px] font-normal self-stretch'>
                              {translation('common.goldenProduct')}
                            </span>
                          </div>
                          {/* <div className='flex flex-col flex-1 self-stretch items-start justify-center h-full'> */}
                            {/* <img
                              src={goldenProductCroppedSrc}
                              className='object-contain'
                              alt='golden'
                              style={{
                                height: 'calc(100vh - 464px)',
                                width: '100%',
                                filter: `brightness(${curDisplayOptionsBrightness <= 50 ? curDisplayOptionsBrightness*2 : mapRange(curDisplayOptionsBrightness, 50, 100, 100, 800)}%) 
                                contrast(${curDisplayOptionsContrast <= 50 ? (curDisplayOptionsContrast)*2 : mapRange(curDisplayOptionsContrast, 50, 100, 100, 800)}%) 
                                saturate(${curDisplayOptionsSaturation <= 50 ? (curDisplayOptionsSaturation)*2 : mapRange(curDisplayOptionsSaturation, 50, 100, 100, 800)}%)`
                              }}
                            /> */}
                            <StaticImageTag
                              dataUri={_.get(selectedFeature, 'cropped_color_map_uri', '')}
                              className='object-contain'
                              alt='sample'
                              style={{
                                // height: 'calc(100vh - 464px - 26px - 26px - 54px - 24px)',
                                height: 'calc(100vh - 464px - 26px - 26px - 26px)',
                                // height: 'calc(100% - 50px)',
                                width: '100%',
                                filter: `brightness(${curDisplayOptionsBrightness <= 50 ? curDisplayOptionsBrightness*2 : mapRange(curDisplayOptionsBrightness, 50, 100, 100, 800)}%) 
                                contrast(${curDisplayOptionsContrast <= 50 ? (curDisplayOptionsContrast)*2 : mapRange(curDisplayOptionsContrast, 50, 100, 100, 800)}%) 
                                saturate(${curDisplayOptionsSaturation <= 50 ? (curDisplayOptionsSaturation)*2 : mapRange(curDisplayOptionsSaturation, 50, 100, 100, 800)}%)`
                              }}
                            />
                          {/* </div> */}
                          <Button
                            type='text'
                            style={{ width: '100%' }}
                            onClick={() => props.history.push(`/aoi/edit-product/${goldenProductId}?editStep=1&detectionStep${_.get(selectedComponentLineItem, 'step', 0)}&featureId=${selectedFeatureId}`)}
                          >
                            <span className='font-source text-[12px] font-semibold text-AOI-blue'>
                              {translation('viewInspection.viewGoldenProduct')}
                            </span>
                          </Button>
                        </div>
                      </div>
                    }
                  </div>
                </div>
                </Fragment>
              }
              </Fragment>
              }
            </div>
          </div>
        </div>
      </div>
    </MainMenuLayout>
    </Fragment>
  );
};

const SingleComponentCard = (props) => {
  const {
    imageUri,
    displayFeatureType,
    timestamp,
    selected,
    onClick,
    displayProductId,
    reevaluateResult,
    lineItemObject,
  } = props;

  const [src, setSrc] = useState(null);

  const latestRetrainFinishTimeByModelType = useSelector((state) => state.setting.latestRetrainFinishTimeByModelType);

  useEffect(() => {
    if (_.isEmpty(imageUri)) return;

    const fetchImage = async (uri) => {
      const res = await fetch(`${serverEndpoint}/data?data_uri=${uri}`);
      const blob = await res.blob();
      const reader = new FileReader();
      reader.readAsDataURL(blob);
      const newSrc = await new Promise((resolve) => {
        reader.onloadend = () => {
          resolve(reader.result);
        };
      });
      setSrc(newSrc);
    };

    fetchImage(imageUri);
  }, [imageUri]);

  return (
    <div
      className={`flex flex-col gap-0.5 p-1 justify-center items-center self-stretch rounded-[4px] ${selected && 'bg-[#56ccf21a]'} 
      cursor-pointer hover:bg-[#56ccf21a] transition-all duration-300`}
      onClick={onClick}
    >
      <img src={src} className='w-[94px] h-[94px] object-contain' alt='component' />
      <div className='flex py-1 px-2 flex-col gap-1 items-center'>
        <span className={`font-source text-[10px] font-normal ${
          (!_.isEmpty(reevaluateResult) && isRevaluateTimeLaterThanLatestRetrainFinishTime(lineItemObject, latestRetrainFinishTimeByModelType)) ? (
            _.get(reevaluateResult, 'pass', false) ? 'text-AOI-green' : 'text-red'
          ) :
          selected ? 'text-AOI-blue' : 'text-white'
        }`}>
          {displayFeatureType}
        </span>
        <span className={`font-source text-[10px] font-normal ${selected ? 'text-AOI-blue' : 'text-white'}`}>
          {displayProductId}
        </span>
      </div>
    </div>
  );
};

const InferenceImageDisplayOptions = (props) => {
  const {
    curDisplayOptionsBrightness,
    setCurDisplayOptionsBrightness,
    curDisplayOptionsContrast,
    setCurDisplayOptionsContrast,
    curDisplayOptionsSaturation,
    setCurDisplayOptionsSaturation,
    isSharpnessEnabled,
    setIsSharpnessEnabled,
  } = props;

  return (
    <div className='flex p-2 flex-col gap-2 w-[220px] bg-[#2D2D2D] rounded-[4px]'>
      <div className='flex justify-between items-center gap-2'>
        <span className='font-source text-[12px] font-semibold'>
          {translation('viewInspection.displayOptions')}
        </span>
        <div
          className='flex items-center justify-center h-8 w-8 cursor-pointer'
          onClick={() => {
            setCurDisplayOptionsBrightness(50);
            setCurDisplayOptionsContrast(50);
            setCurDisplayOptionsSaturation(50);
            setIsSharpnessEnabled(false);
          }}
        >
          <img src='/img/icn/icn_reset_white.svg' alt='reset' className='w-3 h-3' />
        </div>
      </div>
      <div className='flex flex-col self-stretch'>
        <span className='font-source text-[12px] font-normal'>
          {translation('viewInspection.brightness')}
        </span>
        <Slider
          value={curDisplayOptionsBrightness}
          min={0}
          max={100}
          step={1}
          onChange={(value) => setCurDisplayOptionsBrightness(value)}
        />
      </div>
      <div className='flex flex-col self-stretch'>
        <span className='font-source text-[12px] font-normal'>
          {translation('viewInspection.contrast')}
        </span>
        <Slider
          value={curDisplayOptionsContrast}
          min={0}
          max={100}
          step={1}
          onChange={(value) => setCurDisplayOptionsContrast(value)}
        />
      </div>
      <div className='flex flex-col self-stretch'>
        <span className='font-source text-[12px] font-normal'>
          {translation('viewInspection.saturation')}
        </span>
        <Slider
          value={curDisplayOptionsSaturation}
          min={0}
          max={100}
          step={1}
          onChange={(value) => setCurDisplayOptionsSaturation(value)}
        />
      </div>
      <div className='flex flex-col self-stretch'>
        <div className='flex items-center gap-2'>
          <span className='font-source text-[12px] font-normal'>
            {translation('viewInspection.sharpness')}
          </span>
          <Tooltip
            title={translation('viewInspection.sharpnessDesc')}
          >
            <img src='/img/icn/icn_info_white.svg' alt='info' className='w-3 h-3' />
          </Tooltip>
        </div>
        <div className='flex items-center justify-between'>
          <Switch
            size='small'
            checked={isSharpnessEnabled}
            onChange={(checked) => setIsSharpnessEnabled(checked)}
          />
        </div>
      </div>
    </div>
  );
};

const CustomTabs = styled(Tabs)`
  .ant-tabs-nav {
    margin: 0;
  }
`;

export default ComponentReview;