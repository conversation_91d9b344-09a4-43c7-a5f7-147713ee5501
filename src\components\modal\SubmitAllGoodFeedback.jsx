import { Button, Modal, Select } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { handleRequestFailed, translation } from '../../common/util';
import _ from 'lodash';
import { CustomCollapse } from '../../common/darkModeComponents';
import { ALERT_TYPES, aoiAlert } from '../../common/alert';
import MarkAllFeedbackAsGoodGoldenViewer from '../common/viewer/MarkAllFeedbackAsGoodGoldenViewer';
import { serverEndpoint } from '../../common/const';


const SubmitAllGoodFeedback = (props) => {
  const {
    isOpened,
    setIsOpened,
    productInfo,
    goldenProdFeatures,
    handleSubmit,
  } = props;

  const viewerRef = useRef();
  const canvasRef = useRef();

  const [selectedVariant, setSelectedVariant] = useState('');
  const [showMoreClasses, setShowMoreClasses] = useState(false);
  const [goldenDataUrl, setGoldenDataUrl] = useState('');
  const [selectedVariantFeatures, setSelectedVariantFeatures] = useState([]);

  useEffect(() => {
    if (_.isEmpty(selectedVariant) || _.isEmpty(goldenProdFeatures)) return;
    const selectedInspectable = _.find(_.get(productInfo, 'inspectables', []), i => i.variant === selectedVariant);
    if (!selectedInspectable || !_.get(selectedInspectable, 'color_map_uri', '')) return;
    
    const fetchAndLoad = async (
      uri,
      features,
      setGoldenDataUrl,
      setSelectedVariantFeatures,
    ) => {
      let res;
      try {
        res = await fetch(`${serverEndpoint}/data?data_uri=${uri}`);
      } catch (e) {
        handleRequestFailed('fetchColorMap', e); 
        return;
      }

      const blob = await res.blob();
      const reader = new FileReader();
      reader.readAsDataURL(blob);
      const dataurl = await new Promise((resolve) => {
        reader.onload = (event) => {
          resolve(event.target.result);
        };
      });

      setSelectedVariantFeatures(features);
      setGoldenDataUrl(dataurl);

      // viewerRef.current.loadScene(goldenDataUrl, features, 438, 406);
    };

    fetchAndLoad(
      _.get(selectedInspectable, 'color_map_uri', ''),
      _.filter(goldenProdFeatures, f => f.variant === selectedVariant),
      setGoldenDataUrl,
      setSelectedVariantFeatures,
    );
  }, [goldenProdFeatures, selectedVariant, productInfo]);

  return (
    <Modal
      open={isOpened}
      onCancel={() => setIsOpened(false)}
      title={translation('markAllFeedbackAsGood.markAllFeedbackAsGood')}
      footer={
        <div className='flex p-4 items-center gap-2 self-stretch flex-1'>
          <Button
            style={{ width: '50%' }}
            onClick={() => setIsOpened(false)}
          >
            <span className='font-source text-[14px] font-normal'>
              {translation('common.cancel')}
            </span>
          </Button>
          <Button
            style={{ width: '50%' }}
            onClick={() => {
              if (_.isEmpty(selectedVariant)) {
                aoiAlert(translation('notification.error.selectAVariantionFirst'), ALERT_TYPES.COMMON_ERROR);
                return;
              }
              handleSubmit(selectedVariant);
              setIsOpened(false);
            }}
          >
            <span className='font-source text-[14px] font-semibold'>
              {translation('common.save')}
            </span>
          </Button>
        </div>
      }
    >
      <div className='flex flex-col py-6 px-4 items-start gap-8 self-stretch'>
        <div className='flex flex-col items-start gap-2 self-stretch'>
          <span className='text-source text-[14px] font-normal'>
            {translation('markAllFeedbackAsGood.pleaseSelectAVariantForThisProduct')}
          </span>
          <div className='flex items-center gap-6 self-stretch flex-1'>
            <span className='font-source text-[12px] font-normal'>
              {_.get(productInfo, 'product_name', '')}
            </span>
            <div className='flex flex-col items-start gap-2 self-stretch flex-1'>
              <Select
                style={{ width: '100%' }}
                options={_.map(_.get(productInfo, 'inspectables', []), (i) => ({ label: _.get(i, 'variant'), value: _.get(i, 'variant') }))}
                value={selectedVariant}
                onChange={(value) => setSelectedVariant(value)}
              />
            </div>
          </div>
        </div>
        <div className='flex flex-col items-start gap-2 self-stretch'>
          <span className='font-source text-[12px] font-normal'>
            {translation('markAllFeedbackAsGood.componentListOf')}
          </span>
          <div className='flex flex-col items-start gap-2 self-stretch'>
            <div className='flex flex-col items-start gap-0.5 self-stretch'>
              <div
                className='flex gap-2 items-start gap-[14px] self-stretch rounded-[4px] p-2'
                style={{
                  background: 'rgba(0, 0, 0, 0.20)'
                }}
              >
                <span className={`font-source text-[12px] font-normal text-pretty overflow-hidden transition-all duration-300 ease-in-out flex-1
                   ${showMoreClasses ? 'max-h-[18.84px]' : 'max-h-screen'}`}>
                  {!_.isEmpty(selectedVariant) ? _.join(_.map(_.filter(goldenProdFeatures, f => f.variant === selectedVariant), (f) => (
                    _.get(f, 'feature_scope') === 'global' ? _.get(f, 'feature_type') : _.get(f, 'feature_type', '_empty class').substring(1)
                  )), ', ') : ''}
                </span>
                <span className='w-[70px] font-source text-[12px] font-normal italic text-AOI-blue text-pretty cursor-pointer' onClick={() => setShowMoreClasses(!showMoreClasses)}>
                  {showMoreClasses ? translation('markAllFeedbackAsGood.showLess') : `${translation('markAllFeedbackAsGood.viewAll')}(${_.filter(goldenProdFeatures, f => f.variant === selectedVariant).length})`}
                </span>
              </div>
              <CustomCollapse
                destroyInactivePanel={true}
                style={{ width: '100%' }}
                onChange={(keys) => {
                  if (keys[0] === 'quickPreview') {
                    viewerRef.current?.loadScene();
                  }
                }}
                items={[{
                  key: 'quickPreview',
                  label: <div className='flex gap-2 items-center h-[22px]'>
                    <img src='/img/icn/icn_image_white.svg' alt='image' className='w-4 h-4' />
                    <span className='font-source text-[12px] font-normal'>
                      {translation('markAllFeedbackAsGood.imageQuickView')}
                    </span>
                  </div>,
                  children: <div className='w-[438px] h-[406px]'>
                    <MarkAllFeedbackAsGoodGoldenViewer
                      ref={viewerRef}
                      displayCanvasRef={canvasRef}
                      goldenDataUrl={goldenDataUrl}
                      features={selectedVariantFeatures}
                    />
                  </div>,
                }]}
              />
            </div>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default SubmitAllGoodFeedback;