import thunk from 'redux-thunk';
import { connectRouter, routerMiddleware } from 'connected-react-router';
import history from './history';
import { persistCombineReducers, persistStore } from 'redux-persist';
import storage from 'redux-persist/lib/storage';
import { configureStore, createListenerMiddleware } from '@reduxjs/toolkit';
import settingReducer from './actions/setting';
import { productApi } from './services/product';
import cameraReducer from './actions/camera';
import { authApi } from './services/auth';
import _ from 'lodash';
import { systemApi } from './services/system';
import { sessionApi } from './services/session';
import { cameraApi } from './services/camera';
import productReducer from './actions/product';
import { featureApi } from './services/feature';
import { frameApi } from './services/frame';
import createFilter, { createBlacklistFilter } from 'redux-persist-transform-filter';
import { modelApi } from './services/model';
import { calibrationApi } from './services/calibration';


// ref: https://stackoverflow.com/questions/36730793/can-i-dispatch-an-action-in-reducer
// This middleware will just add the property "asyncDispatch - async dispatch" to all actions
const asyncDispatchMiddleware =
  ({ dispatch, getState }) =>
  (next) =>
  (action) => {
    let syncActivityFinished = false;
    let actionQueue = [];

    function flushQueue() {
      actionQueue.forEach((a) => dispatch(a)); // flush queue
      actionQueue = [];
    }

    function asyncDispatch(asyncAction) {
      actionQueue = actionQueue.concat([asyncAction]);

      if (syncActivityFinished) {
        flushQueue();
      }
    }

    const actionWithAsyncDispatch = Object.assign({}, action, {
      asyncDispatch,
    });

    const res = next(actionWithAsyncDispatch);

    syncActivityFinished = true;
    flushQueue();

    return res;
  };

// For user authentication local storage side effects
const loginListenerMiddleware = createListenerMiddleware();
loginListenerMiddleware.startListening({
  matcher: authApi.endpoints.login.matchFulfilled,
  effect: (action) => {
    const {
      payload: { email, token },
    } = action;

    if (!_.isEmpty(token) && !_.isEmpty(email)) {
      localStorage.setItem('accessToken', `Bearer ${token}`);
      localStorage.setItem('userEmail', email);
      localStorage.setItem('accessTokenTimestamp', new Date().getTime());
    }
  },
});

const signupListenerMiddleware = createListenerMiddleware();
signupListenerMiddleware.startListening({
  matcher: authApi.endpoints.signup.matchFulfilled,
  effect: (action) => {
    const {
      payload: { email, token },
    } = action;

    if (!_.isEmpty(token) && !_.isEmpty(email)) {
      localStorage.setItem('accessToken', `Bearer ${token}`);
      localStorage.setItem('userEmail', email);
      localStorage.setItem('accessTokenTimestamp', new Date().getTime());
    }
  },
});

const middleware = [];
middleware.push(
  thunk,
  routerMiddleware(history),
  asyncDispatchMiddleware,
  loginListenerMiddleware.middleware,
  signupListenerMiddleware.middleware,
  // siteApi.middleware
  productApi.middleware,
  systemApi.middleware,
  sessionApi.middleware,
  cameraApi.middleware,
  featureApi.middleware,
  authApi.middleware,
  frameApi.middleware,
  modelApi.middleware,
  calibrationApi.middleware,
);

// presist specific keys
const settingFilter = createFilter('setting', [
  'isStayAtLeatestEnabled',
  'isInferenceRunning',
  'productVariationDefineModalDisabled',
  'productPassRates',
  'viewInspectionViewMode',
  'allowedNGAmount',
  'curRunningIpcSessionIds',
  'isTrainingRuning',
  'curTrainingTaskStartTime',
  'todayTotalManualChangeOffset',
  'todayGoodManualChangeOffset',
  'prevResetIpcCountDate',
  'dailyIpcCountResetTime',
  'latestRetrainFinishTimeByModelType',
  'isViewLiveFullScreenEnabled',
  'userType',
]);

const persistConf = {
  key: 'root',
  storage,
  blacklist: [
    productApi.reducerPath,
    systemApi.reducerPath,
    sessionApi.reducerPath,
    cameraApi.reducerPath,
    featureApi.reducerPath,
    frameApi.reducerPath,
    modelApi.reducerPath,
    calibrationApi.reducerPath,
    'camera',
    'product',
    // 'setting',
  ],
  transforms: [
    settingFilter,
    // filter
  ],
};

const persistReducer = persistCombineReducers(persistConf, {
  router: connectRouter(history),
  setting: settingReducer,
  // [siteApi.reducerPath]: siteApi.reducer,
  [productApi.reducerPath]: productApi.reducer,
  [systemApi.reducerPath]: systemApi.reducer,
  [sessionApi.reducerPath]: sessionApi.reducer,
  [cameraApi.reducerPath]: cameraApi.reducer,
  [featureApi.reducerPath]: featureApi.reducer,
  [frameApi.reducerPath]: frameApi.reducer,
  [modelApi.reducerPath]: modelApi.reducer,
  [calibrationApi.reducerPath]: calibrationApi.reducer,
  camera: cameraReducer,
  product: productReducer,
});

const store = configureStore({
  reducer: persistReducer,
  middleware: middleware,
  devTools: true,
  preloadedState: undefined,
});

const persistor = persistStore(store);

export { store, persistor };