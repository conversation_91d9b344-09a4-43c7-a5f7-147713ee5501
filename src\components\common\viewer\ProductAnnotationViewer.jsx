import { fabric } from 'fabric-with-erasing';
import _ from 'lodash';
import React from 'react';
import { baseBboxStrokeWidth, serverEndpoint, defineProductBboxBoundaryLengthLimit, featureType } from '../../../common/const';
import { debounce, getColorByStr, sleep, translation } from '../../../common/util';
import TwoDBaseViwer from './TwoDBaseViewer';
import { getTwoDRectPminPmax } from './util';
import { ALERT_TYPES, aoiAlert } from '../../../common/alert';


export default class ProductAnnotationViewer extends TwoDBaseViwer {
  constructor(props) {
    super(props, props.displayCanvasRef);
    this.displayCanvasRef = props.displayCanvasRef;
    this.scene = null; // 2d image
    this.fabricCanvas = null;
    this.sceneOriginalWidth = 0;
    this.sceneOriginalHeight = 0;
    this.curMode = 'select'; // select, draw
    this.curRect = null; // current drawing rect
    this.rects = []; // all drawing rects
    this.sceneLocked = false;
    this.selectedFeatureId = null;
    this.handleWindowResize = React.createRef();
    this.containerRef = React.createRef();
    this.curRectInitMousePos = null;
    this.initZoomPanToProductFeature = false;
  };

  state = {
    curBoxWidth: 0,
    curBoxHeight: 0,
    curMouseTop: 0,
    curMouseLeft: 0,
  };

  componentDidMount() {
    if (!this.displayCanvasRef.current) return;
    const { handleRectClickInRemoveMode } = this.props;

    // init fabric canvas
    const fabricCanvas = new fabric.Canvas(
      this.displayCanvasRef.current,
      {
        // Disable uniform scaling
        uniformScaling: false,
      }
    );
    this.fabricCanvas = fabricCanvas;
    fabricCanvas.skipOffscreen = true;
    fabricCanvas.contextContainer.imageSmoothingEnabled = true;

    // enable webgl filter
    let filterBackend = null;
    try {
      filterBackend = new fabric.WebglFilterBackend();
      // console.log('Use WebGL filter backend');
    } catch (e) {
      // console.error('WebGL backend is not supported, using 2d canvas backend');
      filterBackend = new fabric.Canvas2dFilterBackend();
    }
    fabricCanvas.filterBackend = filterBackend;
    if (fabric.isWebglSupported()) {
      console.log('WebGL is supported, increase texture size to 65536');
      fabric.textureSize = 65536; // ow only partial image will be rendered if sharpness is enabled since our image is large
    }

    // Enable Panning
    let isPanning = false;

    fabricCanvas.on('mouse:down', (opt) => {
      // const pointer = fabricCanvas.getPointer(opt.e);
      // console.log('position', pointer.x, pointer.y);
      if (this.sceneLocked) return;

      if (this.curRect) this.removeCurRect();

      if (this.curMode === 'draw') {
        // create rect based on the scene's scale and position
        const pointer = fabricCanvas.getPointer(opt.e);
        // const x = pointer.x / this.scene.scaleX;
        // const y = pointer.y / this.scene.scaleY;
        const rect = new fabric.Rect({
          left: pointer.x,
          top: pointer.y,
          width: 0,
          height: 0,
          fill: 'transparent',
          stroke: 'red',
          strokeWidth: 1,
          selectable: false,
          strokeUniform: true, // Ensure stroke width remains consistent when scaling
        });
        this.curRectInitMousePos = {
          x: pointer.x,
          y: pointer.y,
        };
        rect.perPixelTargetFind = true;
        rect.targetFindTolerance = 10;
        this.curRect = rect;
        fabricCanvas.add(rect);
        this.updateSceneZIndex();
        this.curRect.moveTo(2);

        const mousePos = fabricCanvas.getPointer(opt.e, true);

        this.setState({
          ...this.state,
          curBoxWidth: 0,
          curBoxHeight: 0,
          curMouseTop: mousePos.y,
          curMouseLeft: mousePos.x,
        })
        return;
      }

      if (this.curMode === 'eraseObject' && opt.target) {
        handleRectClickInRemoveMode({
          feature_id: opt.target.get('feature_id'),
          product_id: opt.target.get('product_id'),
          step: opt.target.get('step'),
          variant: opt.target.get('variant'),
        });
        return;
      }

      this.updateSceneZIndex();

      if (opt.target) {
        isPanning = false;
        return;
      }
      
      isPanning = true;
      fabricCanvas.selection = false;
      fabricCanvas.setCursor('grab');
    });

    fabricCanvas.on('mouse:move', (opt) => {
      if (this.sceneLocked) return;
      if (isPanning && opt && opt.e) {
        fabricCanvas.setCursor('grab');
        const delta = new fabric.Point(opt.e.movementX, opt.e.movementY);
        fabricCanvas.relativePan(delta);
      }
      if (this.curMode === 'draw' && this.curRect && !_.isEmpty(this.curRectInitMousePos)) {
        // console.log(this.curRect.top, this.curRect.left, this.curRect.width, this.curRect.height);
        const { curRect } = this;
        const pointer = fabricCanvas.getPointer(opt.e);
        const mousePos = fabricCanvas.getPointer(opt.e, true);

        // NOTE: keep rect's width and height positive when drawing and adjust the top left
        // ow the rect's left and right will be hidden for some reason 2024/10/22
        if (_.get(this.curRectInitMousePos, 'x') > pointer.x) {
          curRect.set({
            left: pointer.x,
            width: this.curRectInitMousePos.x - pointer.x,
          })
        } else {
          curRect.set({
            width: pointer.x - this.curRectInitMousePos.x,
            left: this.curRectInitMousePos.x,
          });
        }

        if (_.get(this.curRectInitMousePos, 'y') > pointer.y) {
          curRect.set({
            top: pointer.y,
            height: this.curRectInitMousePos.y - pointer.y,
          });
        } else {
          curRect.set({
            height: pointer.y - this.curRectInitMousePos.y,
            top: this.curRectInitMousePos.y,
          });
        }
        
        this.updateSceneZIndex();
        curRect.moveTo(2);

        this.setState({
          ...this.state,
          curBoxWidth: this.curRect.width >= 0 ? this.curRect.width : -this.curRect.width,
          curBoxHeight: this.curRect.height >= 0 ? this.curRect.height : -this.curRect.height,
          // curMouseTop: this.curRect.height >= 0 ? mousePos.y : mousePos.y + this.curRect.height,
          // curMouseLeft: this.curRect.width >= 0 ? mousePos.x : mousePos.x + this.curRect.width,
          curMouseTop: mousePos.y,
          curMouseLeft: mousePos.x,
        });
      }
    });

    fabricCanvas.on('mouse:up', (opt) => {
      if (this.sceneLocked) return;
      if (this.curMode === 'draw' && this.curRect) {
        // check if the width and height are negative
        if (this.curRect.width < 0) {
          this.curRect.left += this.curRect.width;
          this.curRect.width *= -1;
        }
        if (this.curRect.height < 0) {
          this.curRect.top += this.curRect.height;
          this.curRect.height *= -1;
        }
        this.updateSceneZIndex();
        this.curRect.setCoords();

        this.setState({
          ...this.state,
          curBoxWidth: 0,
          curBoxHeight: 0,
          curMouseTop: 0,
          curMouseLeft: 0,
        });

        // check if each inner rect boundaries' length is greater or equal to 40 pixel
        // and fabric's rect width and height includes the stroke width...
        if ((this.curRect.width - this.curRect.strokeWidth) < defineProductBboxBoundaryLengthLimit || (this.curRect.height - this.curRect.strokeWidth) < defineProductBboxBoundaryLengthLimit) {
          aoiAlert(translation('notification.error.theMinBboxWeAcceptIs'), ALERT_TYPES.COMMON_ERROR);
          this.fabricCanvas.remove(this.curRect);
          this.curRect = null;
          return;
        }

        const { handleAddFeature } = this.props;
        const { pMax, pMin } = getTwoDRectPminPmax(this.curRect, this.curRect.strokeWidth);
        const pointer = fabricCanvas.getPointer(opt.e, true); // for the create feature popup position

        this.lockScene();

        handleAddFeature({
          pMin,
          pMax,
          mouseX: pointer.x,
          mouseY: pointer.y,
        });
      }
      isPanning = false;
      fabricCanvas.setCursor('default');
      this.fabricCanvas.renderAll();
    });

    // Enable Zooming
    fabricCanvas.on('mouse:wheel', (opt) => {
      const delta = opt.e.deltaY;
      let zoom = fabricCanvas.getZoom();
      zoom *= 0.999 ** delta;
      // if (zoom > 20) zoom = 20;
      // if (zoom < 0.01) zoom = 0.01;
      fabricCanvas.zoomToPoint({ x: opt.e.offsetX, y: opt.e.offsetY }, zoom);
      opt.e.preventDefault();
      opt.e.stopPropagation();

      if (this.rects.length === 0) return;

      const newWidth = Math.min(baseBboxStrokeWidth, baseBboxStrokeWidth / zoom);

      // update top, left, width, height
      // add stroke width delta to the top left width height
      const strokeWidthDelta = newWidth - this.rects[0].strokeWidth;

      if (strokeWidthDelta === 0) return;

      // update the rect stroke width
      this.rects.forEach((rect) => {
        rect.set({
          strokeWidth: newWidth,
          left: rect.left - strokeWidthDelta,
          top: rect.top - strokeWidthDelta,
          width: rect.width + strokeWidthDelta,
          height: rect.height + strokeWidthDelta,
        });

        // reposition the lock icon if exists
        if (rect.lockIcon) {
          rect.lockIcon.set({
            left: rect.left,
            top: rect.top - 12,
          });
        }
      });
      
      this.fabricCanvas.renderAll();
    });

    const updateCanvasSize = () => {
      if (!this.containerRef.current || !this.fabricCanvas) return;
      const canvasWidth = this.containerRef.current.offsetWidth;
      const canvasHeight = this.containerRef.current.offsetHeight;

      // const browserZoomLevel = window.devicePixelRatio;

      this.fabricCanvas.setWidth(canvasWidth);
      this.fabricCanvas.setHeight(canvasHeight);
      // this.fabricCanvas.lowerCanvasEl.width = canvasWidth;
      // this.fabricCanvas.lowerCanvasEl.height = canvasHeight;
      // this.fabricCanvas.upperCanvasEl.width = canvasWidth;
      // this.fabricCanvas.upperCanvasEl.height = canvasHeight;
      this.fabricCanvas.calcOffset();
      this.fabricCanvas.renderAll();
    };

    this.handleWindowResize.current = debounce(updateCanvasSize, 300);

    window.addEventListener('resize', this.handleWindowResize.current);

    if (!_.isEmpty(this.props.uri)) {
      this.loadScene(this.props.uri);
    }

    if (!_.isEmpty(this.props.features)) {
      this.loadFeatures(this.props.features, this.props.selectedVariation, this.props.componentsVisible, this.props.initialSelectedFeatureId);
    }
  };

  componentDidUpdate(prevProps) {
    if (prevProps.curDisplayOptionsBrightness !== this.props.curDisplayOptionsBrightness) this.updateSceneBrightness(this.props.curDisplayOptionsBrightness);
    if (prevProps.curDisplayOptionsContrast !== this.props.curDisplayOptionsContrast) this.updateSceneContrast(this.props.curDisplayOptionsContrast);
    if (prevProps.curDisplayOptionsSaturation !== this.props.curDisplayOptionsSaturation) this.updateSceneSaturation(this.props.curDisplayOptionsSaturation);
    if (prevProps.isSharpnessEnabled !== this.props.isSharpnessEnabled) this.updateSceneSharpness(this.props.isSharpnessEnabled);

    if (prevProps.uri !== this.props.uri) {
      this.loadScene(this.props.uri);
    }

    if (prevProps.selectedVariation !== this.props.selectedVariation || !_.isEqual(prevProps.features, this.props.features)) {
      this.loadFeatures(this.props.features, this.props.selectedVariation, this.props.componentsVisible, this.props.initialSelectedFeatureId);
    }

    // if (prevProps.selectedFeatureId !== this.props.selectedFeatureId) {
    //   this.zoomPanToFeature(this.props.selectedFeatureId);
    // }
  };

  componentWillUnmount() {
    if (this.handleWindowResize.current) window.removeEventListener('resize', this.handleWindowResize.current);
  };

  updateSceneBrightness = (brightness) => {
    if (this.scene) {
      // brightness is a value between 0 to 100 so convert to -1 to 1
      if (_.isNumber(_.get(this.scene, 'filters[0]'))) {
        this.scene.filters[0].brightness = (brightness - 50) / 50;
        this.scene.applyFilters();
      } else {
        this.scene.filters = [new fabric.Image.filters.Brightness({ brightness: (brightness - 50) / 50 })];
        this.scene.applyFilters();
      }
      this.fabricCanvas.renderAll();
    }
  };

  updateSceneContrast = (contrast) => {
    if (this.scene) {
      // contrast is a value between 0 to 100 so convert to -1 to 1
      if (_.isNumber(_.get(this.scene, 'filters[1]'))) {
        this.scene.filters[1].contrast = (contrast - 50) / 50;
        this.scene.applyFilters();
      } else {
        this.scene.filters = [new fabric.Image.filters.Brightness({ brightness: 0 }), new fabric.Image.filters.Contrast({ contrast: (contrast - 50) / 50 })];
        this.scene.applyFilters();
      }
      this.fabricCanvas.renderAll();
    }
  };

  updateSceneSaturation = (saturation) => {
    if (this.scene) {
      // saturation is a value between 0 to 100 so convert to -1 to 1
      if (_.isNumber(_.get(this.scene, 'filters[2]'))) {
        this.scene.filters[2].saturation = (saturation - 50) / 50;
        this.scene.applyFilters();
      } else {
        this.scene.filters = [new fabric.Image.filters.Brightness({ brightness: 0 }), new fabric.Image.filters.Contrast({ contrast: 0 }), new fabric.Image.filters.Saturation({ saturation: (saturation - 50) / 50 })];
        this.scene.applyFilters();
      }
      this.fabricCanvas.renderAll();
    }
  };

  updateSceneSharpness = (enabled) => {
    if (this.scene) {
      if (enabled && _.get(this.scene, 'filters[3]') === undefined) {
        this.scene.filters.push(new fabric.Image.filters.Convolute({
          matrix: [ 0, -1, 0, -1, 5, -1, 0, -1, 0 ]
        }));
        this.scene.applyFilters();
      } else if (!enabled && _.get(this.scene, 'filters[3]') !== undefined) {
        this.scene.filters = this.scene.filters.filter((filter) => !(filter instanceof fabric.Image.filters.Convolute));
        this.scene.applyFilters();
      }
      this.fabricCanvas.renderAll();
    }
  };

  removeCurRect = () => {
    if (this.curRect) {
      if (this.curRect.lockIcon) this.fabricCanvas.remove(this.curRect.lockIcon);
      this.fabricCanvas.remove(this.curRect);
      this.curRect = null;
    }
  };

  zoomPanToFeature = (featureId) => {
    if (!this.fabricCanvas || !this.rects || this.rects.length === 0) return;
    const rect = this.rects.find((r) => String(r.get('feature_id')) === String(featureId));
    if (!rect) return;

    const rectCenter = new fabric.Point(
      rect.left + rect.width / 2,
      rect.top + rect.height / 2
    );

    const canvasWidth = this.fabricCanvas.getWidth();
    const canvasHeight = this.fabricCanvas.getHeight();

    // let zoom = Math.min(
    //   canvasWidth/ rect.width,
    //   canvasHeight / rect.height,
    // );
    // zoom = Math.min(zoom, 5);

    let zoom = Math.min(
      canvasWidth/ rect.width,
      canvasHeight / rect.height,
    );
    zoom -= zoom * 0.1;
    // zoom = Math.min(zoom, 5);

    this.fabricCanvas.zoomToPoint(rectCenter, zoom);

    const newRectCenter = fabric.util.transformPoint(rectCenter, this.fabricCanvas.viewportTransform);

    // Calculate the pan adjustment to center the cropped area
    const panX = (canvasWidth / 2 - newRectCenter.x) + this.fabricCanvas.viewportTransform[4];
    const panY = (canvasHeight / 2 - newRectCenter.y) + this.fabricCanvas.viewportTransform[5];

    // Apply the pan adjustment
    this.fabricCanvas.viewportTransform = [zoom, 0, 0, zoom, panX, panY];

    const newWidth = Math.min(baseBboxStrokeWidth, baseBboxStrokeWidth / zoom);
    const strokeWidthDelta = newWidth - this.rects[0].strokeWidth;
    // update the rect stroke width
    this.rects.forEach((rect) => {
      rect.set({
        strokeWidth: newWidth,
        left: rect.left - strokeWidthDelta,
        top: rect.top - strokeWidthDelta,
        width: rect.width + strokeWidthDelta,
        height: rect.height + strokeWidthDelta,
      });
      rect.setCoords();
      if (rect.lockIcon) {
        rect.lockIcon.set({
          left: rect.left,
          top: rect.top - 12,
        });
      }
    });

    // Re-render the canvas
    this.fabricCanvas.requestRenderAll();
  };

  loadScene = async (dataUrl) => {
    if (!this.fabricCanvas || !this.displayCanvasRef.current || !this.containerRef.current || !dataUrl) return;

    let res;
    try {
      res = await fetch(`${serverEndpoint}/data?data_uri=${dataUrl}`);
    } catch (e) {
      console.error('Failed to fetch data', e);
      return;
    }
    const blob = await res.blob();
    const reader = new FileReader();
    reader.readAsDataURL(blob);
    const dataurl = await new Promise((resolve) => {
      reader.onload = (event) => resolve(event.target.result);
    });

    // dispose previous scene
    if (this.scene) {
      this.fabricCanvas.remove(this.scene);
      this.scene = null;
    }

    fabric.Image.fromURL(dataurl, (img) => {
      img.set({
        selectable: false,
        evented: false,
        objectCaching: false,
        lockScalingFlip: true,
        scaleX: 1,
        scaleY: 1,
      });
      this.sceneOriginalWidth = img.width;
      this.sceneOriginalHeight = img.height;
      const { fabricCanvas } = this;
      fabricCanvas.setWidth(this.containerRef.current.offsetWidth);
      fabricCanvas.setHeight(this.containerRef.current.offsetHeight);
      // fabricCanvas.lowerCanvasEl.width = this.containerRef.current.offsetWidth;
      // fabricCanvas.lowerCanvasEl.height = this.containerRef.current.offsetHeight;
      // fabricCanvas.upperCanvasEl.width = this.containerRef.current.offsetWidth;
      // fabricCanvas.upperCanvasEl.height = this.containerRef.current.offsetHeight;
      fabricCanvas.calcOffset();
      this.scene = img;

      this.scene.filters = [
        new fabric.Image.filters.Brightness({ brightness: _.isNumber(this.props.curDisplayOptionsBrightness) ? (this.props.curDisplayOptionsBrightness - 50) / 50 : 0 }),
        new fabric.Image.filters.Contrast({ contrast: _.isNumber(this.props.curDisplayOptionsContrast) ? (this.props.curDisplayOptionsContrast - 50) / 50 : 0 }),
        new fabric.Image.filters.Saturation({ saturation: _.isNumber(this.props.curDisplayOptionsSaturation) ? (this.props.curDisplayOptionsSaturation - 50) / 50 : 0 }),
        // new fabric.Image.filters.Convolute({ matrix: [ 0, -1, 0, -1, 5, -1, 0, -1, 0 ] }),
      ];

      this.scene.applyFilters();

      this.fabricCanvas.add(this.scene);

      this.fabricCanvas.renderAll();

      // if (_.isNumber(this.props.curDisplayOptionsBrightness)) this.updateSceneBrightness(this.props.curDisplayOptionsBrightness);
      // if (_.isNumber(this.props.curDisplayOptionsContrast)) this.updateSceneContrast(this.props.curDisplayOptionsContrast);
      // if (_.isNumber(this.props.curDisplayOptionsSaturation)) this.updateSceneSaturation(this.props.curDisplayOptionsSaturation);

      this.updateSceneZIndex();
      this.resetView();
    });
  };

  setMode = (mode) => {
    this.curMode = mode;
    if (mode === 'select') {
      this.fabricCanvas.isDrawingMode = false;
      this.fabricCanvas.selection = true;
      this.toggleSelectableForAllObjects(true);
    } else if (mode === 'draw') {
      this.fabricCanvas.isDrawingMode = false;
      this.fabricCanvas.selection = false;
      this.toggleSelectableForAllObjects(false);
    } else if (mode === 'eraseObject') {
      this.fabricCanvas.isDrawingMode = false;
      this.fabricCanvas.selection = true;
      this.toggleSelectableForAllObjects(true);
    } else if (mode === 'translate') {
      this.fabricCanvas.isDrawingMode = false;
      this.fabricCanvas.selection = false;
      this.toggleSelectableForAllObjects(false);
    }
  };

  toggleSelectableForAllObjects = (selectable) => {
    this.fabricCanvas.forEachObject((obj) => {
      if (obj !== this.scene) {
        obj.selectable = selectable;
        obj.evented = selectable;
        // disable pan scale as well
        obj.lockMovementX = !selectable;
        obj.lockMovementY = !selectable;  
        obj.lockScalingX = !selectable;
        obj.lockScalingY = !selectable;
      }
    });
  };

  removeAllFeatures = () => {
    this.rects.forEach((rect) => {
      if (rect.lockIcon) this.fabricCanvas.remove(rect.lockIcon);
      this.fabricCanvas.remove(rect);
    });
    this.rects = [];
  };

  loadFeatures = async (features, selectedVariation, componentsVisible=true, initSelectedFeatureId=null) => {
    // dispose previous rects
    this.rects.forEach((rect) => {
      if (rect.lockIcon) this.fabricCanvas.remove(rect.lockIcon);
      this.fabricCanvas.remove(rect);
    });
    this.rects = [];
    
    if (!this.fabricCanvas || _.isEmpty(features)) return;
    const {
      handleSelectFeature,
      handleModifyFeature,
    } = this.props;

    // load features(bounding box)
    // console.log('load features', this.scene.scaleX, this.scene.scaleY);

    let zoom = this.fabricCanvas.getZoom();
    const newStrokeWidth = Math.min(baseBboxStrokeWidth, baseBboxStrokeWidth / zoom);
    // const strokeWidthDelta = newStrokeWidth - baseBboxStrokeWidth;

    // load lock svg first
    const lockSvg = await new Promise((resolve) => {
      fabric.loadSVGFromURL('/img/icn/icn_lock_purple.svg', (objects, options) => {
        const svgObj = new fabric.Group(objects, options);
        resolve(svgObj);
      }, (item, object) => {
        const group = [];
        object.set('id', item.getAttribute('id'));
        group.push(object);
      });
    });

    for (const feature of features) {
      const pMin = _.get(feature, 'roi.points[0]');
      const pMax = _.get(feature, 'roi.points[1]');
      // this position is based on the original image size
      // hence we need to scale it to the current scene size
      let newLeft = 0;
      let newTop = 0;
      let newWidth = 0;
      let newHeight = 0;
      if (_.isNumber(this.sceneOriginalHeight) && _.isNumber(this.sceneOriginalWidth) && this.scene) {
        newLeft = pMin.x * this.scene.scaleX;
        newTop = pMin.y * this.scene.scaleY;
        newWidth = (pMax.x - pMin.x) * this.scene.scaleX;
        newHeight = (pMax.y - pMin.y) * this.scene.scaleY;
      } else {
        newLeft = pMin.x;
        newTop = pMin.y;
        newWidth = pMax.x - pMin.x;
        newHeight = pMax.y - pMin.y;
      }
      // since we are using width 5 and backend will exclude the pMax point(1 pixel) so...
      newLeft -= newStrokeWidth;
      newTop -= newStrokeWidth;
      newWidth += newStrokeWidth + 1;
      newHeight += newStrokeWidth + 1;
      
      // if (strokeWidthDelta !== 0) {
      //   newLeft -= strokeWidthDelta;
      //   newTop -= strokeWidthDelta;
      //   newWidth += strokeWidthDelta;
      //   newHeight += strokeWidthDelta;
      // }
      
      const rect = new fabric.Rect({
        left: newLeft,
        top: newTop,
        width: newWidth,
        height: newHeight,
        fill: 'transparent',
        stroke: getColorByStr(_.get(feature, 'feature_type')),
        strokeWidth: newStrokeWidth,
        selectable: false,
        strokeUniform: true, // Ensure stroke width remains consistent when scaling
      });

      // disable bbox rotation control
      rect.controls = {
        ...fabric.Rect.prototype.controls,
        mtr: new fabric.Control({
          visible: false,
        }),
      };
      // make bbox selectable by per pixel to avoid overlaped bbox can not be selected
      rect.perPixelTargetFind = true;
      rect.targetFindTolerance = 10;
      rect.set('scaledToScene', _.isNumber(this.sceneOriginalHeight) && _.isNumber(this.sceneOriginalWidth) && this.scene);
      this.fabricCanvas.add(rect);
      this.rects.push(rect);
      
      if (!_.isEmpty(selectedVariation) && _.get(feature, 'variant') !== selectedVariation) {
          // if this feature does not belong to the selected variation add lock icon at top left corner
        const lockIcon = await new Promise((resolve) => {
          lockSvg.clone((svgObj) => {
            resolve(svgObj);
          });
        });
        
        lockIcon.getObjects().forEach((obj) => {
          obj.set({ fill: getColorByStr(_.get(feature, 'feature_type')) }); // Set all paths in the SVG to red
        });

        lockIcon.set({
          left: newLeft,
          top: newTop - 12,
          selectable: false,
          moveCursor: 'default',
          hoverCursor: 'default',
        });
        this.fabricCanvas.add(lockIcon);
        rect.set('lockIcon', lockIcon);

        // and the rect is not selectable
        rect.set({
          selectable: false,
          moveCursor: 'not-allowed',
          hoverCursor: 'not-allowed',
          lockMovementX: true,
          lockMovementY: true,
        });
      }

      rect.set('prevTop', rect.top);
      rect.set('prevLeft', rect.left);
      rect.set('feature_id', _.get(feature, 'feature_id'));
      rect.set('product_id', _.get(feature, 'product_id'));
      rect.set('feature_type', _.get(feature, 'feature_type'));
      rect.set('step', _.get(feature, 'step'));
      rect.set('variant', _.get(feature, 'variant'));
      rect.set('hoverCursor', 'pointer');
      // rect.on('selected', () => {
      //   this.selectedFeatureId = _.get(feature, 'feature_id');
      //   handleSelectFeature(_.get(feature, 'feature_id'));
      // });

      rect.on('mouseup', (opt) => {
        this.selectedFeatureId = _.get(feature, 'feature_id');
        handleSelectFeature(_.get(feature, 'feature_id'));
        rect.set('selectable', true);
        rect.set('hoverCursor', 'move');
        this.fabricCanvas.setActiveObject(rect);
        this.fabricCanvas.renderAll();
      });

      rect.onDeselect = () => {
        if (String(this.selectedFeatureId) === String(rect.get('feature_id'))) return;
        setTimeout(() => {
          rect.set('selectable', false);
          rect.set('hoverCursor', 'pointer');
          handleSelectFeature(null);
        }, 150);
      };
      rect.on('moving', () => {
        // update the lock icon position as well
        if (rect.lockIcon) {
          rect.lockIcon.set({
            left: rect.left,
            top: rect.top - 12,
          });
        }
      });
      rect.on('modified', () => {
        if (!this.scene) return;
        // revert the position and dimension to the original image size
        // console.log('modified', rect.strokeWidth);
        const { pMax, pMin } = getTwoDRectPminPmax(rect, rect.strokeWidth);

        const getUpdatedIgnoreMaskAndUpdateFeature = async (feature, pMax, pMin) => {
          if (_.isEmpty(_.get(feature, 'mask_uri'))) {
            handleModifyFeature({
              ...feature,
              roi: {
                ...feature.roi,
                points: [
                  pMin,
                  pMax
                ]
              }
            });

            return;
          }

          // generate the updated ignore mask based on the new roi
          // get the current ignore mask and put it into an empty scene(filled with 255)
          // then crop the updated scene based on the new roi position and dimension

          // get the current ignore mask
          let ignoreMask;
          let prevMaskRes;
          try {
            prevMaskRes = await fetch(`${serverEndpoint}/data?data_uri=${_.get(feature, 'mask_uri', null)}`);
          } catch (e) {
            console.error(e);
            return;
          }
          const blob = await prevMaskRes.blob();
          // convert to uint8 array
          const reader = new FileReader();
          reader.readAsArrayBuffer(blob);
          ignoreMask = await new Promise((resolve, reject) => {
            reader.onload = (event) => {
              resolve(new Uint8Array(event.target.result));
            };
          });

          // console.log('ignoreMask', ignoreMask);

          // create a new scene filled with 255 and the same size as the original scene
          const newScene = new Uint8Array(this.sceneOriginalWidth * this.sceneOriginalHeight).fill(255);
          // insert the ignore mask into the new scene based on the original feature's position and dimension
          const prevPMin = _.get(feature, 'roi.points[0]');
          const prevPMax = _.get(feature, 'roi.points[1]');
          // console.log('prevPMin', prevPMin, 'prevPMax', prevPMax);
          let ignoreMaskIndex = 0;
          for (let y = prevPMin.y; y < prevPMax.y + 1; y++) {
            for (let x = prevPMin.x; x < prevPMax.x + 1; x++) {
              const index = (y - 1) * this.sceneOriginalWidth + (x - 1);
              newScene[index] = ignoreMask[ignoreMaskIndex];
              ignoreMaskIndex++;
            }
          }
          // console.log('newScene', newScene);

          // crop the new scene based on the new roi position and dimension
          const newIgnoreMask = [];
          // console.log('pMin', pMin, 'pMax', pMax);
          for (let y = pMin.y; y < pMax.y + 1; y++) {
            for (let x = pMin.x; x < pMax.x + 1; x++) {
              const index = (y - 1) * this.sceneOriginalWidth + (x - 1);
              newIgnoreMask.push(newScene[index]);
            }
          }
          // console.log('newIgnoreMask', newIgnoreMask);

          // convert to base64
          let decoded = '';
          for (let i = 0; i < newIgnoreMask.length; i++) {
            decoded += String.fromCharCode(newIgnoreMask[i]);
          }
          const base64 = btoa(decoded);

          // upload the new ignore mask with the updated roi
          handleModifyFeature({
            ...feature,
            roi: {
              ...feature.roi,
              points: [
                pMin,
                pMax
              ]
            },
            mask_data: base64,
            mask_width: pMax.x - pMin.x + 1,
            mask_height: pMax.y - pMin.y + 1,
          });
        };

        getUpdatedIgnoreMaskAndUpdateFeature(feature, pMax, pMin);
      });
      
      rect.on('scaling', () => {
        // precent the inner rect boundaries' length is less than 40 pixel
        // and fabric's rect width and height includes the stroke width...
        // console.log('scaling', rect.getScaledWidth()-rect.strokeWidth, rect.getScaledHeight()-rect.strokeWidth);

        if ((defineProductBboxBoundaryLengthLimit + rect.strokeWidth) / rect.width > rect.scaleX) {
          rect.scaleX = (defineProductBboxBoundaryLengthLimit + rect.strokeWidth) / rect.width;
          rect.left = rect.prevLeft;
        }
        if ((defineProductBboxBoundaryLengthLimit + rect.strokeWidth) / rect.height > rect.scaleY) {
          rect.scaleY = (defineProductBboxBoundaryLengthLimit + rect.strokeWidth) / rect.height;
          rect.top = rect.prevTop;
        }
      });

      rect.set('visible', componentsVisible);
      rect.setCoords();
    }

    if (_.isString(initSelectedFeatureId)) {
      this.zoomPanToFeature(initSelectedFeatureId);
    } else if (!this.initZoomPanToProductFeature) {
      // find product feature
      const productFeature = this.rects.find((r) => r.get('feature_type') === featureType.product);
      if (productFeature) {
        await sleep(500);
        this.selectedFeatureId = productFeature.get('feature_id');
        handleSelectFeature(productFeature.get('feature_id'));
        this.zoomPanToFeature(productFeature.get('feature_id'));
        this.initZoomPanToProductFeature = true;
      }
    }

    this.updateSceneZIndex();
  };

  removeFeatureFromScene = ({ product_id, step, feature_id }) => {
    // remove from rects
    const rect = this.rects.find((r) => {
      return r.get('product_id') === product_id && r.get('step') === step && r.get('feature_id') === feature_id;
    });
    if (rect) {
      if (rect.lockIcon) this.fabricCanvas.remove(rect.lockIcon);
      this.fabricCanvas.remove(rect);
      this.rects = this.rects.filter((r) => r !== rect);
    }
    return;
  };

  clearScene = () => {
    if (this.fabricCanvas) {
      this.fabricCanvas.clear();
    }
    this.scene = null;
    this.rects = [];
  };

  toggleRectsVisibility = (visible) => {
    this.rects.forEach((rect) => {
      rect.set('visible', visible);
    });
    this.fabricCanvas.renderAll();
  };

  updateSceneZIndex = () => {
    if (this.scene) {
      this.scene.moveTo(2);
    }
    this.rects.forEach((rect) => {
      rect.moveTo(4);
      if (rect.lockIcon) rect.lockIcon.moveTo(4);
    });
    this.fabricCanvas.requestRenderAll();
  };

  lockScene = () => this.sceneLocked = true;

  unlockScene = () => this.sceneLocked = false;

  resetView = () => {
    if (!this.fabricCanvas || !this.scene) return;
    // zoom and pan to view the whole scene
    const canvasWidth = this.fabricCanvas.getWidth();
    const canvasHeight = this.fabricCanvas.getHeight();
    const sceneWidth = this.scene.width * this.scene.scaleX;
    const sceneHeight = this.scene.height * this.scene.scaleY;
    const scaleX = canvasWidth / sceneWidth;
    const scaleY = canvasHeight / sceneHeight;
    const zoom = Math.min(scaleX, scaleY);
    // this.fabricCanvas.zoomToPoint({ x: canvasWidth / 2, y: canvasHeight / 2 }, zoom);
    // this.fabricCanvas.absolutePan({
    //   x: (canvasWidth - sceneWidth * zoom) / 2,
    //   y: (canvasHeight - sceneHeight * zoom) / 2
    // });

    const panX = (canvasWidth - sceneWidth * zoom) / 2;
    const panY = (canvasHeight - sceneHeight * zoom) / 2;

    this.fabricCanvas.viewportTransform = [zoom, 0, 0, zoom, panX, panY];

    if (this.rects.length > 0) {
      // update the rect stroke width
      const newWidth = Math.min(baseBboxStrokeWidth, baseBboxStrokeWidth / zoom);
      const strokeWidthDelta = newWidth - this.rects[0].strokeWidth;
      this.rects.forEach((rect) => {
        rect.set({
          strokeWidth: newWidth,
          left: rect.left - strokeWidthDelta,
          top: rect.top - strokeWidthDelta,
          width: rect.width + strokeWidthDelta,
          height: rect.height + strokeWidthDelta,
        });
        rect.setCoords();

        if (rect.lockIcon) {
          rect.lockIcon.set({
            left: rect.left,
            top: rect.top - 12,
          });
        }
      });
    }

    this.fabricCanvas.renderAll();
  };

  render() {
    return  (
      <div className='relative h-full w-full' ref={this.containerRef}>
        <div
          className='absolute z-[10]'
          style={{
            display: this.state.curBoxWidth === 0 && this.state.curBoxHeight === 0 ? 'none' : 'block',
            top: `${this.state.curMouseTop}px`,
            left: `${this.state.curMouseLeft + 10}px`,
            // border: `2px solid ${this.state.curBoxHeight >= defineProductBboxBoundaryLengthLimit && this.state.curBoxWidth >= defineProductBboxBoundaryLengthLimit ? '#81F499' : '#EB5757'}`,
            borderRadius: '4px',
            // background: `${this.state.curBoxHeight >= defineProductBboxBoundaryLengthLimit && this.state.curBoxWidth >= defineProductBboxBoundaryLengthLimit ? '#81F499' : '#EB5757'}`,
            background: '#56CCF2',
          }}
        >
          <div className='flex items-center gap-1 px-2'>
            <span
              className='font-source text-[12px] font-semibold'
              style={{ color: this.state.curBoxWidth >= defineProductBboxBoundaryLengthLimit ? '#131313' : '#EB5757' }}
            >
              {this.state.curBoxWidth.toFixed(0)}
            </span>
            <span
              className='font-source text-[12px] font-semibold text-[#131313]'
            >x</span>
            <span
              className='font-source text-[12px] font-semibold'
              style={{ color: this.state.curBoxHeight >= defineProductBboxBoundaryLengthLimit ? '#131313' : '#EB5757' }}
            >
              {this.state.curBoxHeight.toFixed(0)}
            </span>
          </div>
        </div>
        {super.render()}
      </div>
    );
  }
};