import React, { Fragment, useEffect, useState } from 'react';
import MainMenuLayout from '../layout/MainMenuLayout';
import SessionList from './SessionList';
import InspectionList from './InspectionList';
import { getQueryParams, handleRequestFailed, translation } from '../../common/util';
import { Button } from 'antd';
import { useGetAllProductsQuery } from '../../services/product';
import WorklistExport from '../modal/WorklistExport';
import _ from 'lodash';
import { useLazyGetSessionInfoQuery } from '../../services/session';
import AllInspections from './AllInspections';


const Worklist = (props) => {
  const { search } = props.location;
  const searchParams = getQueryParams(search);
  const { selectedSessionIdFromHome } = searchParams;

  const [displayContent, setDisplayContent] = useState('session'); // session, inspection, allIpc
  const [selectedGoldenProdId, setSelectedGoldenProdId] = useState(null);
  const [startTimeInSession, setStartTimeInSession] = useState(null);
  const [endTimeInSession, setEndTimeInSession] = useState(null);
  const [startTimeInInspection, setStartTimeInInspection] = useState(null);
  const [endTimeInInspection, setEndTimeInInspection] = useState(null);
  const [isExportModalOpened, setIsExportModalOpened] = useState(false);
  const [onlyFeedbackProvided, setOnlyFeedbackProvided] = useState(false);
  const [onlyDefectiveItems, setOnlyDefectiveItems] = useState(false);
  const [searchSerialNumber, setSearchSerialNumber] = useState('');
  const [ipcSessionId, setIpcSessionId] = useState(null);
  const [selectedSession, setSelectedSession] = useState(null);
  const [inspectionPagination, setInspectionPagination] = useState({
    current: 1,
    pageSize: 0,
    total: 0,
  });
  const [sessionPagination, setSessionPagination] = useState({
    current: 0,
    pageSize: 0,
    total: 0,
  });
  const [refreshToggle, setRefreshToggle] = useState(0);
  // const [keepUpdated, setKeepUpdated] = useState(false);

  const { data: allProducts } = useGetAllProductsQuery();
  const [getSessionInfo] = useLazyGetSessionInfoQuery();

  const handlePreselectedSession = async (sessionId) => {
    const res = await getSessionInfo(sessionId);

    if (res.error) {
      handleRequestFailed('getSessionInfo', res.error);
      return;
    }

    setIpcSessionId(sessionId);
    setSelectedSession(_.get(res, 'data'));
    setDisplayContent('inspection');

    props.history.push('/aoi/worklist');
  };

  useEffect(() => {
    // reset two pagination
    setInspectionPagination({
      current: 1,
      pageSize: 0,
      total: 0,
    });
    setSessionPagination({
      current: 0,
      pageSize: 0,
      total: 0,
    });
    setRefreshToggle(0);
  }, [displayContent]);

  useEffect(() => {
    if (!_.isEmpty(selectedSessionIdFromHome)) handlePreselectedSession(selectedSessionIdFromHome);
  }, []);

  return (
    <Fragment>
      <WorklistExport
        isOpened={isExportModalOpened}
        setIsOpened={setIsExportModalOpened}
        exportMode={displayContent}
        selectedGoldenProdId={selectedGoldenProdId}
        startTimeInSession={startTimeInSession}
        endTimeInSession={endTimeInSession}
        startTimeInInspection={startTimeInInspection}
        endTimeInInspection={endTimeInInspection}
        ipcSessionId={ipcSessionId}
        inspectionPagination={inspectionPagination}
        sessionPagination={sessionPagination}
        searchSerialNumber={searchSerialNumber}
        onlyFeedbackProvided={onlyFeedbackProvided}
        onlyDefectiveItems={onlyDefectiveItems}
      />
      <MainMenuLayout>
        <div className='flex pt-2 pr-8 pb-4 pl-8 flex-col items-start gap-4 flex-1 self-stretch bg-[#131313]'>
          <div className='flex h-[48px] py-2 items-center gap-8 self-stretch'>
            <div className='flex items-center gap-3'>
              <div className='flex justify-center items-center'>
                <img className='w-[20px] h-[20px]' src='/img/icn/icn_checklist_white.svg' alt='checklist' />
              </div>
              <span className='font-source text-[20px] font-semibold'>
                {translation('worklist.worklist')}
              </span>
            </div>
            <div className='flex items-center gap-2'>
              <Button
                onClick={() =>  setRefreshToggle(refreshToggle + 1)}
              >
                <span className='font-source text-[12px] font-normal'>
                  {translation('worklist.refreshList')}
                </span>
              </Button>
              <Button onClick={() => {
                setDisplayContent(displayContent === 'allIpc' ? 'session' : 'allIpc');
              }}>
                <span className='font-source text-[12px] font-normal'>
                  {displayContent === 'allIpc' ? translation('worklist.switchToViewSingleSessionInspection') : translation('worklist.switchToAllInspection')}
                </span>
              </Button>
            </div>
          </div>
          <div className='w-full h-[1px] bg-[#333]' />
          { displayContent === 'allIpc' &&
            <AllInspections
              allProducts={allProducts}
            />
          }
          { displayContent === 'session' &&
            <SessionList
              setDisplayContent={setDisplayContent}
              allProducts={allProducts}
              selectedGoldenProdId={selectedGoldenProdId}
              setSelectedGoldenProdId={setSelectedGoldenProdId}
              startTimeInSession={startTimeInSession}
              setStartTimeInSession={setStartTimeInSession}
              endTimeInSession={endTimeInSession}
              setEndTimeInSession={setEndTimeInSession}
              setIsExportModalOpened={setIsExportModalOpened}
              setIpcSessionId={setIpcSessionId}
              setSelectedSession={setSelectedSession}
              pagination={sessionPagination}
              setPagination={setSessionPagination}
              refreshToggle={refreshToggle}
              setRefreshToggle={setRefreshToggle}
            />
          }
          { displayContent === 'inspection' &&
            <InspectionList
              setDisplayContent={setDisplayContent}
              selectedGoldenProdId={selectedGoldenProdId}
              allProducts={allProducts}
              startTimeInInspection={startTimeInInspection}
              setStartTimeInInspection={setStartTimeInInspection}
              endTimeInInspection={endTimeInInspection}
              setEndTimeInInspection={setEndTimeInInspection}
              onlyFeedbackProvided={onlyFeedbackProvided}
              setOnlyFeedbackProvided={setOnlyFeedbackProvided}
              onlyDefectiveItems={onlyDefectiveItems}
              setOnlyDefectiveItems={setOnlyDefectiveItems}
              setIsExportModalOpened={setIsExportModalOpened}
              setSelectedGoldenProdId={setSelectedGoldenProdId}
              setSearchSerialNumber={setSearchSerialNumber}
              searchSerialNumber={searchSerialNumber}
              ipcSessionId={ipcSessionId}
              selectedSession={selectedSession}
              pagination={inspectionPagination}
              setPagination={setInspectionPagination}
              refreshToggle={refreshToggle}
              setRefreshToggle={setRefreshToggle}
            />
          }
        </div>
      </MainMenuLayout>
    </Fragment>
  );
};

export default Worklist;