import { Button, Modal, Radio, Select } from 'antd';
import React, { useState } from 'react';
import { handleRequestFailed, translation } from '../../common/util';
import { useGetAllProductsQuery } from '../../services/product';
import _ from 'lodash';
import { ALERT_TYPES, aoiAlert } from '../../common/alert';
import { serverEndpoint } from '../../common/const';
import { PrimaryButtonConfigProvider } from '../../common/darkModeComponents';


const ExportCaptureData = (props) => {
  const {
    isOpened,
    setIsOpened,
  } = props;
  
  const [selectedGoldenProductId, setSelectedGoldenProductId] = useState(null);
  const [isExporting, setIsExporting] = useState(false);

  const { data: allGoldenProducts } = useGetAllProductsQuery();

  const handleExport = async () => {
    if (_.isNull(selectedGoldenProductId)) {
      aoiAlert(translation('notification.warning.selectAGoldenProduct'), ALERT_TYPES.COMMON_WARNING);
      return;
    }

    setIsExporting(true);
    let exportRes;
    try {
      exportRes = await fetch(`${serverEndpoint}/export?product_id=${selectedGoldenProductId}`);
    } catch (error) {
      handleRequestFailed('exportFailed', error);
      return;
    }
    const exportJson = await exportRes.json();
    if (!_.get(exportJson, 'data_uri')) {
      aoiAlert(translation('notification.warning.dataUriNotFound'), ALERT_TYPES.COMMON_WARNING);
      return;
    }
    setIsExporting(false);
    // download zip
    const link = document.createElement('a');
    link.href = `${serverEndpoint}/file?file_uri=${exportJson.data_uri}`;
    link.download = 'capture_data.zip';
    link.click();
    link.remove();
    setIsOpened(false);
  };

  return (
    <Modal
      open={isOpened}
      onCancel={() => {
        if (!isExporting) setIsOpened(false);
      }}
      footer={null}
      title={
        <span className='font-source text-[16px] font-semibold'>
          {translation('dataExport.dataExport')}
        </span>
      }
    >
      <div className='flex p-4 flex-col items-start gap-6 self-stretch'>
        <div className='flex flex-col items-start gap-6 self-stretch'>
          <div className='flex flex-col gap-2 self-stretch items-start'>
            <span className='font-source text-[14px] font-semibold'>
              {translation('dataExport.scope')}
            </span>
            <Radio.Group value={1}>
              <Radio value={1}>
                {translation('dataExport.goldenProduct')}
              </Radio>
            </Radio.Group>
          </div>
          <div className='flex flex-col gap-2 self-stretch items-start justify-center'>
            <span className='font-source text-[14px] font-semibold'>
              {translation('dataExport.selectAGoldenProduct')}
            </span>
            <Select
              style={{ width: '100%' }}
              options={_.map(_.filter(allGoldenProducts, (product) => product.is_golden === true), (product) => ({
                label: product.product_name,
                value: Number(product.product_id),
              }))}
              value={selectedGoldenProductId}
              onChange={(value) => setSelectedGoldenProductId(value)}
            />
          </div>
        </div>
        <div className='flex py-2 px-4 flex-col items-start gap-4 flex-1 self-stretch bg-[#4F4F4F]'>
          <div className='flex flex-col items-start gap-2 flex-1 self-stretch'>
            <div className='flex py-1 items-center gap-2.5 self-stretch'>
              <span className='font-source text-[14px] font-semibold'>
                {translation('dataExport.zipExport')}
              </span>
            </div>
            <div className='flex py-2 gap-1 items-start self-stretch'>
              <span className='font-source text-[14px] font-normal'>
                {translation('dataExport.zipExportDesc')}
              </span>
            </div>
          </div>
          <div className='flex gap-2 items-center self-stretch'>
            <PrimaryButtonConfigProvider>
              <Button
                style={{ width: '100%' }}
                onClick={() => handleExport()}
                loading={isExporting}
              >
                {translation('dataExport.downloadZip')}
              </Button>
            </PrimaryButtonConfigProvider>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default ExportCaptureData;