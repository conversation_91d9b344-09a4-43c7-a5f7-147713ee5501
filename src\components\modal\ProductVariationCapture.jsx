import { Input, Modal, Select } from 'antd';
import React, { useState } from 'react';
import { handleRequestFailed, translation } from '../../common/util';
import { activeGreen, DarkButton, defaultGreen } from '../../common/darkModeComponents';
import { useRunDetectionPipelineMutation } from '../../services/product';
import { inputRegularExpression } from '../../common/const';
import { ALERT_TYPES, aoiAlert } from '../../common/alert';
import _ from 'lodash';


const ProductVariationCapture = (props) => {
  const {
    isOpened,
    setIsOpened,
    productId,
    productInfo,
    handleVariationRegisterSuccess,
  } = props;

  const [isLoading, setIsLoading] = useState(false);
  const [variationName, setVariationName] = useState('');

  const [productRegister] = useRunDetectionPipelineMutation();

  return (
    <Modal
      open={isOpened}
      onCancel={() => {
        if (isLoading) return;
        setIsOpened(false);
      }}
      title={
        <span className='font-source text-[16px] font-semibold'>
          {translation('imageCaptureRequired.userActionRequired')}
        </span>
      }
      footer={<div className='flex flex-col items-start self-stretch gap-2'>
        <DarkButton
          loading={isLoading}
          bgColor={defaultGreen}
          hoverBgColor={activeGreen}
          borderColor={defaultGreen}
          hoverTextColor={'#333'}
          hoverBorderColor={activeGreen}
          textColor={'#333'}
          style={{ width: '100%' }}
          onClick={async () => {
            if (!inputRegularExpression.newProductVariant.variantName.test(variationName) || _.isEmpty(variationName)) {
              aoiAlert(translation('notification.error.enterAVaildVariationName'), ALERT_TYPES.COMMON_ERROR);
              return;
            }
            setIsLoading(true);
            const res = await productRegister({ product_id: productId, variant: variationName });
            if (res.error) {
              handleRequestFailed('productRegister', res.error);
              setIsLoading(false);
              return;
            }
            handleVariationRegisterSuccess(variationName);
            setIsLoading(false);
            setIsOpened(false);
          }}
        >
          {translation('common.continue')}
        </DarkButton>
        <DarkButton
          loading={isLoading}
          bgColor={defaultGreen}
          hoverBgColor={activeGreen}
          borderColor={defaultGreen}
          hoverTextColor={'#333'}
          hoverBorderColor={activeGreen}
          textColor={'#333'}
          style={{ width: '100%' }}
          onClick={() => {
            setIsLoading(false);
            setIsOpened(false);
          }}
        >
          {translation('common.cancel')}
        </DarkButton>
      </div>}
    >
      <div className='flex py-6 px-4 flex-col items-start gap-8 self-stretch'>
        <Input
          value={variationName}
          onChange={(e) => setVariationName(e.target.value)}
          placeholder={translation('productAnnotation.newVariationName')}
        />
        <span className='text-source text-[14px] font-normal'>
          {translation('productAnnotation.positionTheNextVariation')}
        </span>
      </div>
    </Modal>
  );
};

export default ProductVariationCapture;