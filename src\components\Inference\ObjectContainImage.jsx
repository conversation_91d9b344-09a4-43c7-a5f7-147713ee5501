import _ from 'lodash';
import React, { useContext, useEffect, useRef, useState } from 'react';
import { CameraPreviewContext } from '../Context/Provider';
import { serverEndpoint } from '../../common/const';

// for displaying product detection/inference result
const ObjectContainImage = (props) => {
  const {
    step,
    onClick,
    dataUri,
  } = props;

  const [dimension, setDimensions] = useState({ width: 0, height: 0 });
  const [srcData, setSrcData] = useState('');
  const containerRef = useRef();

  useEffect(() => {
    if (!containerRef.current || !dataUri) return;
    setDimensions({
      width: containerRef.current.clientWidth,
      height: containerRef.current.clientHeight
    });
    // fetch
    const fetchData = async () => {
      let dataRes;
      try {
        dataRes = await fetch(`${serverEndpoint}/data?data_uri=${dataUri}`);
      } catch (error) {
        console.error('fetch data error', error);
        return;
      }
      const blob = await dataRes.blob();
      const reader = new FileReader();
      reader.readAsDataURL(blob);
      reader.onload = (event) => {
        setSrcData(event.target.result);
      };
    };
    fetchData();
  }, [dataUri]);

  return (
    <div
      className={'cursor-pointer w-full h-full rounded-[2px] border-[1px] border-gray-2'}
      ref={containerRef}
      onClick={() => onClick()}
    >
      <img
        src={srcData}
        className='object-contain'
        style={{ width: `${dimension.width}px`, height: `${dimension.height}px` }}
        alt={`camera data ${step}`}
      />
    </div>
  );
};

export default ObjectContainImage;