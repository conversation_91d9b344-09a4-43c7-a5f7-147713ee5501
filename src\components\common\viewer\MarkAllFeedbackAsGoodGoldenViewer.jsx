import { fabric } from 'fabric-with-erasing';
import _ from 'lodash';
import React from 'react';
import { baseBboxStrokeWidth } from '../../../common/const';
import { debounce, getColorByStr } from '../../../common/util';
import TwoDBaseViwer from './TwoDBaseViewer';


export default class MarkAllFeedbackAsGoodGoldenViewer extends TwoDBaseViwer {
  constructor(props) {
    super(props, props.displayCanvasRef);
    this.displayCanvasRef = props.displayCanvasRef;
    this.scene = null; // 2d image
    this.fabricCanvas = null;
    this.sceneOriginalWidth = 0;
    this.sceneOriginalHeight = 0;
    this.curRect = null; // current drawing rect
    this.rects = []; // all drawing rects
    this.sceneLocked = false;
    this.selectedFeatureId = null;
    this.handleWindowResize = React.createRef();
    this.containerRef = React.createRef();
  };

  componentDidMount() {
    if (!this.displayCanvasRef.current) return;

    // init fabric canvas
    const fabricCanvas = new fabric.Canvas(
      this.displayCanvasRef.current,
      {
        // Disable uniform scaling
        uniformScaling: false,
      }
    );
    this.fabricCanvas = fabricCanvas;
    fabricCanvas.skipOffscreen = true;
    fabricCanvas.contextContainer.imageSmoothingEnabled = true;

    // enable webgl filter
    let filterBackend = null;
    try {
      filterBackend = new fabric.WebglFilterBackend();
      // console.log('Use WebGL filter backend');
    } catch (e) {
      // console.error('WebGL backend is not supported, using 2d canvas backend');
      filterBackend = new fabric.Canvas2dFilterBackend();
    }
    fabricCanvas.filterBackend = filterBackend;
    if (fabric.isWebglSupported()) {
      console.log('WebGL is supported, increase texture size to 65536');
      fabric.textureSize = 65536; // ow only partial image will be rendered if sharpness is enabled since our image is large
    }

    // Enable Panning
    let isPanning = false;

    fabricCanvas.on('mouse:down', (opt) => {
      if (this.sceneLocked) return;

      this.updateSceneZIndex();

      if (opt.target) {
        isPanning = false;
        return;
      }
      
      isPanning = true;
      fabricCanvas.selection = false;
      fabricCanvas.setCursor('grab');
    });

    fabricCanvas.on('mouse:move', (opt) => {
      if (this.sceneLocked) return;
      if (isPanning && opt && opt.e) {
        fabricCanvas.setCursor('grab');
        const delta = new fabric.Point(opt.e.movementX, opt.e.movementY);
        fabricCanvas.relativePan(delta);
      }
    });

    fabricCanvas.on('mouse:up', (opt) => {
      if (this.sceneLocked) return;
      isPanning = false;
      fabricCanvas.setCursor('default');
      this.fabricCanvas.renderAll();
    });

    // Enable Zooming
    fabricCanvas.on('mouse:wheel', (opt) => {
      const delta = opt.e.deltaY;
      let zoom = fabricCanvas.getZoom();
      zoom *= 0.999 ** delta;
      // if (zoom > 20) zoom = 20;
      // if (zoom < 0.01) zoom = 0.01;
      fabricCanvas.zoomToPoint({ x: opt.e.offsetX, y: opt.e.offsetY }, zoom);
      opt.e.preventDefault();
      opt.e.stopPropagation();

      if (this.rects.length === 0) return;

      const newWidth = Math.min(baseBboxStrokeWidth, baseBboxStrokeWidth / zoom);
      
      // update top, left, width, height
      // add stroke width delta to the top left width height
      const strokeWidthDelta = newWidth - this.rects[0].strokeWidth;

      if (strokeWidthDelta === 0) return;

      // update the rect stroke width
      this.rects.forEach((rect) => {
        rect.set({
          strokeWidth: newWidth,
          left: rect.left - strokeWidthDelta,
          top: rect.top - strokeWidthDelta,
          width: rect.width + strokeWidthDelta,
          height: rect.height + strokeWidthDelta,
        });
      });
      
      this.fabricCanvas.renderAll();
    });

    const updateCanvasSize = () => {
      if (!this.containerRef.current || !this.fabricCanvas) return;
      const canvasWidth = this.containerRef.current.offsetWidth;
      const canvasHeight = this.containerRef.current.offsetHeight;
      this.fabricCanvas.setWidth(canvasWidth);
      this.fabricCanvas.setHeight(canvasHeight);
      this.fabricCanvas.renderAll();
    };

    this.handleWindowResize.current = debounce(updateCanvasSize, 300);

    window.addEventListener('resize', this.handleWindowResize.current);

    if (!_.isEmpty(this.props.goldenDataUrl)) {
      this.loadScene(this.props.goldenDataUrl, this.props.features, 438, 406);
    }
  };

  componentDidUpdate(prevProps) {
    // if (prevProps.selectedFeatureId !== this.props.selectedFeatureId) {
    //   this.selectedFeatureId = this.props.selectedFeatureId;
    //   this.zoomPanToFeature(this.props.selectedFeatureId);
    // }
    if (prevProps.goldenDataUrl !== this.props.goldenDataUrl) {
      this.loadScene(this.props.goldenDataUrl, this.props.features, 438, 406);
      this.resetView();
    }
    if (prevProps.features !== this.props.features) {
      this.loadFeatures(this.props.features);
    }
  };

  zoomPanToFeature = (featureId) => {
    if (!this.fabricCanvas || !this.rects || this.rects.length === 0) return;
    const rect = this.rects.find((r) => String(r.get('feature_id')) === String(featureId));
    if (!rect) return;

    const rectCenter = new fabric.Point(
      rect.left + rect.width / 2,
      rect.top + rect.height / 2
    );

    const canvasWidth = this.fabricCanvas.getWidth();
    const canvasHeight = this.fabricCanvas.getHeight();

    let zoom = Math.min(
      canvasWidth/ rect.width,
      canvasHeight / rect.height,
    );
    zoom = Math.min(zoom, 5);

    this.fabricCanvas.zoomToPoint(rectCenter, zoom);

    const newRectCenter = fabric.util.transformPoint(rectCenter, this.fabricCanvas.viewportTransform);

    // Calculate the pan adjustment to center the cropped area
    const panX = (canvasWidth / 2 - newRectCenter.x) + this.fabricCanvas.viewportTransform[4];
    const panY = (canvasHeight / 2 - newRectCenter.y) + this.fabricCanvas.viewportTransform[5];

    // Apply the pan adjustment
    this.fabricCanvas.viewportTransform = [zoom, 0, 0, zoom, panX, panY];

    const newWidth = Math.min(baseBboxStrokeWidth, baseBboxStrokeWidth / zoom);
    const strokeWidthDelta = newWidth - this.rects[0].strokeWidth;
    // update the rect stroke width
    this.rects.forEach((rect) => {
      rect.set({
        strokeWidth: newWidth,
        left: rect.left - strokeWidthDelta,
        top: rect.top - strokeWidthDelta,
        width: rect.width + strokeWidthDelta,
        height: rect.height + strokeWidthDelta,
      });
      rect.setCoords();
    });

    // Re-render the canvas
    this.fabricCanvas.requestRenderAll();
  };

  loadScene = async (dataUrl, features, width, height) => {
    if (!this.fabricCanvas || !this.displayCanvasRef.current || !this.containerRef.current) return;

    // dispose previous scene
    if (this.scene) {
      this.fabricCanvas.remove(this.scene);
      this.scene = null;
    }

    fabric.Image.fromURL(dataUrl, (img) => {
      img.set({
        selectable: false,
        evented: false,
        objectCaching: false,
        lockScalingFlip: true,
      });
      this.fabricCanvas.getRetinaScaling();
      // img.imageSmoothing = false;
      // img.objectCaching = false;
      this.sceneOriginalWidth = img.width;
      this.sceneOriginalHeight = img.height;
      const { fabricCanvas } = this;
      fabricCanvas.setWidth(width);
      fabricCanvas.setHeight(height);
      img.scaleToWidth(width);
      img.scaleToHeight(height);
      // fabricCanvas.setWidth(img.width);
      // fabricCanvas.setHeight(img.height);
      // fabricCanvas.add(img);
      this.scene = img;

      this.fabricCanvas.add(this.scene);

      this.fabricCanvas.renderAll();

      if (!_.isEmpty(this.rects)) {
        this.rects.forEach((rect) => {
          fabricCanvas.remove(rect);
        });
        this.rects = [];
      }

      if (!_.isEmpty(features)) {
        this.loadFeatures(features);
      }

      this.updateSceneZIndex();
      this.resetView();
    });
  };

  toggleSelectableForAllObjects = (selectable) => {
    this.fabricCanvas.forEachObject((obj) => {
      if (obj === this.scene) return;
      obj.selectable = selectable;
    });
  };

  removeAllFeatures = () => {
    this.rects.forEach((rect) => {
      if (rect.lockIcon) this.fabricCanvas.remove(rect.lockIcon);
      this.fabricCanvas.remove(rect);
    });
    this.rects = [];
  };

  loadFeatures = (features) => {
    if (!this.fabricCanvas || _.isEmpty(features)) return;

    // dispose previous rects
    this.rects.forEach((rect) => {
      if (rect.lockIcon) this.fabricCanvas.remove(rect.lockIcon);
      this.fabricCanvas.remove(rect);
    });
    this.rects = [];
    // load features(bounding box)
    // console.log('load features', this.scene.scaleX, this.scene.scaleY);

    let zoom = this.fabricCanvas.getZoom();
    const newStrokeWidth = Math.min(baseBboxStrokeWidth, baseBboxStrokeWidth / zoom);
    // const strokeWidthDelta = newStrokeWidth - baseBboxStrokeWidth;

    for (const feature of features) {
      const pMin = _.get(feature, 'roi.points[0]');
      const pMax = _.get(feature, 'roi.points[1]');
      // this position is based on the original image size
      // hence we need to scale it to the current scene size
      let newLeft = 0;
      let newTop = 0;
      let newWidth = 0;
      let newHeight = 0;
      if (_.isNumber(this.sceneOriginalHeight) && _.isNumber(this.sceneOriginalWidth) && this.scene) {
        newLeft = pMin.x * this.scene.scaleX;
        newTop = pMin.y * this.scene.scaleY;
        newWidth = (pMax.x - pMin.x) * this.scene.scaleX;
        newHeight = (pMax.y - pMin.y) * this.scene.scaleY;
      } else {
        newLeft = pMin.x;
        newTop = pMin.y;
        newWidth = pMax.x - pMin.x;
        newHeight = pMax.y - pMin.y;
      }
      // since we are using width 5 and backend will include the pMax point so...
      newLeft -= newStrokeWidth;
      newTop -= newStrokeWidth;
      newWidth += newStrokeWidth + 1;
      newHeight += newStrokeWidth + 1;
      
      // if (strokeWidthDelta !== 0) {
      //   newLeft -= strokeWidthDelta;
      //   newTop -= strokeWidthDelta;
      //   newWidth += strokeWidthDelta;
      //   newHeight += strokeWidthDelta;
      // }
      
      const rect = new fabric.Rect({
        left: newLeft,
        top: newTop,
        width: newWidth,
        height: newHeight,
        fill: 'transparent',
        stroke: getColorByStr(_.get(feature, 'feature_type')),
        strokeWidth: newStrokeWidth,
        selectable: false,
        strokeUniform: true, // Ensure stroke width remains consistent when scaling
        mouseCursor: 'default',
        hoverCursor: 'default',
      });

      // disable bbox rotation control
      rect.controls = {
        ...fabric.Rect.prototype.controls,
        mtr: new fabric.Control({
          visible: false,
        }),
      };
      // make bbox selectable by per pixel to avoid overlaped bbox can not be selected
      rect.perPixelTargetFind = true;
      rect.targetFindTolerance = 10;
      rect.set('scaledToScene', _.isNumber(this.sceneOriginalHeight) && _.isNumber(this.sceneOriginalWidth) && this.scene);
      this.fabricCanvas.add(rect);
      this.rects.push(rect);

      rect.set('feature_id', _.get(feature, 'feature_id'));
      rect.set('product_id', _.get(feature, 'product_id'));
      rect.set('feature_type', _.get(feature, 'feature_type'));
      rect.set('step', _.get(feature, 'step'));
      rect.set('variant', _.get(feature, 'variant'));
    }
    this.updateSceneZIndex();
    this.fabricCanvas.renderAll();
  };

  removeFeatureFromScene = ({ product_id, step, feature_id }) => {
    // remove from rects
    const rect = this.rects.find((r) => {
      return r.get('product_id') === product_id && r.get('step') === step && r.get('feature_id') === feature_id;
    });
    if (rect) {
      this.fabricCanvas.remove(rect);
      this.rects = this.rects.filter((r) => r !== rect);
    }
    return;
  };

  clearScene = () => {
    if (this.fabricCanvas) {
      this.fabricCanvas.clear();
    }
    this.scene = null;
    this.rects = [];
  };

  toggleRectsVisibility = (visible) => {
    this.rects.forEach((rect) => {
      rect.set('visible', visible);
    });
    this.fabricCanvas.renderAll();
  };

  updateSceneZIndex = () => {
    if (this.scene) {
      this.scene.moveTo(1);
    }
    this.rects.forEach((rect) => {
      rect.moveTo(2);
    });
  };

  lockScene = () => this.sceneLocked = true;

  unlockScene = () => this.sceneLocked = false;

  resetView = () => {
    if (!this.fabricCanvas || !this.scene) return;
    // zoom and pan to view the whole scene
    const canvasWidth = this.fabricCanvas.getWidth();
    const canvasHeight = this.fabricCanvas.getHeight();
    const sceneWidth = this.scene.width * this.scene.scaleX;
    const sceneHeight = this.scene.height * this.scene.scaleY;
    const scaleX = canvasWidth / sceneWidth;
    const scaleY = canvasHeight / sceneHeight;
    const zoom = Math.min(scaleX, scaleY);
    // this.fabricCanvas.zoomToPoint({ x: canvasWidth / 2, y: canvasHeight / 2 }, zoom);
    // this.fabricCanvas.absolutePan({
    //   x: (canvasWidth - sceneWidth * zoom) / 2,
    //   y: (canvasHeight - sceneHeight * zoom) / 2
    // });

    const panX = (canvasWidth - sceneWidth * zoom) / 2;
    const panY = (canvasHeight - sceneHeight * zoom) / 2;

    this.fabricCanvas.viewportTransform = [zoom, 0, 0, zoom, panX, panY];

    if (this.rects.length > 0) {
      // update the rect stroke width
      const newWidth = Math.min(baseBboxStrokeWidth, baseBboxStrokeWidth / zoom);
      const strokeWidthDelta = newWidth - this.rects[0].strokeWidth;
      this.rects.forEach((rect) => {
        rect.set({
          strokeWidth: newWidth,
          left: rect.left - strokeWidthDelta,
          top: rect.top - strokeWidthDelta,
          width: rect.width + strokeWidthDelta,
          height: rect.height + strokeWidthDelta,
        });
        rect.setCoords();
      });
    }

    this.fabricCanvas.renderAll();
  };

  render() {
    return  (
      <div className='h-full w-full' ref={this.containerRef}>
        {super.render()}
      </div>
    );
  }
};