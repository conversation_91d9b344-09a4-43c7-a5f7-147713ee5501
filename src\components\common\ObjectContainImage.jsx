import _ from 'lodash';
import React, { useEffect, useRef, useState } from 'react';
import { serverEndpoint } from '../../common/const';


// for displaying product detection/inference result
const ObjectContainImage = (props) => {
  const {
    productId,
    selectedVariation,
    step,
    onClick,
    productInfo,
  } = props;

  const [dummyCameraData, setDummyCameraData] = useState('');
  const [dimension, setDimensions] = useState({ width: 0, height: 0 });
  const containerRef = useRef();

  useEffect(() => {
    if (!containerRef.current) return;
    // if (_.isEmpty(productId) || !_.isInteger(step)) {
    //   setDummyCameraData('');
    //   return;
    // }
    const fetchData = async (productInfo, step, selectedVariation) => {
      setDimensions({
        width: containerRef.current.clientWidth,
        height: containerRef.current.clientHeight
      });

      // find image uri from product info by step
      const stepInfo = _.find(_.get(productInfo, 'inspectables', []), i => i.variant === selectedVariation && ((step && i.step === Number(step)) || !step));
      if (!stepInfo) return;

      const res = await fetch(`${serverEndpoint}/data?data_uri=${stepInfo.color_map_uri}`);
      // const tmp = await fetch('/test/501319r6L1Gr');
      const blob = await res.blob();
      const reader = new FileReader();
      reader.readAsDataURL(blob);
      const url = await new Promise((resolve) => {
        reader.onload = (event) => {
          resolve(event.target.result);
        };
      });
      setDummyCameraData(url);
    };
    fetchData(productInfo, step, selectedVariation);
  }, [productInfo, step, selectedVariation]);

  return (
    <div
      className='cursor-pointer w-full h-full rounded-[2px] border-[1px] border-gray-2'
      ref={containerRef}
      onClick={() => onClick()}
    >
      <img
        src={dummyCameraData}
        className='object-contain'
        style={{ width: `${dimension.width}px`, height: `${dimension.height}px` }}
        alt='dummy camera data'
      />
    </div>
  );
};

export default ObjectContainImage;