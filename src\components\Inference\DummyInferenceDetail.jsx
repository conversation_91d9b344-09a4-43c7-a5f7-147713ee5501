import React, { useContext, useEffect, useRef, useState } from 'react';
import { getCameraCountByCameraViewLayout, getInspectionCountByInspectionViewLayout, getQueryParams, handleRequestFailed, loadAndDecodePoints, translation } from '../../common/util';
import MainMenuLayout from '../layout/MainMenuLayout';
import ManageBoardsLayout from '../layout/ManageBoardsLayout';
import { useGetProductByIdQuery, useLazyGetInferenceStatusQuery, useStopInferenceMutation } from '../../services/product';
import { useLazyGetAllFeaturesByProductIdAndStepQuery } from '../../services/feature';
import _ from 'lodash';
import { useGetSessionInfoQuery, useLazyGetSessionInfoQuery } from '../../services/session';
import { useDispatch, useSelector } from 'react-redux';
import { systemApi } from '../../services/system';
import { CustomCollapse, lightGray, primaryColor, RoundLabel } from '../../common/darkModeComponents';
import { Button, ConfigProvider, Spin } from 'antd';
import { CameraPreviewContext } from '../Context/Provider';
import { setContainerWindowLoadingLocked } from '../../actions/setting';
import { useLazyGetFrameByProductIdAndStepQuery } from '../../services/frame';
import { serverEndpoint } from '../../common/const';
import ObjectContainImage from './ObjectContainImage';
import SingleCameraPreviewViewer from '../common/viewer/SingleCameraPreviewViewer';
import { ALERT_TYPES, aoiAlert } from '../../common/alert';


const DummyInferenceDetail = (props) => {
  const dispatch = useDispatch();

  const { search } = props.location;
  const searchParams = getQueryParams(search);
  const { step } = searchParams; // step is null then it's a full product view
  const { productId, ipcSessionId, ipcProductId } = props.match.params;

  const autoRefetchSessionInfo = useRef();
  const stepCount = useRef([]);
  const singlePrevgiewCanvesRef = useRef();

  const { data: ipcProduct } = useGetProductByIdQuery(ipcProductId);
  const [getFeatureByStep] = useLazyGetAllFeaturesByProductIdAndStepQuery();
  const [getSessionInfo] = useLazyGetSessionInfoQuery();
  const [stopInference] = useStopInferenceMutation();
  const [getInferenceStatus] = useLazyGetInferenceStatusQuery();
  const [getFrame] = useLazyGetFrameByProductIdAndStepQuery();

  const [curStepFeatures , setCurStepFeatures] = useState([]);
  const [isCurSessionFinished, setIsCurSessionFinished] = useState(true);
  const [pageIniting, setPageIniting] = useState(true);
  const [frames, setFrames] = useState({});
  
  const { data: systemMetadata } = useSelector((state) => systemApi.endpoints.getSystemMetadata.select()(state));

  const {
    curInferenceFrameImageData,
    setCurInferenceFrameImageData,
  } = useContext(CameraPreviewContext);

  const handleFetchAllFrameBack = () => {
    dispatch(setContainerWindowLoadingLocked(true));
    const captureSingleCam = async (step) => {
      const res = await getFrame({ product_id: ipcProductId, step });

      // fetch image capture data and load into context
      if (!_.isEmpty(_.get(res, 'data.image.data_uri'))) {
        let dataRes;
        try {
          dataRes = await fetch(`${serverEndpoint}/data?data_uri=${_.get(res, 'data.image.data_uri')}`);
        } catch (error) {
          handleRequestFailed('fetchImageData', error);
          stepCount.current.push(step);
          if (stepCount.current.length === getInspectionCountByInspectionViewLayout(_.get(systemMetadata, 'inspection_view_layout'))) {
            dispatch(setContainerWindowLoadingLocked(false));
            setPageIniting(false);
          }
          return;
        }
        const blob = await dataRes.blob();
        const curImage = URL.createObjectURL(blob);
        setCurInferenceFrameImageData({ ...curInferenceFrameImageData, [String(step)]: {
          ...curInferenceFrameImageData[String(step)],
          image: curImage,
        }});
      }

      stepCount.current.push(step);
      if (stepCount.current.length === getInspectionCountByInspectionViewLayout(_.get(systemMetadata, 'inspection_view_layout'))) {
        dispatch(setContainerWindowLoadingLocked(false));
        setPageIniting(false);
      }
    };
    // const inspectionCount = getInspectionCountByInspectionViewLayout(_.get(systemMetadata, 'inspection_view_layout'));
    // _.times(inspectionCount, (i) => {
    //   captureSingleCam(i);
    // });
  };

  const handleFetchAllFrame = async () => {
    dispatch(setContainerWindowLoadingLocked(true));
    const inspectionCount = getInspectionCountByInspectionViewLayout(_.get(systemMetadata, 'inspection_view_layout'));
    _.times(inspectionCount, async (i) => {
      const res = await getFrame({ product_id: ipcProductId, step: i });
      if (!_.isEmpty(_.get(res, 'data.image.data_uri'))) {
        setFrames({
          ...frames,
          [i]: _.get(res, 'data.image.data_uri')
        });
      }
    });
    dispatch(setContainerWindowLoadingLocked(false));
    setPageIniting(false);
  };

  useEffect(() => {
    if (_.isEmpty(systemMetadata)) return;
    // for now dummy inference does not in progress status
    // hence we fetch all frame at start
    handleFetchAllFrame();
  }, [systemMetadata]);

  if (pageIniting) {
    return (
      <>
        fetching frame data
      </>
    );
  }

  return (
    <MainMenuLayout>
      <ManageBoardsLayout>
        <div className='flex h-[48px] py-2 justify-between items-center self-stretch'>
          <div className='flex items-center gap-6'>
            <div className='flex items-center gap-1'>
              <span className='font-source text-[12px] font-semibold'>
                {translation('viewBoards.board')}
              </span>
              <span className='font-source text-[12px] font-normal'>
                {_.get(ipcProduct, 'product_name')}
              </span>
            </div>
            <div className='flex items-center gap-1'>
              <span className='font-source text-[12px] font-semibold'>
                {translation('viewBoards.model')}
              </span>
              <span className='font-source text-[12px] font-normal'>
                {_.get(ipcProduct, 'product_model', 'product_model placeholder')}
              </span>
            </div>
            <div className='flex items-center gap-1'>
              <span className='font-source text-[12px] font-semibold'>
                {translation('viewBoards.sn')}
              </span>
              <span className='font-source text-[12px] font-normal'>
                {_.get(ipcProduct, 'serial_number', 'product_sn placeholder')}
              </span>
              <div
                className='flex items-center justify-center h-4 w-4 cursor-pointer'
                onClick={() => {}}
              >
                <img src='/img/icn/icn_pencil_white.svg' alt='edit' className='w-2.5 h-2.5 fill-[#fff]' />
              </div>
            </div>
          </div> 
          <div className='flex items-center gap-6'>
            <div className='flex gap-2 self-stretch items-center'>
              <span className='font-source text-[12px] font-normal'>
                {`${getInspectionCountByInspectionViewLayout(_.get(systemMetadata, 'inspection_view_layout'))} ${translation('viewBoards.images')}`}
              </span>
            </div>
            {isCurSessionFinished &&
              <RoundLabel>
                <div className='flex items-center gap-2'>
                  <img src='/img/icn/icn_checkerFlag_white.svg' alt='flag' className='w-[9px] h-[10px] fill-[#fff]' />
                  <span className='font-source text-[12px] font-semibold'>
                    {translation('viewBoards.detectionFinished')}
                  </span>
                </div>
              </RoundLabel>
            }
            <Button
              borderColor={lightGray}
              onClick={() => {
                window.location.href = '/aoi/live-dashboard';
              }}
            >
              <div className='flex items-center gap-2 justify-center'>
                <span className='font-source text-[12px] font-normal'>
                  {translation('viewInspection.backToLiveDashboard')}
                </span>
              </div>
            </Button>
            {!isCurSessionFinished ? (
              // detection is progresss
              <RoundLabel bgColor={primaryColor} textColor='#333'>
                <div className='flex items-center gap-2'>
                  <ConfigProvider
                    theme={{
                      components: {
                        Spin: {
                          dotSize: 10
                        },
                      },
                    }}
                  >
                    <Spin />
                  </ConfigProvider>
                  <span className='font-source text-[12px] font-semibold'>
                    {translation('viewInspection.inspectionInprogress')}
                  </span>
                </div>
              </RoundLabel>
              
            ) : (
              <Button
                borderColor={lightGray}
                onClick={async () => {
                  const res = await getInferenceStatus();
                    if (_.get(res, 'data.status') === 'stopped') {
                      aoiAlert(translation('notification.error.inferenceAlreadyStopped'), ALERT_TYPES.COMMON_ERROR);
                      return;
                    }
                  await stopInference();
                }}
                danger
              >
                <div className='flex items-center gap-2 justify-center'>
                  <img src='/img/icn/icn_cross_red.svg' className='w-2 h-2' />
                  <span className='font-source text-[12px] font-normal'>
                    {translation('viewInspection.stopInspection')}
                  </span>
                </div>
              </Button>
            )}
          </div> 
        </div>
        <div className='flex px-8 items-start gap-2 flex-1 self-stretch'>
          {/* defect list */}
          <div className='flex w-[223px] flex-col items-start self-stretch gap-6'>
            <div className='flex py-2 flex-col justify-center items-start gap-1 self-stretch'>
              <span className='font-source text-[14px] font-semibold'>
                {translation('viewInspection.defectsReviewed', { viewedDefectsCount: '0', totalDefects: '0' })}
              </span>
              <div className='w-full h-[1px] bg-AOI-blue' />
              <span className='font-source text-[12px] font-normal'>
                {translation('viewInspection.totalConfirmedDefects', { totalConfirmedDefects: '0' })}
              </span>
            </div>
            <div className='flex flex-col items-start self-stretch'>
              <div className='flex py-2 justify-between items-center content-center self-stretch flex-wrap'>
                <span className='font-source text-[14px] font-semibold'>
                  {translation('viewInspection.defectList')}
                </span>
                <div className='flex items-center gap-1'>
                  <div className='flex w-3 h-3 flex-col justify-center items-center'>
                    <img src='/img/icn/icn_warning_red.svg' className='w-4 h-[10.5px]' alt='warning' />
                  </div>
                  <span className='font-source text-[12px] font-normal'>
                    {translation('viewInspection.defectsDetected', { totalDefectsCount: '0' })}
                  </span>
                </div>
              </div>
              <CustomCollapse
                style={{ width: '100%' }}
                activeKey={[step]}
                onChange={(key) => {
                  if (key.length === 0) {
                    props.history.push(`/aoi/view-inference/${ipcSessionId}/product/${ipcProductId}`);
                    return;
                  };
                  props.history.push(`/aoi/view-inference/${ipcSessionId}/product/${ipcProductId}?step=${key[1]}`);
                }}
                items={_.times(_.sum(_.get(systemMetadata, 'inspection_view_layout')), (index) => (
                  {
                    key: index,
                    label: <div className='flex items-center h-full'>
                      <span className='font-source text-[12px] font-normal'>
                      {`${translation('viewInspection.image')} ${index + 1}`}
                      </span>
                    </div>,
                    children: <div
                      className='flex justify-between items-center pt-1 pr-2 pb-1 pl-4 self-stretch'
                      style={{ background: 'rgba(255, 255, 255, 0.03)' }}
                    >
                      <div className='flex gap-2 items-center'>
                        <div className='flex w-3 h-3 justify-center items-center'>
                          <img src='/img/icn/icn_bbox_white.svg' className='w-[10px] h-[10px]' alt='bbox' />
                        </div>
                        <span className='font-source text-[12px] font-normal'>
                          {translation('productAnnotation.annotate')}
                        </span>
                      </div>
                      <div className='flex h-4 w-4 justify-center items-center'>
                        <img src='/img/icn/icn_rightArrow_gray.svg' className='w-2.5 h-2.5' alt='rightArrow' />
                      </div>
                    </div>
                  }
                ))}
              />
            </div>
          </div>
          <div
            className='flex p-2 flex-col gap-2 items-start flex-1 self-stretch rounded-[2px]'
            style={{ background: 'rgba(255, 255, 255, 0.10)' }}
          >
            <div className='flex items-center gap-2 p-1 self-stretch rounded-[4px]'>
              <img src='/img/icn/icn_customGridLayout_white.svg' alt='grid' className='w-[16px] h-[11px] fill-[#fff]' />
              <span className='font-source text-[12px] font-normal'>
                {translation('viewBoards.fullProduct', { productName: _.get(ipcProduct, 'product_name') })}
              </span>
            </div>
            <div className='flex flex-col w-full h-full gap-1 self-stretch'>
              {_.map(_.get(systemMetadata, 'inspection_view_layout'), (cols, row) => (
                <div
                  className={`grid gap-x-1 self-stretch h-full`}
                  style={{ gridTemplateColumns: `repeat(${cols}, 1fr)` }}
                  key={row}
                >
                  {_.times(cols, (col) => (
                    // <ObjectContainImage
                    //   key={col}
                    //   onClick={() => {
                    //     props.history.push(`/aoi/view-inference/${ipcSessionId}/product/${ipcProductId}?step=${_.sum(_.slice(_.get(systemMetadata, 'inspection_view_layout'), 0, row)) + col}`);
                    //   }}
                    //   step={_.sum(_.slice(_.get(systemMetadata, 'inspection_view_layout'), 0, row)) + col}
                    //   dataUri={_.get(frames, String(_.sum(_.slice(_.get(systemMetadata, 'inspection_view_layout'), 0, row)) + col))}
                    // />
                    <SingleCameraPreviewViewer
                      displayCanvasRef={singlePrevgiewCanvesRef}
                      dataUri={_.get(frames, String(_.sum(_.slice(_.get(systemMetadata, 'inspection_view_layout'), 0, row)) + col))}
                    />
                  ))}
                </div>
              ))}
            </div>
          </div>
        </div>
      </ManageBoardsLayout>
    </MainMenuLayout>
  );
};

export default DummyInferenceDetail;