import { Button, ConfigProvider, Modal } from 'antd';
import React, { useState } from 'react';
import { handleRequestFailed, translation } from '../../common/util';
import { ALERT_TYPES, aoiAlert } from '../../common/alert';
import _ from 'lodash';
import { useRunDetectionPipelineMutation } from '../../services/product';
import { AOIGreenPrimaryButtonConfig } from '../../common/darkModeComponents';


const DefineProductRegister = (props) => {
  const {
    isOpened,
    setIsOpened,
    selectedVariation,
    productInfo,
    stepNumber,
  } = props;
  
  const [isLoading, setIsLoading] = useState(false);

  const [register] = useRunDetectionPipelineMutation();
  
  return (
    <Modal
      open={isOpened}
      onCancel={() => {
        if (isLoading) return;
        setIsOpened(false)
      }}
      title={
        <span className='font-source text-[16px] font-semibold'>
          {translation('productAnnotation.reregisterTitle')}
        </span>
      }
      footer={
        <div className='flex flex-col items-start gap-2 self-stretch'>
          <AOIGreenPrimaryButtonConfig>
            <Button
              style={{ width: '100%' }}
              loading={isLoading}
              onClick={async () => {
                const selectedInspectable = _.find(_.get(productInfo, 'inspectables'), i => i.variant === selectedVariation);
                if ((_.isEmpty(selectedInspectable) || _.get(selectedInspectable, 'primordial')) && _.get(productInfo, 'inspectables', []).length > 1) {
                  aoiAlert(translation('notification.error.canNotReregisterTheOriginalProduct'), ALERT_TYPES.COMMON_ERROR);
                  return;
                }
                setIsLoading(true);
                const res = await register({ product_id: _.get(productInfo, 'product_id'), variant: selectedVariation });
                if (res.error) {
                  handleRequestFailed('runDetectionPipeline', res.error);
                  setIsLoading(false);
                  return;
                }
                setIsOpened(false);
                window.location.href = `/aoi/edit-product/${_.get(productInfo, 'product_id')}?editStep=1${_.isEmpty(stepNumber) ? '' : `&detectionStep=${stepNumber}`}`;
              }}
            >
              <span className='font-source text-[12px] font-normal'>
                {translation('common.continue')}
              </span>
            </Button>
          </AOIGreenPrimaryButtonConfig>
        </div>
      }
    >
      <div className='flex py-6 flex-col items-start gap-8 self-stretch'>
        <span className='text-source text-[14px] font-normal'>
          {translation('productAnnotation.reregisterDesc')}
        </span>
      </div>
    </Modal>
  );
};

export default DefineProductRegister;