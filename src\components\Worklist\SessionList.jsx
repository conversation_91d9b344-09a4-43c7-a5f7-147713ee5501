import { Button, DatePicker, Select, Table, Tooltip } from 'antd';
import enUS from 'antd/es/date-picker/locale/en_US';
import enLocale from 'antd/es/locale/en_US';
import cnLocale from 'antd/es/locale/zh_CN';
import _, { get } from 'lodash';
import React, { useEffect, useState } from 'react';
import { customZhCNDatePickerLocale } from '../../common/const';
import { backendAutoGenTimeToDisplayString, backendTimestampToDisplayString, convertBackendTimestampToMoment, handleRequestFailed, toLocalISOString, translation } from '../../common/util';
import i18n from '../../i18n';
import { useLazyGetAllSessionsQuery } from '../../services/session';
import { DarkTable } from '../../common/darkModeComponents';


const { RangePicker } = DatePicker;

const SessionList = (props) => {
  const {
    setDisplayContent,
    allProducts,
    selectedGoldenProdId,
    setSelectedGoldenProdId,
    startTimeInSession,
    setStartTimeInSession,
    endTimeInSession,
    setEndTimeInSession,
    setIsExportModalOpened,
    setIpcSessionId,
    setSelectedSession,
    pagination,
    setPagination,
    refreshToggle,
    setRefreshToggle,
  } = props;

  // const [pageSize, setPageSize] = useState(0);
  // const [pagination, setPagination] = useState({ current: 0, pageSize: 0, total: 0 });
  const [getSessionsQueryParam, setGetSessionsQueryParam] = useState({});
  const [displayedSessions, setDisplayedSessions] = useState([]);

  // const { data: allSessions } = useGetAllSessionsQuery(getSessionsQueryParam);
  const [getSessions] = useLazyGetAllSessionsQuery();

  const handleFilterUpdate = async (query, pagination) => {
    const res = await getSessions(query);
    if (res.error) {
      handleRequestFailed('getSessions', res.error);
      setDisplayedSessions([]);
      return;
    }

    setDisplayedSessions(_.get(res, 'data.data', []));
    setPagination({
      ...pagination,
      total: _.get(res, 'data.pageCount', 0) * pagination.pageSize,
    });
  };

  const cols = [
    {
      title: translation('home.sessionId'),
      key: 'session_id',
      render: (text, record) => {
        return (
          <span className='font-source text-[12px] font-normal'>
            {record.ipc_session_id}
          </span>
        );
      },
    },
    {
      title: translation('worklist.goldenProducts'),
      key: 'golden_products',
      render: (text, record) => {
        return (
          <div className='flex items-center gap-2 h-[32px]'>
            <div className='flex items-center gap-1 py-0.5 pl-2 pr-3 rounded-[2px] bg-gray-1'>
              <img src='/img/icn/icn_star_yellow.svg' alt='star' className='w-[14px] h-[14px]' />
              <span className='font-source text-[14px] font-normal'>
                {_.get(record, 'golden_product_ids', []).length}
              </span>
            </div>
            <div className='flex items-center gap-1 h-[32px] w-full overflow-y-auto flex-wrap'>
              {/* {_.map([1066, 1062, 1061, 1054], (pid, index) => { */}
              {_.map(_.get(record, 'golden_product_ids', []), (pid, index) => {
                return <span className={`font-source text-[12px] font-${selectedGoldenProdId === pid ? 'semibold' : 'formal'} text-${selectedGoldenProdId === pid ? 'white' : '[#BDBDBD]'}`}>
                  {`${_.get(_.find(allProducts, p => Number(p.product_id) === pid), 'product_name', 'unknown product')}`}
                </span>
              })}
            </div>
          </div>
        );
      },
    },
    {
      title: translation('worklist.startDate'),
      key: 'start_time',
      render: (text, record) => {
        return (
          <span className='font-source text-[12px] font-normal'>
            {_.get(record, 'started_at') ? backendAutoGenTimeToDisplayString(record.started_at) : ''}
          </span>
        );
      },
      // sorter: (a, b) => convertBackendTimestampToMoment(b.started_at) - convertBackendTimestampToMoment(a.started_at),
    },
    {
      title: translation('worklist.totalProducts'),
      key: 'total_products',
      render: (text, record) => {
        return (
          <span className='font-source text-[12px] font-normal'>
            {record.total_product_count}
          </span>
        );
      },
    },
    {
      title: translation('worklist.productok'),
      key: 'product_ok',
      render: (text, record) => {
        return (
          <span className='font-source text-[12px] font-normal'>
            {_.get(record, 'total_product_count') - _.get(record, 'defective_product_count', 0)}
          </span>
        );
      },
    },
    {
      title: translation('worklist.productng'),
      key: 'product_ng',
      render: (text, record) => {
        return (
          <span className='font-source text-[12px] font-normal'>
            {_.get(record, 'defective_product_count', 0)}
          </span>
        );
      },
    },
    {
      title: translation('worklist.passRate'),
      key: 'pass_rate',
      render: (text, record) => {
        return (
          <span className='font-source text-[12px] font-normal'>
            {_.get(record, 'total_product_count') === 0 ? '' : `${Math.round((1 - _.get(record, 'defective_product_count', 0) / _.get(record, 'total_product_count')) * 100, 4)}% ${translation('worklist.pass')}`}
          </span>
        );
      },
    },
    {
      title: translation('worklist.actions'),
      key: 'actions',
      render: (text, record) => (
        // <div className='flex justify-between flex-1 items-center'>
        <div className='flex items-center'>
          <Button
            type='text'
            onClick={() => {
              if (!_.get(record, 'ipc_session_id', null) || _.isEmpty(_.get(record, 'golden_product_ids', []))) return;
              for (let i = 0; i < _.get(record, 'golden_product_ids', []).length; i++) {
                if (!_.find(allProducts, p => Number(p.product_id) === _.get(record, `golden_product_ids.${i}`))) return;
              }
              setIpcSessionId(_.get(record, 'ipc_session_id', null));
              setSelectedSession(record);
              setDisplayContent('inspection');
            }}
          >
            <span className='font-source text-[12px] font-normal text-AOI-blue'>
              {translation('worklist.open')}
            </span>
          </Button>
          {/* <div
            className='flex w-6 h-6 justify-center items-center cursor-pointer'
            onClick={() => {}}
          >
            <img className='w-[10.7px] h-[12px]' src='/img/icn/icn_delete_red.svg' alt='delete' />
          </div> */}
        </div>
      ),
    }
  ];

  useEffect(() => {
    if (pagination.pageSize === 0) return;

    const queryParam = {};

    if (_.isInteger(selectedGoldenProdId)) queryParam.golden_product_id = selectedGoldenProdId;
    if (startTimeInSession) {
      // dayjs in local timezone to UTC
      queryParam.start_datetime = toLocalISOString(new Date(startTimeInSession));
    }
    if (endTimeInSession) queryParam.end_datetime = toLocalISOString(new Date(endTimeInSession));
    queryParam.page = 0;
    queryParam.limit = pagination.pageSize;

    setGetSessionsQueryParam(queryParam);

    setPagination({
      ...pagination,
      current: 1,
    });

    handleFilterUpdate(queryParam, {
      ...pagination,
      current: 1,
    });
  }, [
    selectedGoldenProdId,
    startTimeInSession,
    endTimeInSession,
  ]);

  useEffect(() => {
    if (refreshToggle <= 0) return;
    handleFilterUpdate({
      ...getSessionsQueryParam,
      page: pagination.current - 1,
      limit: pagination.pageSize,
    }, pagination);
  }, [refreshToggle]);

  useEffect(() => {
    const tableTBodyHeight = window.innerHeight - 340;
    // setPageSize(Math.max(Math.floor(tableTBodyHeight / 55), 1));
    setPagination({
      ...pagination,
      pageSize: Math.max(Math.floor(tableTBodyHeight / 65), 1),
    });

    handleFilterUpdate({
      page: 0,
      limit: Math.max(Math.floor(tableTBodyHeight / 65), 1),
    }, {
      current: 1,
      pageSize: Math.max(Math.floor(tableTBodyHeight / 65), 1),
      total: 0,
    });

    const handleResize = () => {
      const tableTBodyHeight = window.innerHeight - 340;
      // setPageSize(Math.max(Math.floor(tableTBodyHeight / 55), 1));
      setPagination({
        ...pagination,
        pageSize: Math.max(Math.floor(tableTBodyHeight / 65), 1),
      });
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
      setRefreshToggle(0);
    };
  }, []);

  return (
    <div className='flex flex-col items-start gap-4 flex-1 self-stretch'>
      <div className='flex py-2 items-center gap-4 self-stretch'>
        <div className='flex items-center gap-[2px] self-stretch'>
          <Select
            showSearch
            options={
              _.isEmpty(allProducts) ? []
              : _.filter(allProducts, (product) => product.is_golden === true).map((product) => ({
                value: Number(product.product_id),
                label: <span className='font-source text-[12px] font-normal'>{product.product_name}</span>,
              }))
            }
            value={selectedGoldenProdId}
            onChange={(value) => setSelectedGoldenProdId(value)}
            placeholder={<span className='font-source text-[12px] font-normal'>{translation('worklist.filterByGoldenProduct')}</span>}
            style={{ width: '200px' }}
            popupMatchSelectWidth={false}
            allowClear
          />
        </div>
        <RangePicker
          locale={i18n.language === 'cn' ? customZhCNDatePickerLocale : enUS}
          showTime
          onCalendarChange={(value) => {
            setStartTimeInSession(_.get(value, '0', null));
            setEndTimeInSession(_.get(value, '1', null));
          }}
          value={[startTimeInSession, endTimeInSession]}
        />
        <Tooltip
          title={translation('worklist.clearFilter')}
        >
          <div className='flex w-[32px] h-[32px] justify-center items-center cursor-pointer' onClick={() => {
            setSelectedGoldenProdId(null);
            setStartTimeInSession(null);
            setEndTimeInSession(null);
          }}>
            <img className='w-[16px] h-[16px]' src='/img/icn/icn_cancelFilter_blue.svg' alt='cancelFilter' />
          </div>
        </Tooltip>
        <Tooltip
          title={translation('worklist.export')}
        >
          <div className='flex w-[32px] h-[32px] justify-center items-center cursor-pointer' onClick={() => {
            setIsExportModalOpened(true);
          }}>
            <img className='w-[16px] h-[16px]' src='/img/icn/icn_download_blue.svg' alt='filter' />
          </div>
        </Tooltip>
      </div>
      <DarkTable
        locale={i18n.language === 'cn' ? cnLocale.Table : enLocale.Table}
        style={{ width: '100%' }}
        columns={cols}
        pagination={{
          pageSize: pagination.pageSize,
          total: pagination.total,
          current: pagination.current,
          hideOnSinglePage: true,
          showSizeChanger: false,
          showQuickJumper: true,
        }}
        rowHoverable={false}
        dataSource={displayedSessions}
        onChange={(pagination) => {
          setPagination({
            ...pagination,
            current: pagination.current,
          });
          handleFilterUpdate({
            ...getSessionsQueryParam,
            page: pagination.current - 1,
            limit: pagination.pageSize,
          }, pagination);
        }}
      />
    </div>
  );
};

export default SessionList;