import React from 'react';
import { handleRequestFailed, translation } from '../../common/util';
import { Button, Modal } from 'antd';
import { useArchiveAllDataMutation } from '../../services/system';
import { ALERT_TYPES, aoiAlert } from '../../common/alert';


const ArchiveDataComfirmation = (props) => {
  const {
    isOpened,
    setIsOpened,
  } = props;

  const [archiveAllData] = useArchiveAllDataMutation();

  return (
    <Modal
      open={isOpened}
      onCancel={() => setIsOpened(false)}
      title={
        <span className='font-source text-[16px] font-semibold'>
          {translation('dataStorageManage.archiveAllData')}
        </span>
      }
      footer={null}
    >
      <div className='flex py-6 px-4 flex-col items-start gap-8 self-stretch'>
        <span className='font-source text-[12px] font-normal'>
          {translation('dataStorageManage.archiveAllDataDesc')}
        </span>
        <div className='flex items-center gap-2 self-stretch'>
          <Button
            style={{ width: '100%' }}
            onClick={() => {
              const run = async () => {
                const res = await archiveAllData();
                if (res.error) {
                  handleRequestFailed('archiveAllData', res.error);
                  aoiAlert(translation('notification.error.archiveAllDataFailed'), ALERT_TYPES.COMMON_ERROR);
                  return;
                }

                if (res.data.code === 409) {
                  aoiAlert(translation('dataStorageManage.previousArchiveNotCompleted'), ALERT_TYPES.COMMON_ERROR);
                  return;
                }

                setIsOpened(false);
              };

              run();
            }}
          >
            <span className='font-source text-[12px] font-normal'>
              {translation('dataStorageManage.startDataArchive')}
            </span>
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default ArchiveDataComfirmation;
