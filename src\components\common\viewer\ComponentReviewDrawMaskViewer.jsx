import { fabric } from 'fabric-with-erasing';
import _ from 'lodash';
import React from 'react';
import { serverEndpoint } from '../../../common/const';
import { cropFrameDataUrlBasedOnFeatureRoi, debounce } from '../../../common/util';
import TwoDBaseViwer from './TwoDBaseViewer';


export default class ComponentReviewDrawMaskViewer extends TwoDBaseViwer {
  constructor(props) {
    super(props, props.displayCanvasRef);
    this.displayCanvasRef = props.displayCanvasRef;
    this.scene = null; // 2d image
    this.fabricCanvas = null;
    this.sceneOriginalWidth = 0;
    this.sceneOriginalHeight = 0;
    this.sceneLocked = false;
    this.containerRef = React.createRef();
    this.paths = [];
    this.curMode = 'pan'; // pan, pencil, eraser
    this.prevMask = null;
    this.isPanning = false;
    this.curRoi = null;
    this.handleWindowResize = React.createRef();
  };

  componentDidMount() {
    if (!this.displayCanvasRef.current) return;

    // init fabric canvas
    const fabricCanvas = new fabric.Canvas(
      this.displayCanvasRef.current,
      {
        // Disable uniform scaling
        uniformScaling: false,
      }
    );
    this.fabricCanvas = fabricCanvas;
    fabricCanvas.skipOffscreen = true;
    this.fabricCanvas.contextContainer.imageSmoothingEnabled = true;

    // enable webgl filter
    let filterBackend = null;
    try {
      filterBackend = new fabric.WebglFilterBackend();
      // console.log('Use WebGL filter backend');
    } catch (e) {
      // console.error('WebGL backend is not supported, using 2d canvas backend');
      filterBackend = new fabric.Canvas2dFilterBackend();
    }
    fabricCanvas.filterBackend = filterBackend;
    if (fabric.isWebglSupported()) {
      console.log('WebGL is supported, increase texture size to 65536');
      fabric.textureSize = 65536; // ow only partial image will be rendered if sharpness is enabled since our image is large
    }

    this.fabricCanvas.backgroundColor = '#000000';

    // Enable Panning
    let lastPosX, lastPosY;
    
    fabricCanvas.on('mouse:down', (opt) => {
      if (this.curMode === 'pencil' || this.curMode === 'eraser') return;
      if (this.sceneLocked) return;

      if (opt.target) {
        this.isPanning = false;
        return;
      }
      
      this.isPanning = true;
      lastPosX = opt.e.clientX;
      lastPosY = opt.e.clientY;
      fabricCanvas.selection = false;
      fabricCanvas.setCursor('grab');
    });

    fabricCanvas.on('mouse:move', (opt) => {
      if (this.curMode === 'pencil' || this.curMode === 'eraser') return;
      if (this.sceneLocked) return;
      if (this.isPanning && opt && opt.e) {
        fabricCanvas.setCursor('grab');
        // const delta = new fabric.Point(opt.e.movementX, opt.e.movementY);
        // fabricCanvas.relativePan(delta);
        let e = opt.e;
        let vpt = fabricCanvas.viewportTransform;
        vpt[4] += e.clientX - lastPosX;
        vpt[5] += e.clientY - lastPosY;
        fabricCanvas.requestRenderAll();
        lastPosX = e.clientX;
        lastPosY = e.clientY;
      }
    });

    fabricCanvas.on('mouse:up', (opt) => {
      if (this.curMode === 'pencil' || this.curMode === 'eraser') return;
      if (this.sceneLocked) return;

      this.isPanning = false;
      fabricCanvas.setCursor('default');
    });

    // Enable Zooming
    fabricCanvas.on('mouse:wheel', (opt) => {
      const delta = opt.e.deltaY;
      let zoom = fabricCanvas.getZoom();
      zoom *= 0.999 ** delta;
      // if (zoom > 20) zoom = 20;
      // if (zoom < 0.01) zoom = 0.01;
      fabricCanvas.zoomToPoint({ x: opt.e.offsetX, y: opt.e.offsetY }, zoom);
      opt.e.preventDefault();
      opt.e.stopPropagation();

      // this.fabricCanvas.contextContainer.imageSmoothingEnabled = false;
      if (this.scene) {
        // this.scene.imageSmoothing = false;
        this.scene.objectCaching = false;
      }
    });

    fabricCanvas.on('path:created', (e) => {
      e.path.set({
        selectable: false,
        evented: false,
      });
      this.paths.push(e.path);
    });

    const updateCanvasSize = () => {
      if (!this.containerRef.current || !this.fabricCanvas) return;
      const canvasWidth = this.containerRef.current.offsetWidth + 3;
      const canvasHeight = this.containerRef.current.offsetHeight + 3;
      this.fabricCanvas.setWidth(canvasWidth);
      this.fabricCanvas.setHeight(canvasHeight);
      this.fabricCanvas.renderAll();
    };

    this.handleWindowResize.current = debounce(updateCanvasSize, 300);

    window.addEventListener('resize', this.handleWindowResize.current);
  }

  componentWillUnmount() {
    if (this.handleWindowResize.current) window.removeEventListener('resize', this.handleWindowResize.current);
  }

  componentDidUpdate(prevProps) {
    if (this.props.dataUri !== prevProps.dataUri || this.props.roi !== prevProps.roi) {
      this.clearScene();
      // console.log('load scene', this.containerRef.current.offsetWidth, this.containerRef.current.offsetHeight);
      this.loadScene(
        this.props.dataUri,
        this.containerRef.current.offsetWidth + 3,
        this.containerRef.current.offsetHeight + 3,
        this.props.roi,
      );
    }
    if (this.props.drawingMode !== prevProps.drawingMode) {
      this.handleChangeMode(this.props.drawingMode);
    }
    if (this.props.pencilStrokeWidth !== prevProps.pencilStrokeWidth) {
      this.fabricCanvas.freeDrawingBrush.width = Number(this.props.pencilStrokeWidth);
    }
    if (this.props.pencilStrokeColor !== prevProps.pencilStrokeColor) {
      this.fabricCanvas.freeDrawingBrush.color = this.props.pencilStrokeColor;
    }

    if (prevProps.curDisplayOptionsBrightness !== this.props.curDisplayOptionsBrightness) this.updateSceneBrightness(this.props.curDisplayOptionsBrightness);
    if (prevProps.curDisplayOptionsContrast !== this.props.curDisplayOptionsContrast) this.updateSceneContrast(this.props.curDisplayOptionsContrast);
    if (prevProps.curDisplayOptionsSaturation !== this.props.curDisplayOptionsSaturation) this.updateSceneSaturation(this.props.curDisplayOptionsSaturation);
    if (prevProps.isSharpnessEnabled !== this.props.isSharpnessEnabled) this.updateSceneSharpness(this.props.isSharpnessEnabled);
  }

  updateSceneBrightness = (brightness) => {
    if (this.scene) {
      // brightness is a value between 0 to 100 so convert to -1 to 1
      if (_.get(this.scene, 'filters[0]')) {
        this.scene.filters[0].brightness = (brightness - 50) / 50;
        this.scene.applyFilters();
      } else {
        this.scene.filters = [new fabric.Image.filters.Brightness({ brightness: (brightness - 50) / 50 })];
        this.scene.applyFilters();
      }
      this.fabricCanvas.renderAll();
    }
  };

  updateSceneContrast = (contrast) => {
    if (this.scene) {
      // contrast is a value between 0 to 100 so convert to -1 to 1
      if (_.get(this.scene, 'filters[1]')) {
        this.scene.filters[1].contrast = (contrast - 50) / 50;
        this.scene.applyFilters();
      } else {
        this.scene.filters = [new fabric.Image.filters.Brightness({ brightness: 0 }), new fabric.Image.filters.Contrast({ contrast: (contrast - 50) / 50 })];
        this.scene.applyFilters();
      }
      this.fabricCanvas.renderAll();
    }
  };

  updateSceneSaturation = (saturation) => {
    if (this.scene) {
      // saturation is a value between 0 to 100 so convert to -1 to 1
      if (_.get(this.scene, 'filters[2]')) {
        this.scene.filters[2].saturation = (saturation - 50) / 50;
        this.scene.applyFilters();
      } else {
        this.scene.filters = [new fabric.Image.filters.Brightness({ brightness: 0 }), new fabric.Image.filters.Contrast({ contrast: 0 }), new fabric.Image.filters.Saturation({ saturation: (saturation - 50) / 50 })];
        this.scene.applyFilters();
      }
      this.fabricCanvas.renderAll();
    }
  };

  updateSceneSharpness = (enabled) => {
    if (this.scene) {
      // remove previous sharpness filter
      this.scene.filters = this.scene.filters.filter((filter) => !(filter instanceof fabric.Image.filters.Convolute));
      if (enabled) {
        this.scene.filters.push(new fabric.Image.filters.Convolute({
          matrix: [ 0, -1, 0, -1, 5, -1, 0, -1, 0 ]
        }));
        
      }
      this.scene.applyFilters();
      this.fabricCanvas.renderAll();
    }
  };

  handleChangeMode = (mode) => {
    if (!this.fabricCanvas) return;
    this.curMode = mode;
    if (mode === 'pan') {
      this.fabricCanvas.isDrawingMode = false;
    } else if (mode === 'pencil') {
      this.fabricCanvas.isDrawingMode = true;
      this.fabricCanvas.freeDrawingBrush = new fabric.PencilBrush(this.fabricCanvas);
      this.fabricCanvas.freeDrawingBrush.width = Number(this.props.pencilStrokeWidth);
      this.fabricCanvas.freeDrawingBrush.color = this.props.pencilStrokeColor;
    } else if (mode === 'eraser') {
      this.fabricCanvas.isDrawingMode = true;
      this.fabricCanvas.freeDrawingBrush = new fabric.EraserBrush(this.fabricCanvas);
      this.fabricCanvas.freeDrawingBrush.width = Number(this.props.pencilStrokeWidth);
      this.scene.set('erasable', false);
    }
  };

  outputDrawnMaskAsBase64 = () => {
    if (!this.fabricCanvas || !this.scene) return;
    if (_.isEmpty(this.paths)) return;

    // clone paths to a static fabric canvas and output and lower canvas's image data
    // init the canvas with the same size as the scene
    const tmpCanvas = new fabric.StaticCanvas(null, {
      width: this.sceneOriginalWidth,
      height: this.sceneOriginalHeight,
      // NOTE: it's essential to set enableRetinaScaling to false otherwise the canvas size will be scaled by the browser zoom level
      // if brower zoom is not 1 then the path copied to the static canvas will be scaled as well and the output image data will be incorrect
      enableRetinaScaling: false,
    });

    // load first the previous mask if exists
    if (this.prevMask) {
      const clonePrevMask = fabric.util.object.clone(this.prevMask);
      tmpCanvas.add(clonePrevMask);
    }
  
    _.forEach(this.paths, (path) => {
      const clonePath = fabric.util.object.clone(path);
      tmpCanvas.add(clonePath);
    });

    tmpCanvas.renderAll();

    const imageData = tmpCanvas.lowerCanvasEl.getContext('2d').getImageData(
      0,
      0,
      this.curRoi.pMax.x - this.curRoi.pMin.x,
      this.curRoi.pMax.y - this.curRoi.pMin.y
    );

    const data = imageData.data;
    // console.log('data', data);
    const grayscaleArray = [];
    // we support erasing as well so we need check if all the drawn pixels are erased
    let allErased = true;
    for (let i = 0; i < data.length; i += 4) {
      if (data[i + 3] !== 0) allErased = false;
      // Assuming that the drawn pixels will have a non-zero alpha channel (data[i + 3])
      const alpha = data[i + 3];
      grayscaleArray.push(alpha);
    }

    // console.log('grayscaleArray', grayscaleArray);

    if (allErased) return;

    const uint8Array = new Uint8Array(grayscaleArray);
    let decoded = '';
    for (let i = 0; i < uint8Array.length; i++) {
      decoded += String.fromCharCode(uint8Array[i]);
    }
    const base64 = btoa(decoded);

    // dispose tmp canvas
    tmpCanvas.dispose();

    return base64;
  };

  loadCroppedFrame = async (dataUri, feature, prevMaskUri) => {
    if (!_.get(feature, 'roi.points')) return;

    const pMin = _.get(feature, 'roi.points[0]');
    const pMax = _.get(feature, 'roi.points[1]');

    let res;
    try {
      res = await fetch(`${serverEndpoint}/data?data_uri=${dataUri}`);
    } catch (e) {
      console.error(e);
      return;
    }

    // console.log('load cropped frame', dataUri, pMin, pMax);

    const blob = await res.blob();
    const reader = new FileReader();
    reader.readAsDataURL(blob);
    const dataUrl = await new Promise((resolve, reject) => {
      reader.onload = (event) => {
        resolve(event.target.result);
      };
    });

    let uint8Array;
    if (!_.isEmpty(prevMaskUri)) {
      let prevMaskRes;
      try {
        prevMaskRes = await fetch(`${serverEndpoint}/data?data_uri=${prevMaskUri}`);
      } catch (e) {
        console.error(e);
        return;
      }
      const blob = await prevMaskRes.blob();
      // convert to uint8 array
      const reader = new FileReader();
      reader.readAsArrayBuffer(blob);
      uint8Array = await new Promise((resolve, reject) => {
        reader.onload = (event) => {
          resolve(new Uint8Array(event.target.result));
        };
      });
    }
    
    this.loadScene(
      dataUrl,
      this.containerRef.current.offsetWidth + 3,
      this.containerRef.current.offsetHeight + 3,
      { pMin, pMax },
      uint8Array,
    );
  };

  loadScene = async (dataUrl, canvasWidth, canvasHeight, roi, prevMask) => {
    // console.log('load scene in mask annotation', this.containerRef.current.offsetWidth, this.containerRef.current.offsetHeight);
    if (!this.fabricCanvas || !this.displayCanvasRef.current) return;

    if (!_.get(roi, 'pMin') || !_.get(roi, 'pMax')) return;

    // console.log('load scene in mask annotation', roi);

    this.curRoi = roi;

    // dispose previous scene
    if (this.scene) {
      this.fabricCanvas.remove(this.scene);
      this.scene = null;
    }
    this.paths = [];

    // const cropppedFrameDataUrl = await cropFrameDataUrlBasedOnFeatureRoi(dataUrl, roi);
    const cropppedFrameDataUrl = dataUrl;

    fabric.Image.fromURL(cropppedFrameDataUrl, (img) => {
      img.set({
        selectable: false,
        evented: false,
      })
      // img.imageSmoothing = false;
      img.objectCaching = false;

      this.sceneOriginalWidth = img.width;
      this.sceneOriginalHeight = img.height;
      const { fabricCanvas } = this;

      fabricCanvas.setWidth(canvasWidth);
      fabricCanvas.setHeight(canvasHeight);
      this.scene = img;

      this.scene.filters = [
        new fabric.Image.filters.Brightness({ brightness: _.isNumber(this.props.curDisplayOptionsBrightness) ? (this.props.curDisplayOptionsBrightness - 50) / 50 : 0 }),
        new fabric.Image.filters.Contrast({ contrast: _.isNumber(this.props.curDisplayOptionsContrast) ? (this.props.curDisplayOptionsContrast - 50) / 50 : 0 }),
        new fabric.Image.filters.Saturation({ saturation: _.isNumber(this.props.curDisplayOptionsSaturation) ? (this.props.curDisplayOptionsSaturation - 50) / 50 : 0 }),
      ];

      this.updateSceneSharpness(this.props.isSharpnessEnabled);

      this.scene.applyFilters();

      fabricCanvas.add(img);

      this.resetView();
    });

    if (!_.isEmpty(prevMask)) {
      // uint8 array, one channel alpha value 0-255
      // construct a rbga array with prevMask and the size of the clipPath
      const rgbaArray = [];
      // set color to brush color
      // convert this.props.pencilStrokeColor to rgb
      const penColor = this.props.pencilStrokeColor; // ex. #F2C94C
      const penColorArray = [
        parseInt(penColor.substring(1, 3), 16),
        parseInt(penColor.substring(3, 5), 16),
        parseInt(penColor.substring(5, 7), 16),
      ];
      for (let i = 0; i < prevMask.length; i++) {
        rgbaArray.push(penColorArray[0], penColorArray[1], penColorArray[2], prevMask[i]);
      }

      const width = roi.pMax.x - roi.pMin.x;
      const height = roi.pMax.y - roi.pMin.y;
      const imageData = new ImageData(new Uint8ClampedArray(rgbaArray), width, height);
      const canvas = document.createElement('canvas');
      canvas.width = width;
      canvas.height = height;
      const ctx = canvas.getContext('2d');
      ctx.putImageData(imageData, 0, 0);
      const dataUrl = canvas.toDataURL();
      fabric.Image.fromURL(dataUrl, (img) => {
        img.set({
          selectable: false,
          evented: false,
        });
        // img.imageSmoothing = false;
        img.objectCaching = false;
        // set the position of the mask image to match the clipPath
        img.set('left', 0);
        img.set('top', 0);
        this.fabricCanvas.add(img);
        this.prevMask = img;
      });
    }
  };

  loadScene1 = (dataUrl, canvasWidth, canvasHeight, roi, prevMask) => {
    // console.log('load scene in mask annotation', this.containerRef.current.offsetWidth, this.containerRef.current.offsetHeight);
    if (!this.fabricCanvas || !this.displayCanvasRef.current) return;

    if (!_.get(roi, 'pMin') || !_.get(roi, 'pMax')) return;

    // console.log('load scene in mask annotation', roi);

    // dispose previous scene
    if (this.scene) {
      this.fabricCanvas.remove(this.scene);
      this.scene = null;
    }

    this.clipPath = new fabric.Rect({
      left: roi.pMin.x,
      top: roi.pMin.y,
      width: roi.pMax.x - roi.pMin.x,
      height: roi.pMax.y - roi.pMin.y,
      absolutePositioned: true,
    });

    // this.clipPath = new fabric.Rect({
    //   left: roi.pMin.x * img.scaleX,
    //   top: roi.pMin.y * img.scaleY,
    //   width: (roi.pMax.x - roi.pMin.x) * img.scaleX,
    //   height: (roi.pMax.y - roi.pMin.y) * img.scaleY,
    //   absolutePositioned: true,
    // });

    fabric.Image.fromURL(dataUrl, (img) => {
      img.set({
        selectable: false,
        evented: false,
      })
      // img.imageSmoothing = false;
      img.objectCaching = false;

      this.sceneOriginalWidth = img.width;
      this.sceneOriginalHeight = img.height;
      const { fabricCanvas } = this;

      fabricCanvas.setWidth(canvasWidth);
      fabricCanvas.setHeight(canvasHeight);

      // img.scaleToHeight(canvasHeight);
      // img.scaleToWidth(canvasWidth);

      this.clipPath.set('selectable', false);
      img.clipPath = this.clipPath;
      this.scene = img;

      if (_.isNumber(this.props.curDisplayOptionsBrightness)) this.updateSceneBrightness(this.props.curDisplayOptionsBrightness);
      if (_.isNumber(this.props.curDisplayOptionsContrast)) this.updateSceneContrast(this.props.curDisplayOptionsContrast);
      if (_.isNumber(this.props.curDisplayOptionsSaturation)) this.updateSceneSaturation(this.props.curDisplayOptionsSaturation);

      fabricCanvas.add(img);

      this.resetView();
    });

    if (!_.isEmpty(prevMask)) {
      // uint8 array, one channel alpha value 0-255
      // construct a rbga array with prevMask and the size of the clipPath
      const rgbaArray = [];
      for (let i = 0; i < prevMask.length; i++) {
        rgbaArray.push(0, 0, 0, prevMask[i]);
      }

      const imageData = new ImageData(new Uint8ClampedArray(rgbaArray), this.clipPath.width, this.clipPath.height);
      // const imageData = new ImageData(rgbaArray, this.clipPath.width, this.clipPath.height);
      const canvas = document.createElement('canvas');
      canvas.width = this.clipPath.width;
      canvas.height = this.clipPath.height;
      const ctx = canvas.getContext('2d');
      ctx.putImageData(imageData, 0, 0);
      const dataUrl = canvas.toDataURL();
      fabric.Image.fromURL(dataUrl, (img) => {
        img.set({
          selectable: false,
          evented: false,
        });
        // img.imageSmoothing = false;
        img.objectCaching = false;
        // set the position of the mask image to match the clipPath
        img.set('left', this.clipPath.left);
        img.set('top', this.clipPath.top);
        this.fabricCanvas.add(img);
        this.prevMask = img;
      });
    }
  };

  resetView = () => {
    // console.log('reset view', this.curRoi);
    // reset zoom
    this.fabricCanvas.viewportTransform = [1, 0, 0, 1, 0, 0];

    const canvasWidth = this.fabricCanvas.getWidth();
    const canvasHeight = this.fabricCanvas.getHeight();

    const clipCenter = new fabric.Point(
      0 + (this.curRoi.pMax.x - this.curRoi.pMin.x) / 2,
      0 + (this.curRoi.pMax.y - this.curRoi.pMin.y) / 2
    );

    // zoom to roi
    const zoom = Math.min(
      canvasWidth/ (this.curRoi.pMax.x - this.curRoi.pMin.x),
      canvasHeight / (this.curRoi.pMax.y - this.curRoi.pMin.y),
    );
    this.fabricCanvas.zoomToPoint(clipCenter, zoom);

    // Calculate the new position of the clipCenter after zoom
    const newClipCenter = fabric.util.transformPoint(clipCenter, this.fabricCanvas.viewportTransform);

    // Calculate the pan adjustment to center the cropped area
    const panX = canvasWidth / 2 - newClipCenter.x;
    const panY = canvasHeight / 2 - newClipCenter.y;

    // Apply the pan adjustment
    this.fabricCanvas.viewportTransform[4] += panX;
    this.fabricCanvas.viewportTransform[5] += panY;

    // Re-render the canvas
    this.fabricCanvas.requestRenderAll();
  };

  resetView1 = () => {
    // reset zoom
    this.fabricCanvas.viewportTransform = [1, 0, 0, 1, 0, 0];

    const canvasWidth = this.fabricCanvas.getWidth();
    const canvasHeight = this.fabricCanvas.getHeight();

    const clipCenter = new fabric.Point(
      this.clipPath.left + this.clipPath.width / 2,
      this.clipPath.top + this.clipPath.height / 2
    );

    // zoom to roi
    const zoom = Math.min(
      canvasWidth/ this.clipPath.width,
      canvasHeight / this.clipPath.height,
    );
    this.fabricCanvas.zoomToPoint(clipCenter, zoom);

    // Calculate the new position of the clipCenter after zoom
    const newClipCenter = fabric.util.transformPoint(clipCenter, this.fabricCanvas.viewportTransform);

    // Calculate the pan adjustment to center the cropped area
    const panX = canvasWidth / 2 - newClipCenter.x;
    const panY = canvasHeight / 2 - newClipCenter.y;

    // Apply the pan adjustment
    this.fabricCanvas.viewportTransform[4] += panX;
    this.fabricCanvas.viewportTransform[5] += panY;

    // Re-render the canvas
    this.fabricCanvas.requestRenderAll();
  };

  getSceneSize = () => {
    return {
      width: this.fabricCanvas ? this.fabricCanvas.getWidth() : 0,
      height: this.fabricCanvas ? this.fabricCanvas.getHeight() : 0,
    };
  };

  updateSceneSize = (width, height) => {
    if (this.fabricCanvas) { 
      this.fabricCanvas.setWidth(width);
      this.fabricCanvas.setHeight(height);
    }
  };

  lockScene = () => this.sceneLocked = true;

  unlockScene = () => this.sceneLocked = false;

  clearScene = () => {
    if (this.fabricCanvas) {
      this.fabricCanvas.clear();
    }
    this.scene = null;
    this.curRoi = null;
    this.paths = [];
  };

  render() {
    return (
      <div className='w-full h-full' ref={this.containerRef}>
        {super.render()}
      </div>
    );
  }
};