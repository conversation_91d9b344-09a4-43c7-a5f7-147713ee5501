import { createApi } from '@reduxjs/toolkit/query/react';
import { baseQuery } from './api';
import { serverEndpoint } from '../common/const';


export const productApi = createApi({
  reducerPath: 'productApi',
  baseQuery,
  tagTypes: ['Product'],
  endpoints: (build) => ({
    runDetectionPipeline: build.mutation({
      // query: ({product_id, variant}) => ({
      //   url: '/register',
      //   method: 'POST',
      //   params: { product_id, variant },
      // }),
      async queryFn(arg, queryApi, extraOptions, baseQuery) {
        try {
          // manually encode the variant
          const encodedVar = encodeURIComponent(arg.variant);
          const response = await fetch(`${serverEndpoint}/register?product_id=${arg.product_id}&variant=${encodedVar}`, {
            method: 'POST',
          });

          if (!response.ok) {
            return { error: { status: response.status, message: response.statusText } };
          }

          const data = await response.json();
          return { data };
        } catch (error) {
          return { error: { status: 'FETCH_ERROR', message: error.message } };
        }
      },
    }),
    runInferencePipeline: build.mutation({
      // start inference
      query: (golden_product_id) => ({
        url: '/inference',
        method: 'POST',
        body: { product_id: golden_product_id },
      }),
    }),
    postInferenceAction: build.mutation({
      query: () => ({
        url: '/inferenceTrigger',
        method: 'POST',
        body: {},
      }),
    }),
    stopInference: build.mutation({
      query: () => ({
        url: '/stopInference',
        method: 'POST',
      }),
    }),
    getInferenceStatus: build.query({
      query: () => '/inferenceStatus',
    }),
    getAllProducts: build.query({
      query: () => '/product/allProducts',
      providesTags: ['Product'],
    }),
    getProductById: build.query({
      query: (id) => ({
        url: '/product',
        method: 'GET',
        params: { product_id: id },
      }),
      providesTags: (result, error, id) => [{ type: 'Product', product_id: id }],
    }),
    deleteProduct: build.mutation({
      query: (id) => ({
        url: `/board/${id}`,
        method: 'DELETE',
        param: { product_id: id },
      }),
      invalidatesTags: ['Product'],
    }),
    addProduct: build.mutation({
      // query: (product_name) => ({
      //   url: '/addProduct',
      //   method: 'POST',
      //   // body: {},
      //   params: { product_name },
      //   headers: {
      //     'Content-Type': 'application/x-www-form-urlencoded',
      //   }
      // }),
      async queryFn(arg, queryApi, extraOptions, baseQuery) {
        try {
          // space will be encoded to + for some reason in rtk query
          const encodedProductName = encodeURIComponent(arg);
          const response = await fetch(`${serverEndpoint}/addProduct?product_name=${encodedProductName}`, {
            method: 'POST',
          });

          if (!response.ok) {
            return { error: { status: response.status, message: response.statusText } };
          }

          const data = await response.json();
          return { data };
        } catch (error) {
          return { error: { status: 'FETCH_ERROR', message: error.message } };
        }
      },
      invalidatesTags: (result, error, { product_id }) => [{ type: 'Product', product_id }],
    }),
    removeVariant: build.mutation({
      // query: ({ product_id, variant }) => ({
      //   url: '/deleteVariant',
      //   method: 'POST',
      //   params: { product_id, variant },
      // }),
      async queryFn(arg, queryApi, extraOptions, baseQuery) {
        try {
          // manually encode the variant
          const encodedVar = encodeURIComponent(arg.variant);
          const response = await fetch(`${serverEndpoint}/deleteVariant?product_id=${arg.product_id}&variant=${encodedVar}`, {
            method: 'POST',
          });

          if (!response.ok) {
            return { error: { status: response.status, message: response.statusText } };
          }

          const data = await response.json();
          return { data };
        } catch (error) {
          return { error: { status: 'FETCH_ERROR', message: error.message } };
        }
      },
      invalidatesTags: (result, error, { product_id }) => [{ type: 'Product', product_id }],
    }),
    updateProduct: build.mutation({
      query: (body) => ({
        url: '/product',
        method: 'PUT',
        body,
      }),
      invalidatesTags: (result, error, { product_id }) => [{ type: 'Product', product_id }],
    }),
    getCurProductComponentToggle: build.query({
      query: () => '/componentToggle',
    }),
    updateCurProductComponentToggle: build.mutation({
      query: (body) => ({
        url: '/componentToggle',
        method: 'PUT',
        body,
      }),
    }),
    exportDefinitions: build.query({
      query: (params) => ({
        url: '/exportDefinitions',
        method: 'GET',
        params,
      }),
    }),
    lineItemResults: build.query({
      query: (params) => ({
        url: '/lineItemResults',
        method: 'GET',
        params,
      }),
    }),
    setGolden: build.mutation({
      query: (body) => ({
        url: '/setGolden',
        method: 'PUT',
        body,
      }),
    }),
    reevaluateLineItem: build.mutation({
      query: (body) => ({
        url: '/reevaluateLineItem',
        method: 'POST',
        body,
      }),
    }),
    resetSession: build.mutation({
      query: () => ({
        url: '/resetSession',
        method: 'POST',
      }),
    }),
    detectLine: build.mutation({
      query: (body) => ({
        url: '/detectLine',
        method: 'PUT',
        body,
      }),
    }),
  }),
});

export const {
  useGetAllProductsQuery,
  useLazyGetAllProductsQuery,
  useGetProductByIdQuery,
  useLazyGetProductByIdQuery,
  useDeleteProductMutation,
  useRunInferencePipelineMutation,
  useAddProductMutation,
  useRunDetectionPipelineMutation,
  usePostInferenceActionMutation,
  useStopInferenceMutation,
  useLazyGetInferenceStatusQuery,
  useRemoveVariantMutation,
  useUpdateProductMutation,
  useLazyGetCurProductComponentToggleQuery,
  useUpdateCurProductComponentToggleMutation,
  useLazyExportDefinitionsQuery,
  useLazyLineItemResultsQuery,
  useSetGoldenMutation,
  useReevaluateLineItemMutation,
  useResetSessionMutation,
  useDetectLineMutation,
} = productApi;