import React, { Fragment, useEffect, useState } from 'react';
import { DarkModal, PrimaryButtonConfigProvider } from '../../common/darkModeComponents';
import { handleRequestFailed, translation } from '../../common/util';
import CustomExpand from '../common/CustomExpand';
import { Alert, Button, InputNumber, Select, Slider } from 'antd';
import _ from 'lodash';
import { calibrationBoardDetail } from '../../common/const';
import { useAddCalibrationPatternMutation, useInitializeCalibrationMutation } from '../../services/calibration';
import { ALERT_TYPES, aoiAlert } from '../../common/alert';
import { useLazyGetCalibrationPatternCountQuery } from '../../services/calibration';
import { useRemoveLastCalibrationPatternMutation } from '../../services/calibration';
import { useLazyGetCameraCaptureFrameQuery } from '../../services/camera';
import InfieldCalibrationDisplay from '../Scene/InfieldCalibrationDisplay';
import { useUpdateCalibrationMutation } from '../../services/calibration';
import { useEvaluateCalibrationMutation } from '../../services/calibration';
import { useDispatch } from 'react-redux';
import { setContainerWindowLoadingLocked } from '../../actions/setting';


const CameraInfieldCalibration = (props) => {
  const dispatch = useDispatch();

  const {
    isOpened,
    setIsOpened,
    selectedCameraConfig,
  } = props;
  
  const captureStep = 0;
  const cameraId = 0;

  const [curStep, setCurStep] = useState(0); // 0: calibrate, 1: evaluating, 2: finish
  const [isBoardInfoExpanded, setIsBoardInfoExpanded] = useState(true);
  const [boardType, setBoardType] = useState(null);
  const [boardRows, setBoardRows] = useState(null);
  const [boardColumns, setBoardColumns] = useState(null);
  const [boardSpacing, setBoardSpacing] = useState(null);
  const [isAdjustExposureExpanded, setIsAdjustExposureExpanded] = useState(true);
  const [exposureStop, setExposureStop] = useState(0);
  const [projectorBrightness, setProjectorBrightness] = useState(3);
  const [gain, setGain] = useState(0);
  const [isUseInfieldCalibrationExpanded, setIsUseInfieldCalibrationExpanded] = useState(true);
  const [numberOfCaptures, setNumberOfCaptures] = useState(0);
  const [isAccuracyAnalysisExpanded, setIsAccuracyAnalysisExpanded] = useState(true);
  const [isCaliInitialized, setIsCaliInitialized] = useState(false);
  const [curPoseCount, setCurPoseCount] = useState(0);
  const [curFrame, setCurFrame] = useState(null);
  const [exposureTime, setExposureTime] = useState(50);
  const [calibrationFinalized, setCalibrationFinalized] = useState(false);
  const [maxErrorPercentage, setMaxErrorPercentage] = useState(0);
  const [averageErrorPercentage, setAverageErrorPercentage] = useState(0);
  const [isAccuracyOk, setIsAccuracyOk] = useState(false);

  const [initCali] = useInitializeCalibrationMutation();
  const [capturePose] = useAddCalibrationPatternMutation();
  const [getPoseCount] = useLazyGetCalibrationPatternCountQuery();
  const [removePrevPose] = useRemoveLastCalibrationPatternMutation();
  const [getCameraFrame] = useLazyGetCameraCaptureFrameQuery();
  const [updateCalibration] = useUpdateCalibrationMutation();
  const [evaluateCalibration] = useEvaluateCalibrationMutation();

  const initPoseCount = async (captureStep, cameraId) => {
    // console.log('initPoseCount', captureStep, cameraId);
    const res = await getPoseCount({
      // capture_step: captureStep,
      id: cameraId,
    });

    if (res.error) {
      handleRequestFailed('getCalibrationPatternCount', res.error);
      return;
    }

    setCurPoseCount(_.get(res, 'data.pattern_count', 0));
  };

  useEffect(() => {
    initPoseCount(captureStep, cameraId);
  }, []);

  return  (
    <DarkModal
      width={1280}
      open={isOpened}
      onCancel={() => setIsOpened(false)}
      title={
        <span className='font-source text-[16px] font-semibold'>
          {_.get(selectedCameraConfig, 'is_3d') ? translation('infieldCalibration.recalibrateCamera') : translation('infieldCalibration.recalibrate2dCamera')}
        </span>
      }
      footer={null}
      bodyBottomRounded
      style={{ top: '15px' }}
    >
      <div className='flex items-start self-stretch h-[847px]'>
        {/* form column starts */}
        <div className='flex w-[427px] flex-col items-start self-stretch'>
          { curStep === 0 && (
            <Fragment>
              <div
                className='flex p-4 flex-col items-start gap-8 self-stretch'
              >
                <Button
                  style={{ width: '100%' }}
                  onClick={() => {
                    const handleCapture = async (cameraId) => {
                      const res = await getCameraFrame({
                        camera_id: cameraId
                      });

                      if (res.error) {
                        handleRequestFailed('getCameraCaptureFrame', res.error);
                        return;
                      }

                      setCurFrame(_.get(res, 'data', null));
                    };

                    handleCapture(cameraId);
                  }}
                >
                  <span className='font-source text-[12px] font-normal'>
                    {translation('infieldCalibration.cameraCapture')}
                  </span>
                </Button>
                <CustomExpand
                  isExpanded={isBoardInfoExpanded}
                  onExpand={() => setIsBoardInfoExpanded(!isBoardInfoExpanded)}
                  title={<span className='font-source text-[14px] font-semibold'>
                    {translation('infieldCalibration.setCalibrationBoardInfo')}
                  </span>}
                >
                  <div
                    className='flex flex-col py-2 px-3 items-start gap-1 self-stretch transition-all duration-300 rounded-[2px] border-[1px] border-solid border-[rgba(255,255,255,0.1)]'
                    style={{ background: 'rgba(255, 255, 255, 0.05)' }}
                  >
                    <div className='flex gap-6 items-center self-stretch'>
                      <span className='font-source text-[12px] font-normal flex-1'>
                        {translation('infieldCalibration.boardType')}
                      </span>
                      <div className='flex flex-col gap-1 flex-1'>
                        <Select
                          style={{ width: '173.5px' }}
                          size='small'
                          value={boardType}
                          onChange={(value) => {
                            if (value !== 'customized') {
                              setBoardRows(_.get(calibrationBoardDetail, `${value}.rows`));
                              setBoardColumns(_.get(calibrationBoardDetail, `${value}.columns`));
                              setBoardSpacing(_.get(calibrationBoardDetail, `${value}.squareSize`));
                            }
                            setBoardType(value)
                          }}
                          popupMatchSelectWidth={false}
                          options={[
                            {
                              value: '9x11-24',
                              label: `9 ${translation('infieldCalibration.rows')}; 11 ${translation(
                                'infieldCalibration.columns'
                              )}; 24mm`,
                            },
                            {
                              value: '13x23-40',
                              label: `13 ${translation('infieldCalibration.rows')}; 23 ${translation(
                                'infieldCalibration.columns'
                              )}; 40mm`,
                            },
                            {
                              value: '5x7-36',
                              label: `5 ${translation('infieldCalibration.rows')}; 7 ${translation(
                                'infieldCalibration.columns'
                              )}; 36mm`,
                            },
                            {
                              value: '13x21-30',
                              label: `13 ${translation('infieldCalibration.rows')}; 21 ${translation(
                                'infieldCalibration.columns'
                              )}; 30mm`,
                            },
                            {
                              value: '9x15-24',
                              label: `9 ${translation('infieldCalibration.rows')}; 15 ${translation(
                                'infieldCalibration.columns'
                              )}; 24mm`,
                            },
                            {
                              value: '9x15-5',
                              label: `9 ${translation('infieldCalibration.rows')}; 15 ${translation(
                                'infieldCalibration.columns'
                              )}; 5mm`,
                            },
                            {
                              value: 'customized',
                              label: translation('infieldCalibration.customized'),
                            },
                          ]}
                        />
                      </div>
                    </div>
                    <div className='flex gap-6 items-center self-stretch'>
                      <span className='font-source text-[12px] font-normal flex-1'>
                        {translation('infieldCalibration.rows')}
                      </span>
                      <div className='flex flex-col gap-1 flex-1'>
                        <InputNumber
                          size='small'
                          style={{ width: '100%' }}
                          controls={false}
                          value={boardRows}
                          disabled={boardType !== 'customized'}
                          min={1}
                          max={100}
                          onChange={(value) => setBoardRows(value)}
                        />
                      </div>
                    </div>
                    <div className='flex gap-6 items-center self-stretch'>
                      <span className='font-source text-[12px] font-normal flex-1'>
                        {translation('infieldCalibration.columns')}
                      </span>
                      <div className='flex flex-col gap-1 flex-1'>
                        <InputNumber
                          controls={false}
                          size='small'
                          style={{ width: '100%' }}
                          value={boardColumns}
                          disabled={boardType !== 'customized'}
                          min={1}
                          max={100}
                          onChange={(value) => setBoardColumns(value)}
                        />
                      </div>
                    </div>
                    <div className='flex gap-6 items-center self-stretch'>
                      <span className='font-source text-[12px] font-normal flex-1'>
                        {translation('infieldCalibration.spacing')}
                      </span>
                      <div className='flex flex-col gap-1 flex-1'>
                        <InputNumber
                          controls={false}
                          size='small'
                          style={{ width: '100%' }}
                          value={boardSpacing}
                          disabled={boardType !== 'customized'}
                          min={1}
                          max={100}
                          onChange={(value) => setBoardSpacing(value)}
                        />
                      </div>
                    </div>
                    <div className='flex flex-col items-start gap-1 self-stretch py-2'>
                      <span className='font-source text-[12px] font-semibold'>
                        {translation('infieldCalibration.initializeCalibrationDesc')}
                      </span>
                      <PrimaryButtonConfigProvider>
                        <Button
                          style={{ width: '100%' }}
                          onClick={() => {
                            if (!_.isNumber(boardRows) || !_.isNumber(boardColumns) || !_.isNumber(boardSpacing)) {
                              aoiAlert(translation('notification.error.initializeCalibrationRequiredFields'), ALERT_TYPES.COMMON_ERROR);
                              return;
                            }

                            const cali = async (captureStep, cameraId, boardColumns, boardRows, boardSpacing) => {
                              const res = await initCali({
                                // "capture_step": captureStep,
                                "id": cameraId,
                                "pattern_column": boardColumns,
                                "pattern_row": boardRows,
                                "center_distance": boardSpacing,
                              });

                              if (res.error) {
                                handleRequestFailed('initCalibration', res.error);
                                return;
                              }

                              aoiAlert(translation('notification.success.initCalibration'), ALERT_TYPES.COMMON_SUCCESS);

                              setIsCaliInitialized(true);
                              setCalibrationFinalized(false);
                              initPoseCount(captureStep, cameraId);
                            };

                            cali(captureStep, cameraId, boardColumns, boardRows, boardSpacing);
                          }}
                        >
                          <span className='font-source text-[12px] font-normal'>
                            {translation('infieldCalibration.initializeCalibration')}
                          </span>
                        </Button>
                      </PrimaryButtonConfigProvider>
                    </div>
                  </div>
                </CustomExpand>
                <CustomExpand
                  title={<span className='font-source text-[14px] font-semibold'>
                    {/* {`${translation('infieldCalibration.useInfieldCalibration')} (${translation('infieldCalibration.optional')})`} */}
                    {translation('infieldCalibration.useInfieldCalibration')}
                  </span>}
                  isExpanded={isUseInfieldCalibrationExpanded}
                  onExpand={() => setIsUseInfieldCalibrationExpanded(!isUseInfieldCalibrationExpanded)}
                >
                  <div
                    className='flex flex-col py-2 px-3 items-start gap-4 self-stretch transition-all duration-300 rounded-[2px] border-[1px] border-solid border-[rgba(255,255,255,0.1)]'
                    style={{ background: 'rgba(255, 255, 255, 0.05)' }}
                  >
                    <div className='flex flex-col gap-2 items-start self-stretch'>
                      { _.get(selectedCameraConfig, 'is_3d', false) &&
                        <Fragment>
                          <div className='flex flex-col items-start gap-1 self-stretch'>
                            <div className='flex gap-4 self-stretch item-center'>
                              <div className='flex py-1 items-center self-stretch flex-1'>
                                <span className='font-source text-[12px] font-normal'>
                                  {translation('infieldCalibration.exposureStop')}
                                </span>
                              </div>
                              <Slider
                                min={-1}
                                max={4}
                                value={exposureStop}
                                size='small'
                                style={{ width: '151px' }}
                                onChange={(value) => setExposureStop(value)}
                              />
                              <div className='flex py-1 items-center self-stretch justify-center'>
                                <InputNumber
                                  min={-1}
                                  max={4}
                                  controls={false}
                                  style={{ width: '36px', height: '26px' }}
                                  size='small'
                                  value={exposureStop}
                                  onChange={(value) => setExposureStop(value)}
                                />
                              </div>
                            </div>
                          </div>
                          <div className='flex flex-col items-start gap-1 self-stretch'>
                            <div className='flex gap-4 self-stretch item-center'>
                              <div className='flex py-1 items-center self-stretch flex-1'>
                                <span className='font-source text-[12px] font-normal'>
                                  {translation('infieldCalibration.projectorBrightness')}
                                </span>
                              </div>
                              <Slider
                                min={1}
                                max={3}
                                value={projectorBrightness}
                                size='small'
                                style={{ width: '151px' }}
                                onChange={(value) => setProjectorBrightness(value)}
                              />
                              <div className='flex py-1 items-center self-stretch justify-center'>
                                <InputNumber
                                  min={1}
                                  max={3}
                                  controls={false}
                                  style={{ width: '36px', height: '26px' }}
                                  size='small'
                                  value={projectorBrightness}
                                  onChange={(value) => setProjectorBrightness(value)}
                                />
                              </div>
                            </div>
                          </div>
                        </Fragment>
                      }
                      <div className='flex flex-col items-start gap-1 self-stretch'>
                        <div className='flex gap-4 self-stretch item-center'>
                          <div className='flex py-1 items-center self-stretch flex-1'>
                            <span className='font-source text-[12px] font-normal'>
                              {translation('infieldCalibration.gain')}
                            </span>
                          </div>
                          <Slider
                            min={0}
                            max={3}
                            value={gain}
                            size='small'
                            style={{ width: '151px' }}
                            onChange={(value) => setGain(value)}
                          />
                          <div className='flex py-1 items-center self-stretch justify-center'>
                            <InputNumber
                              min={0}
                              max={3}
                              controls={false}
                              style={{ width: '36px', height: '26px' }}
                              size='small'
                              value={gain}
                              onChange={(value) => setGain(value)}
                            />
                          </div>
                        </div>
                      </div>
                      <div className='flex flex-col items-start gap-1 self-stretch'>
                        <div className='flex gap-4 self-stretch item-center'>
                          <div className='flex py-1 items-center self-stretch flex-1'>
                            <span className='font-source text-[12px] font-normal'>
                              {translation('infieldCalibration.exposureTime')}
                            </span>
                          </div>
                          <Slider
                            min={20}
                            max={450}
                            value={exposureTime}
                            size='small'
                            style={{ width: '121px' }}
                            onChange={(value) => setExposureTime(value)}
                          />
                          <div className='flex py-1 items-center self-stretch justify-center'>
                            <InputNumber
                              min={20}
                              max={450}
                              prefix={translation('infieldCalibration.ms')}
                              controls={false}
                              style={{ width: '66px', height: '26px' }}
                              size='small'
                              value={exposureTime}
                              onChange={(value) => setExposureTime(value)}
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                    { _.get(selectedCameraConfig, 'is_3d', false) &&
                      <span className='font-source text-[12px] font-normal'>
                        {translation('infieldCalibration.captureAtLeastThreePoses')}
                      </span>
                    }
                    <Button
                      onClick={() => {
                        const capture = async (captureStep, cameraId, exposureStop, projectorBrightness, gain, selectedCameraConfig, exposureTime) => {
                          dispatch(setContainerWindowLoadingLocked(true));
                          const payload = _.get(selectedCameraConfig, 'is_3d', false) ? {
                            // "capture_step": captureStep,
                            "id": cameraId,
                            "exposure_stop": exposureStop,
                            "brightness": projectorBrightness,
                            "gain": gain,
                            "exposure_time": exposureTime,
                          } : {
                            // "capture_step": captureStep,
                            "id": cameraId,
                            "exposure_time": exposureTime,
                          };
                          const res = await capturePose(payload);

                          dispatch(setContainerWindowLoadingLocked(false));

                          if (res.error) {
                            handleRequestFailed('capturePose', res.error);
                            return;
                          }

                          aoiAlert(translation('notification.success.poseCaptured'), ALERT_TYPES.COMMON_SUCCESS);

                          initPoseCount(captureStep, cameraId);
                        };

                        capture(captureStep, cameraId, exposureStop, projectorBrightness, gain, selectedCameraConfig, exposureTime);
                      }}
                      style={{ width: '100%' }}
                      disabled={!isCaliInitialized}
                    >
                      <span className='font-source text-[12px] font-normal'>
                        {translation('infieldCalibration.captureCurrentPose')}
                      </span>
                    </Button>
                    <div className='flex items-center gap-4 self-stretch'>
                      <span className='font-source text-[12px] font-normal flex-1'>
                        {`${translation('infieldCalibration.numberOfCaptures')}: ${curPoseCount}`}
                      </span>
                      <Button
                        onClick={() => {
                          const remove = async (captureStep, cameraId) => {
                            const res = await removePrevPose({
                              // "capture_step": captureStep,
                              "id": cameraId,
                            });

                            if (res.error) {
                              handleRequestFailed('removeLastCalibrationPattern', res.error);
                              return;
                            }

                            aoiAlert(translation('notification.success.prevPoseRemoved'), ALERT_TYPES.COMMON_SUCCESS);

                            initPoseCount(captureStep, cameraId);
                          };

                          remove(captureStep, cameraId);
                        }}
                        disabled={!isCaliInitialized || curPoseCount === 0}
                      >
                        <span className='font-source text-[12px] font-normal'>
                          {translation('infieldCalibration.removePrevPose')}
                        </span>
                      </Button>
                    </div>
                  </div>
                </CustomExpand>
              </div>
              <div className='flex items-start p-4 gap-2 slef-stretch w-full'>
                <div className='flex flex-col items-center gap-2 flex-1 self-stretch'>
                  <span className='font-source text-[12px] font-semibold'>
                    {translation('infieldCalibration.thisProcessWillTake')}
                  </span>
                  <div className='flex items-center gap-2 self-stretch'>
                    <PrimaryButtonConfigProvider>
                      <Button
                        style={{ width: '100%' }}
                        onClick={() => {
                          const handleCalibrate = async (captureStep, cameraId) => {
                            const res = await updateCalibration({
                              "id": cameraId,
                              "capture_step": captureStep,
                            });

                            if (res.error) {
                              handleRequestFailed('updateCalibration', res.error);
                              return;
                            }

                            setCalibrationFinalized(true);
                            aoiAlert(translation('notification.success.calibrate'), ALERT_TYPES.COMMON_SUCCESS);
                          };

                          handleCalibrate(captureStep, cameraId);
                        }}
                        disabled={!isCaliInitialized || curPoseCount === 0}
                      >
                        <div className='flex items-center gap-1'>
                          <span className='font-source text-[12px] font-normal'>
                            {translation('infieldCalibration.calibrate')}
                          </span>
                          {/* <img className='w-2.5 h-2.5 shrink-0' src='/img/icn/icn_arraowRight_black.svg' alt='arrow' /> */}
                        </div>
                      </Button>
                    </PrimaryButtonConfigProvider>
                    <PrimaryButtonConfigProvider>
                      <Button
                        style={{ width: '100%' }}
                        onClick={() => {
                          const handleEvulate = async (captureStep, cameraId, exposureStop, projectorBrightness, gain, exposureTime, selectedCameraConfig) => {
                            const payload = _.get(selectedCameraConfig, 'is_3d', false) ? {
                              "id": cameraId,
                              // "capture_step": captureStep,
                              "exposure_stop": exposureStop,
                              "brightness": projectorBrightness,
                              "gain": gain,
                              "exposure_time": exposureTime,
                            } : {
                              "id": cameraId,
                              // "capture_step": captureStep,
                              "exposure_time": exposureTime,
                            };
                            const res = await evaluateCalibration(payload);

                            if (res.error) {
                              handleRequestFailed('evaluateCalibration', res.error);
                              return;
                            }

                            setCurStep(2);
                            setMaxErrorPercentage(_.get(res, 'data.max_error_percentage', 0));
                            setAverageErrorPercentage(_.get(res, 'data.average_error_percentage', 0));
                            setIsAccuracyOk(_.get(res, 'data.ok', false));
                          };

                          handleEvulate(captureStep, cameraId, exposureStop, projectorBrightness, gain, exposureTime, selectedCameraConfig);
                        }}
                      >
                        <div className='flex items-center gap-1'>
                          <span className='font-source text-[12px] font-normal'>
                            {translation('infieldCalibration.evaluate')}
                          </span>
                          <img className='w-2.5 h-2.5 shrink-0' src='/img/icn/icn_arraowRight_black.svg' alt='arrow' />
                        </div>
                      </Button>
                    </PrimaryButtonConfigProvider>
                  </div>
                </div>
              </div>
            </Fragment>
          )}
          { curStep === 2 && (
            <Fragment>
              <div className='flex p-4 flex-col items-start gap-8 self-stretch'>
                <CustomExpand
                  title={<span className='font-source text-[14px] font-semibold'>
                    {translation('infieldCalibration.accuracyAnalysis')}
                  </span>}
                  isExpanded={isAccuracyAnalysisExpanded}
                  onExpand={() => setIsAccuracyAnalysisExpanded(!isAccuracyAnalysisExpanded)}
                >
                  <div className='flex p-3 flex-col items-start gap-4 self-stretch transition-all duration-300 rounded-[2px] border-[1px] border-solid border-[rgba(255,255,255,0.1)]' style={{ background: 'rgba(255, 255, 255, 0.05)' }}>
                    <span className='font-source text-[12px] font-normal'>
                      {translation('infieldCalibration.theCalibrationIsMostly')}
                    </span>
                    <div className='flex flex-col items-start gap-1 self-stretch flex-1'>
                      <div className='flex items-start gap-0.5 self-stretch'>
                        <div className='flex flex-1 py-1 flex-col justify-center items-start flex-1'>
                          <span className='font-source text-[12px] font-normal'>
                            {translation('infieldCalibration.averageTrueness')}
                          </span>
                        </div>
                        <div className='flex flex-col justify-center items-center flex-1 py-1 bg-gray-1 rounded-[2px]'>
                          <span className='font-source text-[12px] font-normal'>
                            {_.round(averageErrorPercentage, 4)}%
                          </span>
                        </div>
                      </div>
                      <div className='flex items-start gap-0.5 self-stretch'>
                        <div className='flex flex-1 py-1 flex-col justify-center items-start'>
                          <span className='font-source text-[12px] font-normal'>
                            {translation('infieldCalibration.maxtrueness')}
                          </span>
                        </div>
                        <div className='flex flex-col justify-center items-center flex-1 py-1 bg-gray-1 rounded-[2px]'>
                          <span className='font-source text-[12px] font-normal'>
                            {_.round(maxErrorPercentage, 4)}%
                          </span>
                        </div>
                      </div>
                    </div>
                    { isAccuracyOk ? (
                      <Alert
                        style={{ width: '100%' }}
                        message={
                          <div className='flex items-start gap-2 self-stretch pr-1'>
                            <div className='flex w-[20px] h-[20px] items-center justify-center'>
                              <img src='/img/icn/icn_checkMark_green.svg' alt='check' className='w-3 h-3 shrink-0' />
                            </div>
                            <span className='font-source text-[12px] font-normal text-AOI-green'>
                              {translation('infieldCalibration.theCalibrationIsAccurate')}
                            </span>
                          </div>
                        }
                        type='success'
                      />
                    ): (
                      <Alert
                        style={{ width: '100%' }}
                        message={
                          <div className='flex items-start gap-2 self-stretch pr-1'>
                            <div className='flex w-[20px] h-[20px] items-center justify-center'>
                              <img src='/img/icn/icn_warning_red.svg' alt='warn' className='w-3 h-3 shrink-0' />
                            </div>
                            <span className='font-source text-[12px] font-normal'>
                              {translation('infieldCalibration.recalibrateToImprove')}
                            </span>
                          </div>
                        }
                        type='error'
                      />
                    )}
                  </div>
                </CustomExpand>
              </div>
              <div className='flex p-4 flex-col items-start gap-2 w-full'>
                <PrimaryButtonConfigProvider>
                  <Button
                    onClick={() => {
                      setCurStep(0);
                    }}
                    style={{ width: '100%' }}
                  >
                    <span className='font-source text-[12px] font-semibold'>
                      {translation('infieldCalibration.recalibrate')}
                    </span>
                  </Button>
                </PrimaryButtonConfigProvider>
                <Button
                  onClick={() => { setIsOpened(false); }}
                  style={{ width: '100%' }}
                >
                  <span className='font-source text-[12px] font-semibold'>
                    {translation('infieldCalibration.finish')}
                  </span>
                </Button>
              </div>
            </Fragment>
          )}
        </div>
        {/* form column ends */}
        {/* scene column starts */}
        <div className='flex flex-col justify-center items-center self-stretch gap-1 flex-1 bg-[#000000]'>
          <InfieldCalibrationDisplay
            curFrame={curFrame}
          />
        </div>
        {/* scene column ends */}
      </div>
    </DarkModal>
  );
};

export default CameraInfieldCalibration;