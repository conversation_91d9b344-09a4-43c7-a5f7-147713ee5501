import { I18nextProvider } from 'react-i18next';
import i18n from './i18n';
import { Provider } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';
import { store, persistor } from './store';
import Router from './router';
import { ConfigProvider, theme } from 'antd';
import { ContextProvider } from './components/Context/Provider';
import enUS from 'antd/locale/en_US';
import zhCN from 'antd/locale/zh_CN';


const Container = () => {
  const { darkAlgorithm } = theme;

  return (
    <I18nextProvider i18n={i18n}>
      <Provider store={store}>
        <PersistGate loading={null} persistor={persistor}>
          <ConfigProvider
            locale={i18n.language === 'en' ? enUS : zhCN}
            theme={{
              algorithm: darkAlgorithm,
              components: {
                Select: {
                  selectorBg: '#333',   
                  optionSelectedColor: '#fff',
                  optionSelectedBg: '#555',
                },
                Switch: {
                  handleBg: '#000',
                  handleShadow: '#828282',
                },
                Button: {
                  primaryColor: '#333',
                  colorText: '#56CCF2',
                },
                Tabs: {
                  cardPadding: '8px 0',
                },
                Segmented: {
                  itemSelectedBg: '#56CCF2',
                  itemSelectedColor: '#000'
                },
                Input: {
                  inputFontSizeSM: 12,
                  paddingBlockSM: 1.6,
                },
              },
              token: {
                colorText: '#fff',
                colorBgContainer: '#333',
                colorBorder: '#555',
                colorPrimary: '#56CCF2',
              }
            }}
          >
            <ContextProvider>
              <Router />
            </ContextProvider>
          </ConfigProvider>
        </PersistGate>
      </Provider>
    </I18nextProvider>
  );
}

export default Container;
