/* eslint-disable no-template-curly-in-string */
// please avoid adding depulicate text
// key with language code ex. en, cn etc. should be only appear at leaf level


export const text = {
  common: {
    exitFullScreenMode: {
      en: 'Exit Full Screen Mode',
      cn: '退出全屏模式',
    },
    fullScreenMode: {
      en: 'Full Screen Mode',
      cn: '全屏模式',
    },
    showFullView: {
      en: 'Show Full View',
      cn: '显示全图',
    },
    enableSharpness: {
      en: 'Enable Sharpness',
      cn: '启用锐化',
    },
    disableSharpness: {
      en: 'Disable Sharpness',
      cn: '禁用锐化',
    },
    curSession: {
      en: 'Current Session',
      cn: '当前任务',
    },
    todayTotalProduct: {
      en: 'Today Total Product',
      cn: '今日总产品数',
    },
    currentUserType: {
      en: 'Current User Type: ',
      cn: '当前用户类型: ',
    },
    reevaluationResult: {
      en: 'Reevaluation Model Output',
      cn: '重新评估模型输出',
    },
    inspectionResult: {
      en: 'Inspection Model Output',
      cn: '检测模型输出',
    },
    modelNotFound: {
      en: 'Model not found',
      cn: '模型未找到',
    },
    pleaseConfirm: {
      en: 'Please confirm to proceed',
      cn: '请确认继续',
    },
    refresh: {
      en: 'Refresh',
      cn: '刷新',
    },
    save: {
      en: 'Save',
      cn: '保存',
    },
    saveAndCaptureGoldenBoard: {
      en: 'Save and Capture Golden Product',
      cn: '保存并拍摄基准产品',
    },
    continue: {
      en: 'Continue',
      cn: '继续',
    },
    stop: {
      en: 'Stop',
      cn: '停止',
    },
    lastModified: {
      en: 'Last Modified',
      cn: '最后修改',
    },
    cancel: {
      en: 'Cancel',
      cn: '取消',
    },
    view: {
      en: 'View',
      cn: '查看',
    },
    no: {
      en: 'No',
      cn: '否',
    },
    remove: {
      en: 'Remove',
      cn: '移除',
    },
    add: {
      en: 'Add',
      cn: '添加',
    },
    delete: {
      en: 'Delete',
      cn: '删除',
    },
    more: {
      en: 'More',
      cn: '更多',
    },
    resetAll: {
      en: 'Reset All',
      cn: '重置所有',
    },
    apply: {
      en: 'Apply',
      cn: '应用',
    },
    settings: {
      en: 'Settings',
      cn: '设置',
    },
    language: {
      en: 'Language',
      cn: '语言',
    },
    english: {
      en: 'English',
      cn: '英语',
    },
    chinese: {
      en: 'Chinese',
      cn: '中文',
    },
    reset: {
      en: 'Reset',
      cn: '重置',
    },
    addAndUpdate: {
      en: 'Add & Update',
      cn: '添加并更新',
    },
    none: {
      en: 'None',
      cn: '无',
    },
    inspectedDate: {
      en: 'Inspected Date',
      cn: '检测日期',
    },
    capture: {
      en: 'Capture',
      cn: '拍摄',
    },
    exportSelected: {
      en: 'Export Selected',
      cn: '导出所选',
    },
    all: {
      en: 'All',
      cn: '全部',
    },
    exportAll: {
      en: 'Export All',
      cn: '导出所有',
    },
    camera: {
      en: 'Camera',
      cn: '相机',
    },
    viewAll: {
      en: 'View All',
      cn: '查看全部',
    },
    display: {
      en: 'Display',
      cn: '显示',
    },
    detectionStep: {
      en: 'Detection Step',
      cn: '检测步骤',
    },
    allDetectionSteps: {
      en: 'All Detection Steps',
      cn: '所有检测步骤',
    },
    pointCloud: {
      en: 'Point Cloud',
      cn: '点云',
    },
    goldenProduct: {
      en: 'Golden Product',
      cn: '基准产品',
    },
    resetView: {
      en: 'Reset View',
      cn: '重置视图',
    },
    clearFilter: {
      en: 'Clear Filter',
      cn: '清除筛选',
    },
    prevStep: {
      en: 'Previous Step',
      cn: '上一步',
    },
    nextStep: {
      en: 'Next Step',
      cn: '下一步',
    },
    all: {
      en: 'All',
      cn: '全部',
    },
    editMask: {
      en: 'Edit Mask',
      cn: '编辑掩模',
    },
    finish: {
      en: 'Finish',
      cn: '完成',
    },
    confirm: {
      en: 'Confirm',
      cn: '确认',
    },
    update: {
      en: 'Update',
      cn: '更新',
    },
    close: {
      en: 'Close',
      cn: '关闭',
    },
    clearAll: {
      en: 'Clear All',
      cn: '清除所有',
    },
    ai_deviation_score: {
      en: 'Deviation Score',
      cn: '异常分数',
    },
    ai_deviation_threshold: {
      en: 'Deviation Threshold',
      cn: '偏差阈值',
    },
    height_difference: {
      en: 'Height Difference',
      cn: '高度差异',
    },
    height_difference_threshold: {
      en: 'Height Difference Threshold',
      cn: '高度差异阈值',
    },
    stretch: {
      cn: '偏离值',
      en: 'Stretch',
    },
    tolerance: {
      en: 'Tolerance',
      cn: '容许偏离值',
    },
  },
  notification: {
    error: {
      archiveAllDataFailed: {
        en: 'Failed to archive all data',
        cn: '归档所有数据失败',
      },
      failToParseThisYamlToObject: {
        en: 'Failed to parse this yaml to object',
        cn: '无法将此yaml解析为对象',
      },
      pleaseEnterAValidValue: {
        en: 'Please enter a valid value',
        cn: '请输入有效值',
      },
      pleaseSelectAProduct: {
        en: 'Please select a product',
        cn: '请选择一个产品',
      },
      failedToSetGoldenProduct: {
        en: 'Failed to set golden product',
        cn: '设置基准产品失败',
      },
      failToParseFeatureParamJson: {
        en: 'Failed to parse feature param json',
        cn: '无法解析特征参数json',
      },
      failToInsertDrawnMaskIntoPayload: {
        en: 'Failed to insert drawn mask into payload',
        cn: '无法将绘制的掩模插入请求',
      },
      selectAVariantionFirst: {
        en: 'Please select a variation first',
        cn: '请先选择一个选配',
      },
      passwordMismatch: {
        en: 'Password mismatch',
        cn: '密码不匹配',
      },
      loginFailed: {
        en: 'Login failed',
        cn: '登录失败',
      },
      requestFailed: {
        en: 'Request failed: ${requestName}',
        cn: '请求失败: ${requestName}',
      },
      selectAGoldenProduct: {
        en: 'Please select a golden product',
        cn: '请选择一个基准产品',
      },
      enterAProductName: {
        en: 'Please enter a product name',
        cn: '请输入产品名称',
      },
      invalidProductName: {
        en: 'Product name can only contain letters, numbers, chinese, brackets, hyphens, spaces, dots',
        cn: '产品名称只能包含字母、数字、中文、括号、横杠, 空格，点',
      },
      inferenceAlreadyRunning: {
        en: 'Inference is already running',
        cn: '检测已在运行',
      },
      inferenceRunningPleaseStopFirst: {
        en: 'Inference is running, please stop first',
        cn: '检测正在运行，请先停止',
      },
      inferenceAlreadyStopped: {
        en: 'Inference is already stopped',
        cn: '检测已停止',
      },
      sessionProductIdNotFound: {
        en: 'Session product id not found',
        cn: '会话产品ID未找到',
      },
      selectedFeatureNotFound: {
        en: 'Selected feature not found',
        cn: '未找到所选特征',
      },
      pleaseSelectAllPointsForCoordinateSystem: {
        en: 'Please select all points for custom coordinate system',
        cn: '请为自定义坐标系选择所有点',
      },
      serverAlreadyHasARunningRetrainTask: {
        en: 'Server already has a running training task, please wait for it to finish',
        cn: '服务器已经有一个正在运行的训练任务，请等待其完成',
      },
      retrainFailed: {
        en: 'Retrain failed',
        cn: '重新训练失败',
      },
      stopInferenceFirst: {
        en: 'Please stop inference first',
        cn: '请先停止检测',
      },
      frameImageUrlEmpty: {
        en: 'Frame image URL is empty',
        cn: '帧图像URL为空',
      },
      enterAVaildVariationName: {
        en: 'Variant name can only contain letters, numbers, chinese, brackets, hyphens, spaces, dots',
        cn: '选配名称只能包含字母、数字、中文、括号、横杠, 空格，点',
      },
      canNotReregisterTheOriginalProduct: {
        en: 'Can not re-register the primordial product when the product has multiple variations',
        cn: '当产品有多个选配时，无法重新注册原始产品',
      },
      canNotDeleteThePrimordialVariant: {
        en: 'Can not delete the primordial variant',
        cn: '无法删除原始选配',
      },
      productHasBeenRegistered: {
        en: 'Product has been registered',
        cn: '产品已注册',
      },
      someTrainingTaskIsRunning: {
        en: 'Some training task is running',
        cn: '一些训练任务正在运行',
      },
      theMinBboxWeAcceptIs: {
        en: 'The minimum bounding box we accept is 5x5 pixels, please use a larger bounding box',
        cn: '我们接受的最小边界框是5x5像素，请使用更大的边界框',
      },
      pleaseLeaveAtLeastOneComponentEnabled: {
        en: 'Please leave at least one component enabled',
        cn: '请至少保留一个组件启用',
      },
      customClassNameCanOnlyContainLettersNumbersAndUnderscores: {
        en: 'Custom class name can only contain letters, numbers, chinese, brackets and hyphens',
        cn: '自定义类名只能包含字母、数字、中文、括号、横杠',
      },
      initializeCalibrationRequiredFields: {
        en: 'Please fill in rows, cols, board spacing or select a board type',
        cn: '请填写行、列、板间距或选择板类型',
      },
      onlyZipFileAcceptedForGoldenProductDefinitionUpload: {
        en: 'Only zip file is accepted for golden product definition upload',
        cn: '只接受zip文件用于基准产品定义上传',
      },
      canNotFindAnyInferenceResultForThisComponent: {
        en: 'Can not find any inference result for this component',
        cn: '找不到此组件的任何检测结果',
      },
      pleaseEnterAValidInteger: {
        en: 'Please enter a valid integer',
        cn: '请输入有效的整数',
      },
      pleaseEnterAValidNumber: {
        en: 'Please enter a valid number',
        cn: '请输入有效的数字',
      },
      pleaseEnterANumberWithinTheRange: {
        en: 'Please enter a number within the range',
        cn: '请输入在范围内的数字',
      },
      failToParseThisJsonToObject: {
        en: 'Failed to parse this json to object',
        cn: '无法将此json解析为对象',
      },
      failToUpdateAllSystemConfigFiles: {
        en: 'Fail to update system config files',
        cn: '更新系统配置文件失败',
      },
      invalidUsernameOrPassword: {
        en: 'Invalid username or password',
        cn: '用户名或密码错误',
      },
    },
    warning: {
      noRunningInference: {
        en: 'No running inference',
        cn: '没有正在运行的检测',
      },
      emptyMask: {
        en: 'Mask not found, please draw a mask',
        cn: '未找到掩模，请绘制掩模',
      },
      aiPredictionHasAGoodResult: {
        en: 'AI prediction result is good',
        cn: 'AI预测结果为良好',
      },
      aiPredictionHasADefectiveResult: {
        en: 'AI prediction result is defective',
        cn: 'AI预测结果为有缺陷',
      },
      correctFeedbackProvided: {
        en: 'Correct feedback has been provided',
        cn: '已提供正确反馈',
      },
      defectFeedbackProvided: {
        en: 'Defect feedback has been provided',
        cn: '已提供缺陷反馈',
      },
      productIdNotFoundInUrl: {
        en: 'Product id not found in URL',
        cn: 'URL中未找到产品ID',
      },
      selectAGoldenProduct: {
        en: 'Please select a golden product',
        cn: '请选择一个基准产品',
      },
      dataUriNotFound: {
        en: 'Data URI not found',
        cn: '未找到数据URI',
      },
      selectAFeatureTypeFirst: {
        en: 'Please select a feature type first',
        cn: '请先选择一个特征类型',
      },
    },
    info: {
      maskReqiredForFeedback: {
        en: 'Please draw a mask to provide feedback',
        cn: '请绘制掩模以提供反馈',
      },
    },
    success: {
      lineDetection: {
        en: 'Line detection has been done',
        cn: '排线检测已完成',
      },
      dataStorageSettingUpdated: {
        en: 'Data storage setting has been updated',
        cn: '数据存储设置已更新',
      },
      datasetReevaluated: {
        en: 'Dataset has been reevaluated',
        cn: '数据集已重新评估',
      },
      cancelAnnotation: {
        en: 'Annotation has been canceled',
        cn: '标注已取消',
      },
      setGoldenProductSuccess: {
        en: 'Set golden product success',
        cn: '设置基准产品成功',
      },
      inferenceFinished: {
        en: 'Inference finished',
        cn: '检测完成',
      },
      replaySession: {
        en: 'Replay inpspection finished',
        cn: '检测回放完成',
      },
      deleteProduct: {
        en: 'Product ${productName} has been deleted',
        cn: '产品 ${productName} 已删除',
      },
      inferenceAnnotation: {
        en: 'Annotation has been saved',
        cn: '标注已保存',
      },
      cameraConfigUpdated: {
        en: 'Camera configuration has been updated',
        cn: '相机配置已更新',
      },
      featureUpdated: {
        en: 'Feature has been updated',
        cn: '特征已更新',
      },
      featureAdded: {
        en: 'Feature has been added',
        cn: '特征已添加',
      },
      featureRemoved: {
        en: 'Feature has been removed',
        cn: '特征已移除',
      },
      retrainFinished: {
        en: 'Retrain finished',
        cn: '重新训练完成',
      },
      submitGoodFeedbackForAllInspectionInThisProduct: {
        en: 'Good feedback has been submitted for all inspections in this product',
        cn: '已为此产品中的所有检测提交了良好反馈',
      },
      initCalibration: {
        en: 'Calibration has been initialized',
        cn: '校准已初始化',
      },
      poseCaptured: {
        en: 'Pose has been captured',
        cn: '姿势已捕获',
      },
      prevPoseRemoved: {
        en: 'Previous pose has been removed',
        cn: '上一个姿势已移除',
      },
      calibrate: {
        en: 'Calibration has been updated',
        cn: '校准已更新',
      },
      goldenProductZipDownloadStarted: {
        en: 'Golden product zip file download started',
        cn: '基准产品zip文件下载已开始',
      },
      componentFilterUpdated: {
        en: 'Component filter has been updated',
        cn: '组件过滤已更新',
      },
      variantDeleted: {
        en: 'Variant has been deleted',
        cn: '选配已删除',
      },
      systemConfigFilesUpdated: {
        en: 'System config files have been updated',
        cn: '系统配置文件已更新',
      },
    }
  },
  login: {
    welcomeToDaoAIAOI: {
      en: 'Welcome to DaoAI AOI',
      cn: '欢迎使用 DaoAI AOI',
    },
    username: {
      en: 'Username',
      cn: '用户名',
    },
    password: {
      en: 'Password',
      cn: '密码',
    },
    signIn: {
      en: 'Sign In',
      cn: '登录',
    },
    login: {
      en: 'Login',
      cn: '登录',
    },
    new: {
      en: 'New?',
      cn: '新用户?',
    },
    createAnAccount: {
      en: 'Create an account',
      cn: '创建账户',
    },
    email: {
      en: 'Email',
      cn: '邮箱',
    },
  },
  signup: {
    createAnAccount: {
      en: 'Create an Account',
      cn: '创建账户',
    },
    alreadyHaveAnAccount: {
      en: 'Already have an account?',
      cn: '已有账户?',
    },
    login: {
      en: 'Login',
      cn: '登录',
    },
    reenterPassword: {
      en: 'Re-enter Password',
      cn: '请重新输入密码',
    },
    signup: {
      en: 'Sign Up',
      cn: '注册',
    },
  },
  mainMenu: {
    stopInference: {
      en: 'Stop Inference',
      cn: '停止检测',
    },
    startInference: {
      en: 'Start Inference',
      cn: '开始检测',
    },
    newInspectTask: {
      en: 'New Inspection Task',
      cn: '新建检测任务',
    },
    home: {
      en: 'Home',
      cn: '首页',
    },
    daoaiAOISystem: {
      en: 'DaoAI Robotics AOI System',
      cn: 'DaoAI Robotics AOI 系统',
    },
    productVersion: {
      en: 'Version: ${version}',
      cn: '版本: ${version}',
    },
    lastInspection: {
      en: 'Last Inspection: ',
      cn: '最近检测: ',
    },
    systemStatus: {
      en: 'System Status: ',
      cn: '系统状态: ',
    },
    inferenceStatus: {
      en: 'Inference Status: ',
      cn: '检测状态: ',
    },
    username: {
      en: 'username',
      cn: '用户名',
    },
    changeLanguage: {
      en: 'Change Language',
      cn: '更改语言',
    },
    inspectionRunning: {
      en: 'Inspection in progress',
      cn: '检测中',
    },
    inspectionIdle: {
      en: 'Idle',
      cn: '空闲',
    },
    updateBackendHost: {
      en: 'Update Backend Host Address',
      cn: '更新后端主机地址',
    },
    trainingTaskIsRunning: {
      en: 'Training task is running',
      cn: '训练任务正在运行',
    },
    currentTrainingTaskStartTime: {
      en: 'Started at: ${time}',
      cn: '开始于: ${time}',
    },
    swapToAdminUser: {
      en: 'Swap to Admin User',
      cn: '切换到管理员用户',
    },
    swapToOperatorUser: {
      en: 'Swap to Operator User',
      cn: '切换到操作员用户',
    },
    admin: {
      en: 'Admin',
      cn: '管理员',
    },
    operator: {
      en: 'Operator',
      cn: '操作员',
    },
  },
  home: {
    welcomeToDaoAIAOI: {
      en: 'Welcome to DaoAI Robotics AOI System',
      cn: '欢迎使用 DaoAI Robotics AOI 系统',
    },
    returnToInspection: {
      en: 'Inspection Task',
      cn: '检测任务',
    },
    newInspectionWork: {
      en: 'New Inspection Work',
      cn: '新建检测任务',
    },
    liveDashboard: {
      en: 'Live Dashboard',
      cn: '实时监控',
    },
    workList: {
      en: 'Work List',
      cn: '任务列表',
    },
    manageSystem: {
      en: 'Manage System',
      cn: '系统管理',
    },
    boards: {
      en: 'Boards',
      cn: 'PCB板',
    },
    products: {
      en: 'Products',
      cn: '产品',
    },
    productName: {
      en: 'Product Name',
      cn: '产品名称',
    },
    defectCount: {
      en: 'Defect Count',
      cn: '缺陷数',
    },
    defectedComponentCount: {
      en: 'Defected Component Count',
      cn: '缺陷组件数',
    },
    defectType: {
      en: 'Defect Type',
      cn: '缺陷类型',
    },
    cameraPreview: {
      en: 'Camera Preview',
      cn: '相机预览',
    },
    systemSettings: {
      en: 'System Settings',
      cn: '系统设置',
    },
    recentInspectionWork: {
      en: 'Recent Inspection Work',
      cn: '最近检测任务',
    },
    serialNumber: {
      en: 'Serial Number',
      cn: '序列号',
    },
    detectionStatus: {
      en: 'Detection Status',
      cn: '检测状态',
    },
    running: {
      en: 'Running',
      cn: '运行中',
    },
    finished: {
      en: 'Finished',
      cn: '完成',
    },
    export: {
      en: 'Export',
      cn: '导出',
    },
    goldenProducts: {
      en: 'Golden Products',
      cn: '基准产品',
    },
    startedAt: {
      en: 'Started At',
      cn: '开始于',
    },
    sessionId: {
      en: 'Inspection Task ID',
      cn: '检测任务ID'
    },
    openInWorklist: {
      en: 'Open Worklist',
      cn: '打开任务列表',
    },
    openInLiveList: {
      en: 'Open Live List',
      cn: '打开实时列表',
    },
    exportByGoldenProduct: {
      en: 'Export by Golden Product',
      cn: '按基准产品导出',
    },
  },
  ngFeedbackConfirmation: {
    pleaseConfirmTheFollowingToProceed: {
      en: `Current feature's inspection record confidence score is lower than the max confidence of all this feature's inspection records that have been provided good feedback,
      please confirm the following to proceed`,
      cn: '当前特征的检测记录置信度分数低于所有当前特征已提供良好反馈的检测记录的最大置信度，请确认以下内容以继续。',
    },
    ngIsReal: {
      en: 'Please confirm the current product is a NG case, ex. NG could caused by camera capture error but product itself is good',
      cn: '请确认当前产品是不合格案例，例如不合格可能是由于相机拍摄错误，但产品本身是好的',
    },
    increaseOfOverKill: {
      cn: '此操作将造成一定程度的过杀，如果这种情况经常发生，请考虑重新训练模型',
      en: 'This operation will cause a certain degree of overkill, if this happens often, please consider retraining the model',
    },
  },
  workList: {
    workList: {
      en: 'Work List',
      cn: '任务列表',
    },
    refreshWorkList: {
      en: 'Refresh Work List',
      cn: '刷新任务列表',
    },
    onlyShowDefects: {
      en: 'Only Show Defects',
      cn: '仅显示缺陷',
    },
    feedbackProvided: {
      en: 'Feedback Provided',
      cn: '已提供反馈', 
    },
    productName: {
      en: 'Product Name',
      cn: '产品名称',
    },
    serialNumber: {
      en: 'Serial Number',
      cn: '序列号',
    },
    date: {
      en: 'Date',
      cn: '日期',
    },
    defectCount: {
      en: 'Defect Count',
      cn: '缺陷数',
    },
    defectType: {
      en: 'Defect Type',
      cn: '缺陷类型',
    },
    detectionStatus: {
      en: 'Detection Status',
      cn: '检测状态',
    },
    feedback: {
      en: 'Feedback',
      cn: '反馈',
    },
  },
  manageBoards: {
    manageBoards: {
      en: 'Manage Product',
      cn: '管理产品',
    },
    boardsCount: {
      en: 'PRODUCTS',
      cn: '产品数量',
    },
    createBoard: {
      en: 'Create Product',
      cn: '新建产品',
    },
    productName: {
      en: 'Product Name',
      cn: '产品名称',
    },
    description: {
      en: 'Description',
      cn: '描述',
    },
    productModel: {
      en: 'Product Model',
      cn: '产品型号',
    },
    goldenProduct: {
      en: 'Golden Product:',
      cn: '基准产品:',
    },
    aiModel: {
      en: 'AI Model',
      cn: 'AI模型',
    },
    imagingMap: {
      en: 'Imaging Map',
      cn: '成像图',
    },
    actions: {
      en: 'Actions',
      cn: '操作',
    },
    exportGoldenProduct: {
      en: 'Export Golden Product',
      cn: '导出基准产品',
    },
    importGoldenProduct: {
      en: 'Import Golden Product',
      cn: '导入基准产品',
    },
    viewTrainingSet: {
      en: 'View Training Set',
      cn: '查看训练集',
    },
  },
  newBoard: {
    newBoard: {
      en: 'New Product',
      cn: '新建产品',
    },
    pleaseSpecifyTheFollowing: {
      en: 'Please specify the following information',
      cn: '请填写以下信息',
    },
    boardName: {
      en: 'Product Name',
      cn: '板名称',
    },
    boardLength: {
      en: 'Product Length (inches)',
      cn: '板长',
    },
    boardWidth: {
      en: 'Product Width (inches)',
      cn: '板宽',
    },
    boardModel: {
      en: 'Product Model',
      cn: '板型号',
    },
    description: {
      en: 'Description',
      cn: '描述',
    },
    aiModel: {
      en: 'AI Model',
      cn: 'AI模型',
    },

  },
  imageCaptureRequired: {
    userActionRequired: {
      en: 'User Action Required: Image Capture',
      cn: '用户操作要求: 图像捕获',
    },
    positionTheNextPart: {
      en: 'Position the next part and select “Continue” to begin image capture. ',
      cn: '放置下一个零件并选择“继续”开始图像捕获。',
    },
    inferencePositionTheNextPart: {
      en: 'Position the product and select “Continue” to do single inspection(or wait for the incoming inspection done), or "Stop" inspection for defects review. ',
      cn: '放置产品并选择“继续”进行单次检测(或等待检测完成)，或“停止”检测以进行缺陷审查。',
    },
  },
  viewBoards: {
    board: {
      en: 'Product:',
      cn: '产品:',
    },
    model: {
      en: 'Model:',
      cn: '型号:',
    },
    sn: {
      en: 'S/N:',
      cn: '序列号:',
    },
    series: {
      en: 'Series',
      cn: '系列',
    },
    images: {
      en: 'Images',
      cn: '图像',
    },
    detectionInProgress: {
      en: 'Detection in Progress',
      cn: '检测中',
    },
    detectionFinished: {
      en: 'Detection Finished',
      cn: '检测完成',
    },
    registerAgain: {
      en: 'Register Again',
      cn: '重新注册',
    },
    fullProduct: {
      en: '${productName} - Full Product',
      cn: '${productName} - 完整产品',
    },
    showFullProduct: {
      en: 'Show Full Product',
      cn: '显示完整产品',
    },
    updateCameraSettings: {
      en: 'Update Camera Settings',
      cn: '更新相机设置',
    },
    selectProductVariant: {
      en: 'Select Product Variant',
      cn: '选择产品选配',
    }
  },
  newProduct: {
    registerNewProduct: {
      en: 'Register New Product',
      cn: '注册新产品',
    },
    setupCamera: {
      en: 'Setup Camera',
      cn: '设置相机',
    },
    captureAndDefineGoldenProduct: {
      en: 'Capture and Define Golden Product',
      cn: '拍摄并定义基准产品',
    },
    nextStep: {
      en: 'Next Step',
      cn: '下一步',
    },
    proceedWithoutCapture: {
      en: 'Proceed without capture',
      cn: '不进行拍摄',
    },
    capture: {
      en: 'Capture',
      cn: '拍摄',
    },
  },
  cameraPreview: {
    turnOnContinuousCapture: {
      en: 'Turn on continuous capture',
      cn: '打开连续拍摄',
    },
    turnOffContinuousCapture: {
      en: 'Turn off continuous capture',
      cn: '关闭连续拍摄',
    },
    ledBrightness: {
      en: 'LED Brightness',
      cn: 'LED亮度',
    },
    channel1: {
      en: 'Channel 1',
      cn: '通道1',
    },
    channel2: {
      en: 'Channel 2',
      cn: '通道2',
    },
    channel3: {
      en: 'Channel 3',
      cn: '通道3',
    },
    channel4: {
      en: 'Channel 4',
      cn: '通道4',
    },
    recalibrate3dCamera: {
      en: 'Re-calibrate 3D camera',
      cn: '重新校准3D相机',
    },
    recalibrate2dCamera: {
      en: 'Re-calibrate 2D camera',
      cn: '重新校准2D相机',
    },
    cameraSettings: {
      en: 'Camera Settings',
      cn: '相机设置',
    },
    recaptureImage: {
      en: 'Re-capture Image',
      cn: '重新拍摄',
    },
    cameraPreview: {
      en: 'Camera Preview',
      cn: '相机预览',
    },
    showPointCloud: {
      en: 'Show Point Cloud',
      cn: '显示点云',
    },
    cameraSetup: {
      en: 'Camera Setup',
      cn: '相机设置',
    },
    positionTheNextPart: {
      cn: '放置下物体并开始图像捕获。',
      en: 'Position the object and start image capture.',
    },
    frameSettings: {
      en: 'Frame Settings',
      cn: '帧设置',
    },
    pointCloudFilter: {
      en: 'Point Cloud Filter',
      cn: '点云过滤器',
    },
    cameraConfigEmpty: {
      en: 'Camera configuration is empty',
      cn: '相机配置为空',
    },
    newFrame: {
      en: 'New Frame',
      cn: '新帧',
    },
    capture_step: {
      en: 'Capture Step',
      cn: '拍摄步骤',
    },
    frame: {
      en: 'Frame',
      cn: '帧',
    },
    exposureStop: {
      en: 'Exposure Stop',
      cn: '曝光档位',
    },
    brightness: {
      en: 'Brightness',
      cn: '亮度',
    },
    gain: {
      en: 'Gain',
      cn: '增益',
    },
    outlier: {
      en: 'Outlier',
      cn: '异常值',
    },
    smoothing: {
      en: 'Smoothing',
      cn: '平滑',
    },
    contrast: {
      en: 'Contrast',
      cn: '对比度',
    },
    correction: {
      en: 'Correction',
      cn: '校正',
    },
    outlierStrength: {
      en: 'Outlier Strength',
      cn: '异常值强度',
    },
    off: {
      en: 'Off',
      cn: '关闭',
    },
    weak: {
      en: 'Weak',
      cn: '弱',
    },
    normal: {
      en: 'Normal',
      cn: '正常',
    },
    strong: {
      en: 'Strong',
      cn: '强',
    },
    customized: {
      en: 'Customized',
      cn: '自定义',
    },
    outlierRemoval: {
      en: 'Outlier Removal (Advanced)',
      cn: '异常值去除 (高级)',
    },
    outlierThresholdLevel: {
      en: 'Outlier Threshold Level',
      cn: '异常值阈值级别',
    },
    faceNormalFilter: {
      en: 'Face Normal Filter',
      cn: '法向量过滤器',
    },
    clusterFilter: {
      en: 'Cluster Filter',
      cn: '移除小型离散区域',
    },
    strength: {
      en: 'Strength',
      cn: '强度',
    },
    neighborDistance: {
      en: 'Neighbor Distance',
      cn: '邻居距离',
    },
    smoothingStrength: {
      en: 'Smoothing Strength',
      cn: '平滑强度',
    },
    smoothingSettings: {
      en: 'Smoothing Settings: (advanced)',
      cn: '平滑设置: (高级)',
    },
    gaussianFilter: {
      en: 'Gaussian Filter',
      cn: '高斯滤波器',
    },
    medianFilter: {
      en: 'Median Filter',
      cn: '中值滤波器',
    },
    smoothFilter: {
      en: 'Smooth Filter',
      cn: '平滑滤波器',
    },
    contrastSettings: {
      en: 'Contrast Settings: (Advanced)',
      cn: '对比度设置: (高级)',
    },
    intensityThreshold: {
      en: 'Intensity Threshold',
      cn: '强度阈值',
    },
    removeOverexposeRegion: {
      en: 'Remove Overexpose Region',
      cn: '移除过曝光区域',
    },
    removeLowQualityRegion: {
      en: 'Remove Low Quality Region',
      cn: '移除低质量区域',
    },
    fillGaps: {
      en: 'Fill Gaps',
      cn: '填补缺口',
    },
    holeSize: {
      en: 'Hole Size',
      cn: '孔尺寸',
    },
    depthDifference: {
      en: 'Depth Difference',
      cn: '深度差异',
    },
    contrastDistortion: {
      en: 'Contrast Distortion',
      cn: '对比度失真',
    },
    correctLevel: {
      en: 'Correct Level',
      cn: '校正级别',
    },
    removeLevel: {
      en: 'Remove Level',
      cn: '移除级别',
    },
    correct: {
      en: 'Correct',
      cn: '校正',
    },
    remove: {
      en: 'Remove',
      cn: '移除',
    },
    exposureTime: {
      en: 'Exposure Time (ms)',
      cn: '曝光时间(ms)',
    },
    twoDCaptureThreeDCam: {
      en: '2D Capture(3D camera)',
      cn: '2D 捕获(3D相机)',
    },
    twoDCapture: {
      en: '2D Capture',
      cn: '2D 捕获',
    },
    exposureMode: {
      en: 'Exposure Mode',
      cn: '曝光模式',
    },
    timed: {
      en: 'Timed',
      cn: '定时',
    },
    flash: {
      en: 'Flash',
      cn: '闪光',
    },
    triggerMode: {
      en: 'Trigger Mode',
      cn: '触发模式',
    },
    software: {
      en: 'Software',
      cn: '软件',
    },
    hardware: {
      en: 'Hardware',
      cn: '硬件',
    },
    colorBalance: {
      en: 'Color Balance/Ratio',
      cn: '色彩平衡/比例',
    },
    red: {
      en: 'Red',
      cn: '红',
    },
    green: {
      en: 'Green',
      cn: '绿',
    },
    blue: {
      en: 'Blue',
      cn: '蓝',
    },
    coordinateSystem: {
      en: 'Coordinate system',
      cn: '坐标系',
    },
    defaultCoordinateSystem: {
      en: 'Default Coordinate System',
      cn: '默认坐标系',
    },
    customCoordinateSystem: {
      en: 'Custom Coordinate System',
      cn: '自定义坐标系',
    },
    customCoordinateSystemLower: {
      en: 'Custom coordinate system',
      cn: '自定义坐标系',
    },
    toDefineACoordSystem: {
      en: 'To define a coordinate system, pick three points on point cloud display.',
      cn: '要定义坐标系，请在点云显示上选择三个点。',
    },
    firstPoint: {
      en: '· 1st point: the origin of the new coordinate system',
      cn: '· 第一个点: 新坐标系的原点',
    },
    secondPoint: {
      en: '· 2nd point: define x-axis (with 1st point)',
      cn: '· 第二个点: 定义x轴(与第一个点)',
    },
    thirdPoint: {
      en: '· 3rd point: determine XOY plane (with X-axis/2nd point)',
      cn: '· 第三个点: 确定XOY平面(与X轴/第二个点)',
    },
    pointOne: {
      en: 'Point 1',
      cn: '点1',
    },
    pointTwo: {
      en: 'Point 2',
      cn: '点2',
    },
    pointThree: {
      en: 'Point 3',
      cn: '点3',
    },
    setPointOne: {
      en: 'Set Point 1',
      cn: '设置点1',
    },
    setPointTwo: {
      en: 'Set Point 2',
      cn: '设置点2',
    },
    setPointThree: {
      en: 'Set Point 3',
      cn: '设置点3',
    },
    proceedToDefineProduct: {
      en: 'Proceed to define product',
      cn: '继续定义产品',
    },
    productRegister: {
      en: 'Product Register',
      cn: '产品注册',
    },
    registerDesc: {
      en: 'Register is mandatory for the first time to define the product.',
      cn: '首次定义产品时，注册是必需的。',
    },
    fullCameraViews: {
      en: 'All Camera(s) overview',
      cn: '所有相机概览',
    },
  },
  productAnnotation: {
    paixianLineDetection: {
      en: 'Paixian Line Detection',
      cn: '排线检测',
    },
    translateToolDesc: {
      en: 'Pan/Zoom Tool',
      cn: '平移/缩放工具',
    },
    pleaseRetrainTheModelAfterEditingIgnoreMask: {
      en: 'Please retrain the model after editing ignore mask to take effect',
      cn: '编辑忽略掩模后请重新训练模型以生效',
    },
    removeComponent: {
      en: 'Remove Component',
      cn: '删除组件',
    },
    newVariationName: {
      en: 'New Variation Name',
      cn: '新选配名称',
    },
    newVariation: {
      en: 'New Variation',
      cn: '新选配',
    },
    newVariationDescription: {
      en: '*Add variation for selected part of the product',
      cn: '*为产品的选定部分添加选配',
    },
    selectNewVariationInferenceStep: {
      en: 'Select the infernce step for the new product variation',
      cn: '选择新产品选配的检测步骤',
    },
    newProductVariation: {
      en: 'New Product Variation',
      cn: '新建产品选配',
    },
    positionTheNextVariation: {
      en: 'Position the next variation and select “Continue” to begin image capture. ',
      cn: '放置下一个选配并选择“继续”开始图像捕获。',
    },
    hideComponent: {
      en: 'Hide Component(${componentCount})',
      cn: '隐藏组件(${componentCount})',
    },
    showComponent: {
      en: 'Show Component(${componentCount})',
      cn: '显示组件(${componentCount})',
    },
    showPointcloud: {
      en: 'Show Pointcloud',
      cn: '显示点云',
    },
    showImage: {
      en: 'Show Image',
      cn: '显示2D图像',
    },
    selectToolDesc: {
      en: 'Selection Tool: Click components to zoom and inspect.',
      cn: '选择工具: 单击组件以放大和检查。',
    },
    drawBboxToolDesc: {
      en: 'Draw Bounding Box Tool: Draw a bounding box around the component.',
      cn: '绘制边界框工具: 在组件周围绘制边界框。',
    },
    eraseToolDesc: {
      en: 'Erase Tool: Erase the bounding box of the component.',
      cn: '擦除工具: 擦除组件的边界框。',
    },
    defectList: {
      en: 'Defect List',
      cn: '缺陷列表',
    },
    componentList: {
      en: 'Component List',
      cn: '组件列表',
    },
    imageListItemTitle: {
      en: 'Inference step ${index}',
      cn: '检测步骤 ${index}',
    },
    annotate: {
      en: 'Annotate',
      cn: '标注',
    },
    noComponentSelected: {
      en: 'No component selected',
      cn: '未选择组件',
    },
    selectedComponent: {
      en: 'Selected Component',
      cn: '已选择组件',
    },
    type: {
      en: 'Type: ',
      cn: '类型: ',
    },
    yesRemoveComponent: {
      en: 'Yes, remove component',
      cn: '是的，删除组件',
    },
    thisActionWillRemoveOneComponent: {
      en: 'This action will remove 1 component. Are you sure you want to continue?',
      cn: '此操作将删除1个组件。您确定要继续吗？',
    },
    addComponent: {
      en: 'Add Component',
      cn: '添加组件',
    },
    AISensitivity: {
      en: 'Defect Model Sensitivity(1-10):',
      cn: '缺陷模型敏感度(1-10):',
    },
    heightDifference: {
      en: 'Height Model standard deviation(1-10):',
      cn: '高度模型标准差(1-10):',
    },
    sensitivityDesc: {
      cn: '10代表对缺陷敏感，容易误报，但不放过任何缺陷。1代表对缺陷不敏感。',
      en: '10 means sensitive to defects, easy to false alarm, but not miss any defects. 1 means insensitive to defects.',
    },
    heightDiffDesc: {
      en: '1 standard deviation means not sentitive to height difference. 10 standard deviation means sensitive to height difference.',
      cn: '1标准差表示对高度差异不敏感。10标准差表示对高度差异敏感。',
    },
    reregisterDesc: {
      en: `NOTE: Reregistering the product will remove all the selected variant's classes and its existing feedbacks can not be used for new trainings.`,
      cn: `注意: 重新注册产品将删除所有已选择选配的类别，其现有反馈不能用于新的训练。`,
    },
    reregisterTitle: {
      en: 'Reregister Product',
      cn: '重新注册产品',
    },
    ignoreMask: {
      en: 'Ignore Mask:',
      cn: '忽略掩模:',
    },
    ignoreMaskDesc: {
      en: 'Drawn area will be ignored in inference. Some areas are variable in different variant products component ex. different language text, etc. can be ignored in inference.',
      cn: '绘制的区域将在检测中被忽略。一些区域在不同的选配产品组件中是可变的，例如，不同的语言文本等可以在检测中被忽略。',
    },
    enableIgnoreMaskForThisComponent: {
      en: 'Ignore mask enabled',
      cn: '忽略掩模已启用',
    },
    currentIgnoreMaskPreview: {
      en: 'Current Ignore Mask Preview:',
      cn: '当前忽略掩模预览:',
    },
    agentName: {
      defect_detection: {
        en: 'Defect Detection',
        cn: '缺陷检测',
      },
      height_diff: {
        en: 'Height Difference',
        cn: '高度差异',
      },
      objdet: {
        en: 'SanReGao Object Detection',
        cn: '散热膏对象检测',
      },
      line_detection: {
        en: 'Paixian Line Detection',
        cn: '排线检测',
      }
    },
    paramName: {
      // first level key is agent name
      // second level key is param name
      line_detection: {
        theta: {
          title: {
            en: 'Line Angle(°):',
            cn: '排线角度(°)',
          },
        },
        threshold: {
          title: {
            en: 'Line Angle Threshold(°):',
            cn: '排线角度阈值(°):',
          },
        },
        edge_direction: {
          title: {
            en: 'Edge Direction:',
            cn: '边缘方向:',
          },
          desc: {
            en: `Scanning the each column of Roi in 'search direction'​ to find the edge point of the sharpest gradient in 'edge direction'`,
            cn: `在ROI的每个列中按照“搜索方向”扫描，在“边缘方向”中找到最陡峭的梯度的边缘点`,
          },
          options: {
            DARK_TO_BRIGHT: {
              en: 'Dark to Bright',
              cn: '暗到亮',
            },
            BRIGHT_TO_DARK: {
              en: 'Bright to Dark',
              cn: '亮到暗',
            },
            BOTH: {
              en: 'Both',
              cn: '两者',
            },
          },
        },
        search_direction: {
          title: {
            en: 'Search Direction:',
            cn: '搜索方向:',
          },
          options: {
            FORWARD: {
              en: 'Forward',
              cn: '向前',
            },
            BACKWARD: {
              en: 'Backward',
              cn: '向后',
            },
          }
        },
      },
      defect_detection: {
        sensitivity: {
          title: {
            en: 'Defect Model Sensitivity(1-10):',
            cn: '缺陷模型敏感度(1-10):',
          },
          desc: {
            en: '10 means sensitive to defects, easy to false alarm, but not miss any defects. 1 means insensitive to defects.',
            cn: '10代表对缺陷敏感，容易误报，但不放过任何缺陷。1代表对缺陷不敏感。',
          },
        },
        enable_box_fitting: {
          title: {
            en: 'Enable Box Fitting',
            cn: '启用框拟合',
          },
          desc: {
            en: 'Enable box fitting for defect detection',
            cn: '启用框拟合进行缺陷检测',
          },
        },
        ai_deviation_threshold: {
          title: {
            en: 'AI Deviation Threshold',
            cn: 'AI偏差阈值',
          },
          desc: {
            en: 'AI deviation threshold for defect detection',
            cn: '缺陷检测的AI偏差阈值',
          },
        },
        use_user_threshold: {
          title: {
            cn: '使用自定义阈值',
            en: 'Use customized threshold',
          },
        },
        user_defined_threshold: {
          title: {
            en: 'Customized Threshold',
            cn: '自定义阈值',
          },
        },
      },
      height_diff: {
        height_difference_threshold: {
          title: {
            en: 'Height Difference Threshold:',
            cn: '高度差阈值:',
          },
          desc: {
            en: 'Height difference threshold for height difference detection',
            cn: '高度差检测的高度差阈值',
          }
        },
        ext_left: {
          title: {
            en: 'Left extension of the ROI',
            cn: 'ROI的左侧扩展',
          },
          desc: {
            en: 'Number of pixels to include additionally on the left side of the ROI',
            cn: '在ROI左侧额外包含的像素数',
          }
        },
        ext_right: {
          title: {
            en: 'Right extension of the ROI',
            cn: 'ROI的右侧扩展',
          },
          desc: {
            en: 'Number of pixels to include additionally on the right side of the ROI',
            cn: '在ROI右侧额外包含的像素数',
          }
        },
        ext_top: {
          title: {
            en: 'Top extension of the ROI',
            cn: 'ROI的顶部扩展',
          },
          desc: {
            en: 'Number of pixels to include additionally on the top side of the ROI',
            cn: '在ROI顶部额外包含的像素数',
          }
        },
        ext_bottom: {
          title: {
            en: 'Bottom extension of the ROI',
            cn: 'ROI的底部扩展',
          },
          desc: {
            en: 'Number of pixels to include additionally on the bottom side of the ROI',
            cn: '在ROI底部额外包含的像素数',
          }
        },
      },
    },
    retrainModel: {
      en: 'Retrain Model',
      cn: '重新训练模型',
    },
  },
  newInspection: {
    newInspection: {
      en: 'New Inspection',
      cn: '新建检测',
    },
    startInspection: {
      en: 'Start Inspection',
      cn: '开始检测',
    },
    inspectionSetup: {
      en: 'Inspection Setup',
      cn: '检测设置',
    },
    chooseAProduct: {
      en: 'Choose a Golden Product:',
      cn: '选择一个基准产品:',
    },
    selectedProductInfo: {
      en: 'Selected Product Info',
      cn: '已选择产品信息',
    },
    description: {
      en: 'Description',
      cn: '描述',
    },
    productModel: {
      en: 'Product Model',
      cn: '产品型号',
    },
    aiModel: {
      en: 'AI Model',
      cn: 'AI模型',
    },
    startInferenceDesc: {
      en: 'NOTE: Inference task can not be started if any training task is running, please wait for the training task to finish.',
      cn: '注意: 如果有任何训练任务正在运行，则无法启动检测任务，请等待训练任务完成。',
    },
  },
  liveDashboard: {
    liveDashboard: {
      en: 'Live Dashboard',
      cn: '实时监控',
    },
    currentProduct: {
      en: 'Current Product:',
      cn: '当前产品:',
    },
    totalProducts: {
    en: 'Total Products:',
      cn: '总产品数:',
    },
    goodInferenceCount: {
      en: 'Good:',
      cn: '合格:',
    },
    defectiveInferenceCount: {
      en: 'Defective:',
      cn: '不合格:',
    },
    viewCurrentInspection: {
      en: 'View Current Inspection',
      cn: '查看当前检测',
    },
    viewLatestInspection: {
      en: 'View Latest Inspection',
      cn: '查看最新检测',
    },
    stopInspection: {
      en: 'Stop Inspection',
      cn: '停止检测',
    },
    startInspection: {
      en: 'Start Inspection',
      cn: '开始检测',
    },
    noInspectionIsRunning: {
      en: 'No inspection is running',
      cn: '没有正在运行的检测',
    },
    defect: {
      en: 'Defect',
      cn: '缺陷',
    },
    more: {
      en: '+${count} more',
      cn: '更多 +${count}',
    },
    productModel: {
      en: 'Product Model',
      cn: '产品型号',
    },
    productName: {
      en: 'Product Name',
      cn: '产品名称',
    },
    date: {
      en: 'Date',
      cn: '日期',
    },
    aiModel: {
      en: 'AI Model',
      cn: 'AI模型',
    },
    defects: {
      en: 'Defects',
      cn: '缺陷',
    },
    defectFeedback: {
      en: 'Defect Feedback',
      cn: '缺陷反馈',
    },
    na: {
      en: 'n/a',
      cn: '无',
    },
    missing: {
      en: 'Missing ${count}',
      cn: '缺失 ${count}',
    },
    wrongComponent: {
      en: 'Wrong Component',
      cn: '错误组件',
    },
    viewLatestInspection: {
      en: 'View Latest Inspection',
      cn: '查看最新检测',
    },
    loadingDefectsThumbnail: {
      en: 'Loading defects thumbnail...',
      cn: '正在加载缺陷缩略图...',
    },
    componentName: {
      en: 'Component Name:',
      cn: '组件名称:',
    },
    failedInspectionItems: {
      en: 'Failed Inspection Items:',
      cn: '检测失败项目:',
    },
  },
  viewInspection: {
    currentProductName: {
      en: 'Golden Product',
      cn: '基准产品',
    },
    pickRetrainGoldenProduct: {
      en: `Select a golden product's model to retrain`,
      cn: '选择一个基准产品的模型进行重新训练',
    },
    curInspectionGoldenProduct: {
      en: `Current Inspection's Golden Product:`,
      cn: '本次检测基准产品:',
    },
    selectAGoldenProduct: {
      en: 'Select a Golden Product:',
      cn: '选择一个基准产品:',
    },
    switchGoldenProduct: {
      en: 'Switch Inspection Golden Product',
      cn: '切换检测基准产品',
    },
    showComponentFilter: {
      en: 'Show Component Filter',
      cn: '显示组件筛选',
    },
    hideComponentFilter: {
      en: 'Hide Component Filter',
      cn: '隐藏组件筛选',
    },
    searchComponent: {
      en: 'Search Component',
      cn: '搜索组件',
    },
    filterComponents: {
      en: 'Filter Components',
      cn: '筛选组件',
    },
    showChecklist: {
      en: 'Show Golden Components Checklist',
      cn: '显示基准组件清单',
    },
    checklist: {
      en: 'Golden Components Checklist',
      cn: '基准组件清单',
    },
    totalComponent: {
      en: 'Total Components:',
      cn: '总组件数:',
    },
    highlyDefective: {
      en: 'Highly Defective',
      cn: '高度缺陷',
    },
    notDefective: {
      en: 'Not Defective',
      cn: '非缺陷',
    },
    displayDefectHeatmap: {
      en: 'Display defect heatmap/analysis (detected by AI Model) ',
      cn: '显示缺陷热图/分析(由AI模型检测)',
    },
    perspective: {
      en: 'Perspective view',
      cn: '透视图',
    },
    top: {
      en: 'Top view',
      cn: '顶视图',
    },
    left: {
      en: 'Left view',
      cn: '左视图',
    },
    right: {
      en: 'Right view',
      cn: '右视图',
    },
    front: {
      en: 'Front view',
      cn: '前视图',
    },
    back: {
      en: 'Back view',
      cn: '后视图',
    },
    feedback: {
      en: 'Feedback',
      cn: '反馈',
    },
    single: {
      en: 'Single',
      cn: '单次',
    },
    inspectionSummary: {
      en: 'Inspection Summary',
      cn: '检测总结',
    },
    inferenceStep: {
      en: 'Inference Step',
      cn: '检测步骤',
    },
    currentProduct: {
      en: 'Current session pass rate:',
      cn: '本次检测合格率:',
    },
    currentProdInspResult: {
      en: 'Current Product Inspection Result:',
      cn: '当前产品检测结果:',
    },
    pass: {
      en: 'PASS',
      cn: '通过',
    },
    defective: {
      en: 'DEFECTIVE',
      cn: '有缺陷',
    },
    currentProductTotal: {
      cn: '本次检测总数:',
      en: 'Current session inspections total:',
    },
    totalProducts: {
      en: 'Total Products:',
      cn: '总产品数:',
    },
    totalProductPassRate: {
      cn: '总产品合格率:',
      en: 'Total Product Pass Rate:',
    },
    OK: {
      en: 'OK:',
      cn: '良好:',
    },
    NG: {
      en: 'NG:',
      cn: '不合格:',
    },
    adjustPassingRate: {
      en: 'Adjust Passing Rate',
      cn: '调整合格率',
    },
    sharpnessDesc: {
      en: 'Sharpness enhancement is useful when details are not clear enough ex. when drawing defective mask., but it is not yet supported for reference comparison.',
      cn: '锐化增强在图片细节不够清晰时有用，例如在绘制有缺陷的掩模时，但目前参考对比不支持锐化增强。',
    },
    sharpness: {
      en: 'Sharpness enhancement',
      cn: '锐化增强',
    },
    inspectionTrigger: {
      en: 'Single Inspection',
      cn: '单次检测',
    },
    replaySession: {
      en: 'Replay Session',
      cn: '回放会话',
    },
    submitGoodFeedbackForAllInspectionItems: {
      en: 'Mark all feedback as good',
      cn: '标记所有反馈为合格',
    },
    submitGoodFeedbackForAll: {
      en: 'Submit good feedback for All Inspection Items in this Product',
      cn: '提交合格反馈给此产品中的所有检测项目',
    },
    next: {
      en: 'Next',
      cn: '下一个',
    },
    previous: {
      en: 'Previous',
      cn: '上一个',
    },
    saturation: {
      en: 'Saturation',
      cn: '饱和度',
    },
    contrast: {
      en: 'Contrast',
      cn: '对比度',
    },
    brightness: {
      en: 'Brightness',
      cn: '亮度',
    },
    displayOptions: {
      en: 'Display Options',
      cn: '显示选项',
    },
    currentFeedback: {
      en: 'Current Feedback: ',
      cn: '当前反馈: ',
    },
    startInspection: {
      en: 'Start Inspection',
      cn: '开始检测',
    },
    liveInspectionView: {
      en: 'Live Inspection View',
      cn: '实时检测视图',
    },
    inspectionView: {
      en: 'Inspection View',
      cn: '检测视图',
    },
    inspectionInprogress: {
      en: 'Inspection in progress',
      cn: '检测中',
    },
    stopInspection: {
      en: 'Stop Inspection',
      cn: '停止检测',
    },
    defectsReviewed: {
      en: '${viewedDefectsCount}/${totalDefects} Defects Reviewed',
      cn: '${viewedDefectsCount}/${totalDefects} 缺陷已审核',
    },
    totalConfirmedDefects: {
      en: 'Total Confirmed Defects: ${totalConfirmedDefects}',
      cn: '总确认缺陷数: ${totalConfirmedDefects}',
    },
    defectList: {
      en: 'Defect List',
      cn: '缺陷列表',
    },
    predictionList: {
      en: 'Prediction List',
      cn: '预测列表',
    },
    defectsDetected: {
      en: '${totalDefectsCount} Defects Detected',
      cn: '检测到的缺陷',
    },
    image: {
      en: 'Image',
      cn: '图像',
    },
    backToLiveDashboard: {
      en: 'Back to Live Dashboard',
      cn: '返回实时监控',
    },
    emptyDefectDetail: {
      en: 'No defect detail',
      cn: '无缺陷详情',
    },
    AIPrediction: {
      en: 'AI Prediction',
      cn: 'AI预测',
    },
    latestReevaluatedPrediction: {
      en: 'Latest Re-evaluated Prediction',
      cn: '最新重新评估的预测',
    },
    byModelUsedDuringInspection: {
      en: 'By model used during inspection',
      cn: '由检测期间使用的模型',
    },
    inspectedAt: {
      en: 'Inspected at',
      cn: '检测于',
    },
    reevaluatedAt: {
      en: 'Re-evaluated at ${displayTime}',
      cn: '重新评估于 ${displayTime}',
    },
    userFeedback: {
      en: 'User Feedback',
      cn: '用户反馈',
    },
    pickOne: {
      en: 'Pick One:',
      cn: '选择一个:',
    },
    noDefect: {
      en: 'No Defect',
      cn: '无缺陷',
    },
    referenceComparison: {
      en: 'Reference Comparison',
      cn: '参考对比',
    },
    sample: {
      en: 'Sample',
      cn: '样本',
    },
    golden: {
      en: 'Golden',
      cn: '基准',
    },
    viewGoldenProduct: {
      en: 'View Golden Product',
      cn: '查看基准产品',
    },
    defected: {
      en: 'Defected',
      cn: '有缺陷',
    },
    correctOne: {
      en: 'Correct One',
      cn: '正确',
    },
    pass: {
      en: 'Pass',
      cn: '通过',
    },
    redefineDetect: {
      en: 'Re-define Detect',
      cn: '优化检测',
    },
    draw: {
      en: 'Draw',
      cn: '绘制',
    },
    submitAsFeedback: {
      en: 'Submit Mask and Feedback',
      cn: '提交掩模和反馈',
    },
    stroke: {
      en: 'Stroke',
      cn: '笔画',
    },
    pencilTool: {
      en: 'Pencil Tool',
      cn: '铅笔工具',
    },
    pencilToolDesc: {
      en: 'Click and drag on the display to draw.',
      cn: '在显示器上单击并拖动以绘制。',
    },
    eraserTool: {
      en: 'Eraser Tool',
      cn: '橡皮擦工具',
    },
    eraserToolDesc: {
      en: 'Click and drag on the display to erase.',
      cn: '在显示器上单击并拖动以擦除。',
    },
    panTool: {
      en: 'Pan Tool',
      cn: '平移工具',
    },
    panToolDesc: {
      en: 'Click and drag on the display to pan.',
      cn: '在显示器上单击并拖动以平移。',
    },
    selected: {
      en: 'Selected',
      cn: '已选择',
    },
    cancelFeedback: {
      en: 'Cancel Feedback',
      cn: '取消反馈',
    },
    editPrevMask: {
      en: 'Edit Previous Mask',
      cn: '编辑上一个掩模',
    },
    edit: {
      en: 'Edit',
      cn: '编辑',
    },
    stayAtLatest: {
      en: 'Stay at Latest',
      cn: '保持在最新',
    },
    alwaysDisplayLatestScan: {
      en: 'Always display latest scan',
      cn: '始终显示最新扫描',
    },
    taskOverview: {
      en: 'Task Overview',
      cn: '任务概览',
    },
    todayInspectionOverview: {
      en: 'Today Inspection Overview',
      cn: '今日检测概览',
    },
    overallPassRate: {
      en: 'Task overall Pass Rate:',
      cn: '任务整体合格率:',
    },
    todayPassRate: {
      en: 'Today Pass Rate:',
      cn: '今日合格率:',
    },
    totalItems: {
      en: 'Total Items:',
      cn: '总产品数:',
    },
    todayTotalItems: {
      en: 'Today Total Items:',
      cn: '今日总产品数:',
    },
    components: {
      en: 'Components',
      cn: '组件',
    },
    currentSessionOkNg: {
      en: 'Current Session OK/NG:',
      cn: '本次检测良好/不良:',
    },
    todayOkNg: {
      en: 'Today OK/NG Count:',
      cn: '今日良好/不良数:',
    },
    badFeedback: {
      en: 'Not Good',
      cn: '不良',
    },
    annotateFeedbackOptions: {
      notProvided: {
        en: 'Not Provided',
        cn: '未提供',
      },
      objdet: {
        good: {
          en: 'Object Detected',
          cn: '检测到对象',
        },
        notGood: {
          en: 'Object Not Detected',
          cn: '未检测到对象',
        },
      },
      anomaly_detection: {
        good: {
          en: 'Good',
          cn: '良好',
        },
        notGood: {
          en: 'Not Good',
          cn: '不良',
        },
      },
      height_diff: {
        good: {
          en: 'Good',
          cn: '良好',
        },
        notGood: {
          en: 'Not Good',
          cn: '不良',
        },
      },
      defect_detection: {
        good: {
          en: 'Good',
          cn: '良好',
        },
        notGood: {
          en: 'Not Good',
          cn: '不良',
        },
      },
    },
    retrainModel: {
      en: 'Re-train Model',
      cn: '重新训练模型',
    },
    yesStartTraining: {
      en: 'Stop current session and start training',
      cn: '停止当前任务并开始训练',
    },
    startTrainingAfterSession: {
      en: 'Start training after this session ends',
      cn: '在此任务结束后开始训练',
    },
    readyToRetrainTheModel: {
      en: 'Are you sure you want to retrain the model?',
      cn: '您确定要重新训练模型吗？',
    },
    retrainModelModalDesc: {
      en: `We’ll use the feedback you’ve provided and new captured data to improve the detection models. This process depends on the amount of data and
       will take approximately 1 minute, 
      during this period the software will still be able to run inspection. Note: You need to restart the inspection after training ends to make the new model effective.`,
      cn: `我们将使用您提供的反馈和新捕获的数据来改进检测模型。此过程取决于数据量，大约需要1分钟，期间软件仍然可以运行检测。
      注意：您需要在训练结束后重新启动检测以使新模型生效。`,
    },
    lineItem: {
      anomaly_detection: {
        en: 'Defect Detection',
        cn: '异常检测',
      },
      height_diff: {
        en: 'Height Difference',
        cn: '高度差异',
      },
      defect_detection: {
        en: 'Defect Detection',
        cn: '缺陷检测',
      },
      objdet: {
        en: 'Object Detection',
        cn: '对象检测',
      },
    },
    lineItemGoodResult: {
      en: 'Good',
      cn: '良好',
    },
    lineItemBadResult: {
      en: 'Not good',
      cn: '不良',
    },
    featureTypes: {
      screw: {
        en: 'Screw',
        cn: '螺丝',
      },
      other: {
        en: 'Other',
        cn: '其他',
      },
      another_type: {
        en: 'Another Type',
        cn: '其他类型',
      },
      cable: {
        en: 'Cable',
        cn: '线缆',
      },
      sanregao: {
        en: 'Thermal Paste',
        cn: '散热膏',
      },
      gangqingai: {
        en: 'Piano Cover Cable',
        cn: '钢琴盖排线',
      },
      jiaoquan: {
        en: 'Rubber Ring',
        cn: '胶圈',
      },
      duanzipai: {
        en: 'Terminal',
        cn: '端子排',
      },
      neicuntiao: {
        en: 'Memory',
        cn: '内存',
      },
      'wangwangka': {
        en: 'Wangwang Card',
        cn: '旺旺卡',
      },
      'SSD': {
        en: 'SSD',
        cn: 'SSD',
      },
      'biaoshi': {
        en: 'Square Mark',
        cn: '方形标识',
      },
      'MALA': {
        en: 'MALA',
        cn: 'MALA',
      },
      'fengshan': {
        en: 'Fan',
        cn: '风扇',
      },
      'dianchi': {
        en: 'Battery Label',
        cn: '电池标签',
      },
      'tianxian': {
        en: 'Antenna',
        cn: '天线',
      },
      'jinshukou': {
        en: 'Metal Buckle',
        cn: '金属扣',
      },
      'paixian_KB': {
        en: 'Keyboard Cable',
        cn: '键盘排线',
      },
      'paixian_TP': {
        en: 'Touchpad Cable',
        cn: '触控板排线',
      },
      'paixian_BL': {
        en: 'Backlight Cable',
        cn: '背光排线',
      },
      'paixian_NFC': {
        en: 'NFC Cable',
        cn: 'NFC排线',
      },
      'paixian_RTC': {
        en: 'RTC Cable',
        cn: 'RTC排线',
      },
      'paixian_FPR': {
        en: 'Fingerprint Cable',
        cn: '指纹排线',
      },
      'duanzipai_battery': {
        en: 'Battery Terminal',
        cn: '电池端子',
      },
      'duanzipai_brk': {
        en: 'Brake Terminal',
        cn: '刹车端子',
      },
      'duanzipai_fan': {
        en: 'Fan Terminal',
        cn: '风扇端子',
      },
      'duanzipai_rtc': {
        en: 'RTC Terminal',
        cn: 'RTC端子',
      },
      'fan': {
        en: 'Fan',
        cn: '风扇',
      },
      'EDP': {
        en: 'EDP',
        cn: 'EDP',
      },
      'CAM': {
        en: 'Camera',
        cn: '摄像头',
      },
      'DDR': {
        en: 'DDR',
        cn: 'DDR',
      },
      'product': {
        en: 'Product',
        cn: '产品',
      },
    },
    prediction: {
      en: 'Prediction',
      cn: '预测',
    },
    review: {
      en: 'Review',
      cn: '审核',
    },
    comparison2d: {
      en: '2D',
      cn: '2D',
    },
    heightDifferenceComparison: {
      en: 'Height Difference Comparison',
      cn: '高度差异对比',
    },
    heightDifference: {
      en: 'Height Difference',
      cn: '高度差异',
    },
    overlay: {
      en: 'Overlay:',
      cn: '叠加:',
    },
    showProduct: {
      en: 'Show Product',
      cn: '显示产品',
    },
    showGoldenProduct: {
      en: 'Show Golden Product',
      cn: '显示基准产品',
    },
    liveFilterComponent: {
      en: 'Filter Component',
      cn: '过滤组件',
    },
    selectDisabledComponentForCurrentProduct: {
      en: 'Select enabled component for current product:',
      cn: '选择当前产品的启用组件:',
    },
    systemWillSkipDisabledComponent: {
      en: 'The system will skip the disabled(unchecked) component during the inspection.',
      cn: '系统将在检测过程中跳过禁用(未勾选)的组件。',
    },
    atLeastOneComponentHas: {
      en: 'At least one component has to be selected.',
      cn: '至少选择一个组件。',
    },
    inspectAllComponents: {
      en: 'Inspect All ${count} Components',
      cn: '检查所有${count}组件',
    },
    defectArea: {
      en: 'Show Defect Area',
      cn: '显示缺陷区域',
    },
    defectAreaDesc: {
      en: 'Red means the defect model outputs a high score for this pixel, yellow means a low score, transparent means the model thinks this pixel is good.',
      cn: '红色表示缺陷模型对该像素输出高分(大概率为缺陷)，黄色表示低分(低概率为缺陷)，透明表示模型认为该像素良好。',
    },
    separateView: {
      en: 'Separate View',
      cn: '分开查看',
    },
    stackedView: {
      en: 'Stacked View',
      cn: '堆叠查看',
    },
    zPlaneView: {
      en: 'Z-Plane View',
      cn: 'Z-平面查看',
    },
    front: {
      en: 'Front',
      cn: '正面',
    },
    back: {
      en: 'Back',
      cn: '背面',
    },
    left: {
      en: 'Left',
      cn: '左侧',
    },
    right: {
      en: 'Right',
      cn: '右侧',
    },
    top: {
      en: 'Top',
      cn: '顶部',
    },
    bottom: {
      en: 'Bottom',
      cn: '底部',
    },
  },
  markAllFeedbackAsGood: {
    markAllFeedbackAsGood: {
      en: 'Mark All Feedback as Good',
      cn: '将所有反馈标记为良好',
    },
    pleaseSelectAVariantForThisProduct: {
      en: `Please select this product's variant`,
      cn: '请选择此产品的选配'
    },
    componentListOf: {
      en: 'Component list of selected variation:',
      cn: '已选选配的组件列表:',
    },
    viewAll: {
      en: 'View All',
      cn: '查看全部',
    },
    showLess: {
      en: 'Show Less',
      cn: '显示更少',
    },
    imageQuickView: {
      en: 'Image quick view',
      cn: '图像快速查看',
    },
  },
  adjustPassingRate: {
    useRealDateForDisplay: {
      cn: '在任务概览中使用真实数据展示',
      en: 'Use real date for display in task overview',
    },
    adjustIpcCountTitle: {
      cn: '以下数据为当前数据，您可以对当日检测总数，合格数，进行调整。所有调整及计数器重置将在第二日设置时间重置。',
      en: 'The following data is current data, you can adjust the total inspection count, good count. All adjustments and counter resets will be reset at the set time the next day.',
    },
    adjustPassingRate: {
      en: 'Adjust Passing Rate',
      cn: '调整合格率',
    },
    newPassingRate: {
      en: 'Daily Passing Rate Alarm Trigger Threshold:',
      cn: '每日合格率报警触发阈值:',
    },
    ngProductTolarance: {
      en: 'Daily Defective Product Amount Tolerance:',
      cn: '每日不良产品数量容差:',
    },
    continuousDefectThreshold: {
      en: 'Continuous Defect Count Alarm Trigger Threshold:',
      cn: '连续不良产品数报警触发阈值:',
    },
    adjustTotalIpcCount: {
      en: 'Adjust Total Inspection Count',
      cn: '调整总检测次数',
    },
    adjustTotalGoodCount: {
      en: 'Adjust Total Good Count',
      cn: '调整总良好次数',
    },
    adjustTotalNgCount: {
      en: 'Adjust Total Defective Count',
      cn: '调整总不良次数',
    },
    adjustCounterResetTime: {
      en: 'Adjust Inspection Counter Reset Hour(0-23): ',
      cn: '调整检测计数器重置点数(0-23): ',
    },
    pickTime: {
      en: 'Pick Time',
      cn: '选择时间',
    },
  },
  deleteProduct: {
    title: {
      en: 'Delete Product',
      cn: '删除产品',
    },
    deleteProduct: {
      en: 'Do you want to delete this product',
      cn: '您确定要删除此产品吗',
    },
  },
  infieldCalibration: {
    theCalibrationIsAccurate: {
      en: 'The calibration is accurate, camera is ready!',
      cn: '校准准确，相机已准备就绪！',
    },
    recalibrateToImprove: {
      en: 'Re-calibrate to improve accuracy',
      cn: '重新校准以提高准确性',
    },
    recalibrateCamera: {
      en: 'Re-calibrate 3D camera',
      cn: '重新校准3D相机',
    },
    recalibrate2dCamera: {
      en: 'Re-calibrate 2D camera',
      cn: '重新校准2D相机',
    },
    setCalibrationBoardInfo: {
      en: 'Set Calibration Board Info',
      cn: '设置校准板信息',
    },
    customized: {
      en: 'Customized',
      cn: '自定义',
    },
    boardType: {
      en: 'Board Type',
      cn: '板类型',
    },
    rows: {
      en: 'Row(s)',
      cn: '行',
    },
    columns: {
      en: 'Column(s)',
      cn: '列',
    },
    spacing: {
      en: 'Spacing (mm)',
      cn: '间距(mm)',
    },
    adjustExposure: {
      en: 'Adjust Exposure',
      cn: '调整曝光',
    },
    exposureStop: {
      en: 'Exposure Stop',
      cn: '曝光档位',
    },
    projectorBrightness: {
      en: 'Projector Brightness',
      cn: '投影仪亮度',
    },
    gain: {
      en: 'Gain',
      cn: '增益',
    },
    cameraCapture: {
      en: 'Camera capture',
      cn: '相机捕获',
    },
    useInfieldCalibration: {
      en: 'Use In-Field Calibration',
      cn: '使用现场校准',
    },
    optional: {
      en: 'Optional',
      cn: '可选',
    },
    captureAtLeastThreePoses: {
      en: 'Capture at least 3 new poses to evaluate calibration accuracy.',
      cn: '至少捕获3个新姿势以评估校准精度。',
    },
    captureCurrentPose: {
      en: 'Capture Current Pose',
      cn: '捕获当前姿势',
    },
    numberOfCaptures: {
      en: 'Number of Captures',
      cn: '捕获数量',
    },
    clearAllPoses: {
      en: 'Clear All Poses',
      cn: '清除所有姿势',
    },
    thisProcessWillTake: {
      en: '*This process will take 10-20 seconds',
      cn: '*此过程将花费10-20秒',
    },
    calibrate: {
      en: 'Calibrate',
      cn: '校准',
    },
    accuracyAnalysis: {
      en: 'Accuracy Analysis',
      cn: '精度分析',
    },
    theCalibrationIsMostly: {
      en: 'The calibration is mostly accurate if result is <0.2%.',
      cn: '校准在1mm内基本准确。',
    },
    averageTrueness: {
      en: 'Average Trueness',
      cn: '平均真实度',
    },
    maxtrueness: {
      en: 'Max Trueness',
      cn: '最大真实度',
    },
    recalibrateToImprove: {
      en: 'Recalibrate to improve accuracy',
      cn: '重新校准以提高准确性',
    },
    recalibrate: {
      en: 'Re-calibrate',
      cn: '重新校准',
    },
    finish: {
      en: 'Finish',
      cn: '完成',
    },
    initializeCalibration: {
      en: 'Initialize Calibration',
      cn: '初始化校准',
    },
    removePrevPose: {
      en: 'Remove Previous Pose',
      cn: '删除上一个姿势',
    },
    exposureTime: {
      en: 'Exposure Time',
      cn: '曝光时间',
    },
    ms: {
      en: 'ms',
      cn: '毫秒',
    },
    evaluate: {
      en: 'Evaluate',
      cn: '评估',
    },
    initializeCalibrationDesc: {
      en: 'Initialize calibration will remove all existed poses',
      cn: '初始化校准将删除所有现有姿势',
    },
  },
  dataExport: {
    dataExport: {
      en: 'Data Export',
      cn: '数据导出',
    },
    scope: {
      en: 'Scope',
      cn: '范围',
    },
    goldenProduct: {
      en: 'Golden Product',
      cn: '基准产品',
    },
    selectAGoldenProduct: {
      en: 'Select a Golden Product',
      cn: '选择一个基准产品',
    },
    zipExport: {
      en: 'Zip Export',
      cn: 'Zip导出',
    },
    zipExportDesc: {
      en: `Export selected golden product's captured data, features, inference results, etc. as a zip file.`,
      cn: '将所选基准产品的捕获数据、特征、推断结果等导出为zip文件。',
    },
    downloadZip: {
      en: 'Download Zip',
      cn: '下载Zip',
    },
  },
  includeDefinedComponentsModal: {
    includeDefined: {
      en: 'Include Defined Components',
      cn: '包含已定义的组件',
    },
    theNewVariationWillInclude: {
      en: 'The new variation will include all defined components from the original image, and they will be locked. To make changes, please edit the original image.',
      cn: '新选配将包含原始图像中的所有已定义组件，并且它们将被锁定。要进行更改，请编辑原始图像。',
    },
    noNeedToRemind: {
      en: 'No need to remind me about locked components next time',
      cn: '下次无需提醒我关于锁定的组件',
    },
    gotIt: {
      en: 'Got it',
      cn: '知道了',
    },
  },
  exportGoldenProductDefinition: {
    title: {
      en: 'Export Golden Product',
      cn: '导出基准产品',
    },
    selectGoldenProduct: {
      en: 'Select Golden Product(s) to export',
      cn: '选择要导出的基准产品',
    },
    searchGoldenProduct: {
      en: 'Search Golden Product',
      cn: '搜索基准产品',
    },
  },
  deleteVariantComfirmation: {
    deleteVariant: {
      en: 'Delete Variant',
      cn: '删除选配',
    },
    thisActionWillDelete: {
      en: 'This action will delete the selected variant ${varinat} and all its associated classes. Are you sure you want to proceed?',
      cn: '此操作将删除选配 ${varinat} 及其所有关联的类。您确定要继续吗？',
    },
  },
  worklist: {
    refreshList: {
      en: 'Refresh List',
      cn: '刷新列表',
    },
    worklist: {
      en: 'Worklist',
      cn: '工作列表',
    },
    keepListUpdated: {
      en: 'Keep the list updated',
      cn: '保持列表更新',
    },
    filterByGoldenProduct: {
      en: 'Filter by Golden Product',
      cn: '按基准产品筛选',
    },
    worklistExport: {
      en: 'Worklist Export',
      cn: '工作列表导出',
    },
    useWorklistFilters: {
      en: 'Tip: Use worklist filters to define your export criteria.',
      cn: '提示: 使用工作列表过滤器定义您的导出条件。',
    },
    scope: {
      en: 'Scope',
      cn: '范围',
    },
    currentPage: {
      en: 'Current Page',
      cn: '当前页',
    },
    allPages: {
      en: 'All Pages',
      cn: '所有页',
    },
    export: {
      en: 'Export',
      cn: '导出',
    },
    productName: {
      en: 'Product Name',
      cn: '产品名称',
    },
    startDate: {
      en: 'Start Date',
      cn: '开始日期',
    },
    totalProducts: {
      en: 'Total Products',
      cn: '总产品数',
    },
    productok: {
      cn: '合格产品总数:',
      en: 'OK Products Total:',
    },
    productng: {
      cn: '不合格产品总数:',
      en: 'NG Products Total:',
    },
    passRate: {
      en: 'Pass Rate',
      cn: '合格率',
    },
    actions: {
      en: 'Actions',
      cn: '操作',
    },
    open: {
      en: 'Open',
      cn: '打开',
    },
    openComponentOkNgList: {
      en: 'Open Component OK/NG List',
      cn: '打开组件合格/不合格列表',
    },
    sessionGoldenProd: {
      en: 'Session Golden Product:',
      cn: '检测包含基准产品:',
    },
    totalProducts: {
      en: 'Total Products:',
      cn: '总产品数:',
    },
    good: {
      en: 'Good:',
      cn: '良好:',
    },
    defective: {
      en: 'Defective:',
      cn: '有缺陷:',
    },
    feedbackProvided: {
      en: 'Feedback provided',
      cn: '已提供反馈',
    },
    onlyDisplayDefectiveItems: {
      en: 'Only display defective items',
      cn: '仅显示有缺陷的项目',
    },
    date: {
      en: 'Date',
      cn: '日期',
    },
    totalComponent: {
      en: 'Total Component',
      cn: '总组件',
    },
    okngCounts: {
      en: 'OK/NG Counts',
      cn: '合格/不合格计数',
    },
    passFail: {
      en: 'Pass/Fail',
      cn: '合格/不合格',
    },
    pass: {
      en: 'PASS',
      cn: '合格',
    },
    fail: {
      en: 'FAIL',
      cn: '不合格',
    },
    feedback: {
      en: 'Feedback',
      cn: '反馈',
    },
    provided: {
      en: 'Provided',
      cn: '已提供',
    },
    notProvided: {
      en: 'Not Provided',
      cn: '未提供',
    },
    review: {
      en: 'Review',
      cn: '审核',
    },
    componentList: {
      en: 'Component List',
      cn: '组件列表',
    },
    inspection: {
      en: 'Inspection',
      cn: '检测',
    },
    filterByGoldenProduct: {
      en: 'Search by Golden Product',
      cn: '按基准产品搜索',
    },
    filterBySerialNumber: {
      en: 'Search by Serial Number',
      cn: '按序列号搜索',
    },
    goldenProductName: {
      en: 'Golden Product Name',
      cn: '基准产品名称',
    },
    serialNumber: {
      en: 'Serial Number',
      cn: '序列号',
    },
    goodProducts: {
      en: 'Good Products:',
      cn: '良好产品:',
    },
    defectiveProducts: {
      en: 'Defective Products:',
      cn: '有缺陷产品:',
    },
    goldenProducts: {
      en: 'Golden Products',
      cn: '基准产品',
    },
    clearFilter: {
      en: 'Clear Filter',
      cn: '清除筛选',
    },
    export: {
      en: 'Export',
      cn: '导出',
    },
    switchToAllInspection: {
      en: 'Switch to All Inspection',
      cn: '切换到所有检测',
    },
    switchToViewSingleSessionInspection: {
      en: 'Switch to View Single Task Inspection',
      cn: '切换到查看单个任务检测',
    },
  },
  componentReview: {
    filterByFeedbackTooltip: {
      en: `NOTE: This applies to inspection items(height difference, defect detection, etc.) wise, if you have provided feedback for a record\'s height difference, 
      but not for defect detection, the record will only show height difference tab. This effect will be applied to the re-evaulated result color status in the list as well.`,
      cn: '注意: 这适用于检测项目(高度差异，缺陷检测等)，如果您为记录的高度差异提供了反馈，但未为缺陷检测提供反馈，该记录将仅显示高度差异选项卡。此效果也将应用于列表中重新评估的结果颜色状态。',
    },
    reevaluateWithNewModelForThisRecord: {
      en: 'Re-evaluate with new model for this record',
      cn: '使用新模型重新评估此记录',
    },
    reevaluateWithNewModel: {
      en: 'Re-evaluate with new model for ${recordCount} records',
      cn: '使用新模型为${recordCount}条记录重新评估',
    },
    filterByFeatureType: {
      en: 'Filter by Feature Type',
      cn: '按特征类型筛选',
    },
    filterByInferenceResult: {
      en: 'Filter by Inference Result',
      cn: '按推断结果筛选',
    },
    filterByFeedback: {
      en: 'Filter by Feedback',
      cn: '按反馈筛选',
    },
    retrainModelNow: {
      en: 'Retrain Model',
      cn: '重新训练模型',
    },
    componentReview: {
      en: 'Component Review',
      cn: '组件审核',
    },
    inspectionTask: {
      en: 'Inspection Task:',
      cn: '检测任务:',
    },
    foundResults: {
      en: 'Found Results:',
      cn: '发现结果:',
    },
    sortByIpcTime: {
      en: 'Sort by inspection time',
      cn: '按检测时间排序',
    },
    good: {
      en: 'Good',
      cn: '良好',
    },
    notGood: {
      en: 'Not Good',
      cn: '不良',
    },
    notProvided: {
      en: 'Not Provided',
      cn: '未提供',
    },
  },
  updateBackendHost: {
    title: {
      en: 'Update Backend Host Address',
      cn: '更新后端主机地址',
    },
    currentHostDidNotResponsePleaseUpdate: {
      en: 'Current host did not respond, please update the host address.',
      cn: '当前主机未响应，请更新主机地址。',
    },
    currentHost: {
      en: 'Current Host:',
      cn: '当前主机:',
    },
    saveAndRefresh: {
      en: 'Save and Refresh',
      cn: '保存并刷新',
    },
  },
  editSystemConfig: {
    fisConfig: {
      en: 'FIS Config',
      cn: 'FIS配置',
    },
    plcConfig: {
      en: 'PLC Config',
      cn: 'PLC配置',
    },
    editSystemConfig: {
      en: 'Edit System Config',
      cn: '编辑系统配置',
    },
    systemJsonConfig: {
      en: 'System JSON Config',
      cn: '系统JSON配置',
    },
    captureAgent: {
      en: 'Capture Agent',
      cn: '捕获模块',
    },
    captureAgentConfig: {
      en: 'Capture Agent JSON Config',
      cn: '捕获模块JSON配置',
    },
    sensorConfig: {
      en: 'Sensor JSON Config',
      cn: '传感器JSON配置',
    },
    componentDetector: {
      en: 'Component Detector',
      cn: '组件检测器',
    },
    componentDetectorConfig: {
      en: 'Component Detector JSON Config',
      cn: '组件检测器JSON配置',
    },
    inferenceAgent: {
      en: 'Inference Agent',
      cn: '推断模块',
    },
    defectConfig: {
      en: 'Defect JSON Config',
      cn: '缺陷JSON配置',
    },
    depthDiffConfig: {
      en: 'Depth Difference JSON Config',
      cn: '高度差异JSON配置',
    },
    inspection: {
      en: 'Inspection',
      cn: '检测',
    },
    inspectionConfig: {
      en: 'Inspection JSON Config',
      cn: '检测JSON配置',
    },
    system: {
      en: 'System',
      cn: '系统',
    },
    restartReminder: {
      en: 'System restart reminder',
      cn: '系统重启提醒',
    },
    restartReminderDesc: {
      en: 'Please restart the system after editing the system configuration to make the changes effective.',
      cn: '编辑系统配置后，请重新启动系统以使更改生效。',
    },
    dicoveredCameras: {
      en: 'Discovered Cameras',
      cn: '发现的相机',
    },
  },
  trainingInProgress: {
    modelRetrainingInProgress: {
      en: 'Model Re-training in Progress',
      cn: '模型重新训练中',
    },
    theSystemIsTemporary: {
      en: 'The system is temporarily unavailable as we’re re-training the model to improve results. ',
      cn: '系统暂时不可用，我们正在重新训练模型以改进结果。',
    }
  },
  dataStorageManage: {
    previousArchiveNotCompleted: {
      en: 'Previous archive is not completed, please wait for it to finish.',
      cn: '前一个归档未完成，请等待其完成。',
    },
    startDataArchive: {
      en: 'Start Data Archive',
      cn: '开始数据归档',
    },
    archiveAllDataDesc: {
      en: `NOTE: this action will cause running inspection to be slower around 5% due to disk I/O, and new data archive action can not be executed if previous one is still running.`,
      cn: '注意: 因为磁盘I/O，此操作将导致运行检测速度降低约5%，如果前一个归档操作仍在运行，则不能执行新的数据归档操作。',
    },
    execute: {
      en: 'Execute',
      cn: '执行',
    },
    archiveAllData: {
      en: 'Archive All Data',
      cn: '归档所有数据',
    },
    dataStorageManage: {
      en: 'Data Storage Manage',
      cn: '数据存储管理',
    },
    scanAndCleanUpCaptured: {
      en: `System will check all captured images and point clouds' life cycle every 24 hrs, it will be archived if the data is older than the retention period, 
      and only keeping images of inspected products for future reference`,
      cn: '系统每24小时检查所有捕获的图像和点云的生命周期，如果数据超过保留期限则将归档数据，并仅保留检测产品的图像以供将来参考',
    },
    notedArchivedImages: {
      en: 'Warning: You are not able to view archived images in AOI software',
      cn: '注意：您无法在AOI软件中查看已归档的图像',
    },
    dataRetentionPeriod: {
      en: 'Data Retention Period',
      cn: '数据保留期限',
    },
    dataStoragePath: {
      en: 'Data Storage Path',
      cn: '数据存储路径',
    },
  },
};