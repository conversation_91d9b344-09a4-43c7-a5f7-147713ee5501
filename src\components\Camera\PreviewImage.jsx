import React, { useContext, useEffect, useRef, useState } from 'react';
import { systemApi } from '../../services/system';
import { Spin } from 'antd';
import _ from 'lodash';
import { CameraPreviewContext } from '../Context/Provider';
import { useDispatch, useSelector } from 'react-redux';
import { handleRequestFailed } from '../../common/util';
import ObjectContainImage from './ObjectContainImage';
import { setSelectedCameraView } from '../../actions/camera';
import SingleCameraPreviewViewer from '../common/viewer/SingleCameraPreviewViewer';
import { serverEndpoint } from '../../common/const';


const PreviewImage = (props) => {
  const {
    frames,
    setLayoutMode,
    layoutMode,
    isContinuousCaptureOn,
  } = props;

  const dispatch = useDispatch();

  const containerRef = useRef();
  const viewer = useRef();
  const singlePrevgiewCanvesRef = useRef();
  const [cameraPreviewImageData, setCameraPreviewImageData] = useState(null);

  const { data: systemMetadata } = useSelector((state) => systemApi.endpoints.getSystemMetadata.select()(state));
  const selectedCameraView = useSelector((state) => state.camera.selectedCameraView);

  // const {
  //   cameraPreviewImageData,
  // } = useContext(CameraPreviewContext);

  const fetchAndDecode2dData = async (imgUri) => {
    let dataRes;
    try {
      dataRes = await fetch(`${serverEndpoint}/data?data_uri=${imgUri}`);
    } catch (error) {
      handleRequestFailed('fetchImageData', error);
      return;
    }

    const blob = await dataRes.blob();
    const curImage = URL.createObjectURL(blob);
    setCameraPreviewImageData(curImage);
    // viewer.current.loadScene(curImage, containerRef.current.offsetWidth, containerRef.current.offsetHeight);
  };

  useEffect(() => {
    if (_.isEmpty(frames) || _.isNull(selectedCameraView)) return;
    if (!_.get(frames, `${selectedCameraView}.imageUri`)) return;
    fetchAndDecode2dData(_.get(frames, `${selectedCameraView}.imageUri`));
  }, [frames, selectedCameraView]);

  // use camera layout from system metadata to render camera preview
  if (_.isEmpty(_.get(systemMetadata, 'camera_view_layout'))) return <div>camera view layout does not exist</div>;

  return (
    <div className='flex flex-col w-full h-full gap-1 self-stretch bg-[#000000]' ref={containerRef}>
      {(layoutMode === 'grid' && _.isNull(selectedCameraView)) ? _.map(_.get(systemMetadata, 'camera_view_layout'), (cols, row) => (
        <div
          className={`grid gap-x-1 self-stretch h-full`}
          style={{ gridTemplateColumns: `repeat(${cols}, 1fr)` }}
        >
          {_.times(cols, (col) => (
            <ObjectContainImage
              key={`camera-view-${row}-${col}`}
              cameraId={row + col}
              onClick={() => {
                dispatch(setSelectedCameraView(row + col));
                setLayoutMode('detail');
              }}
              isFullView={true}
              // cameraPreviewImageData={cameraPreviewImageData}
              uri={_.get(frames, `${row + col}.imageUri`)}
            />
          ))}
        </div>
      )) : (
        <div className='relative w-full h-full'>
          <div className='absolute top-0 left-0 w-full h-full z-[1]'>
            <SingleCameraPreviewViewer
              ref={viewer}
              cameraId={selectedCameraView}
              // viewerDimension={{ width: containerRef.current.clientWidth, height: containerRef.current.clientHeight }}
              displayCanvasRef={singlePrevgiewCanvesRef}
              cameraPreviewImageData={cameraPreviewImageData}
              isContinuousCaptureOn={isContinuousCaptureOn}
            />
          </div>
          <div className='absolute bottom-5 left-5 z-[10]'>
            <div
              className='flex w-6 h-6 items-center justify-center cursor-pointer bg-[#2D2D2D] rounded-[4px]'
              onClick={() => { if (viewer.current) viewer.current.resetView(); }}
            >
              <img className='w-3 h-3' src='/img/icn/icn_resetZoom_white.svg' alt='pencil' />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PreviewImage;