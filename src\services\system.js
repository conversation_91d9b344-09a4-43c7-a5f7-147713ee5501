import { createApi } from '@reduxjs/toolkit/query/react';
import { baseQuery } from './api';


export const systemApi = createApi({
  reducerPath: 'systemApi',
  baseQuery,
  tagTypes: ['System'],
  endpoints: (build) => ({
    getSystemMetadata: build.query({
      query: () => ({
        url: '/system',
        method: 'GET',
      }),
      providesTags: ['System'],
    }),
    exportInspection: build.mutation({
      query: (body) => ({
        url: '/exportInspections',
        method: 'POST',
        body,
      }),
    }),
    exportSession: build.mutation({
      query: (body) => ({
        url: '/exportSessions',
        method: 'POST',
        body,
      }),
    }),
    getAllSystemConfigFiles: build.query({
      query: () => ({
        url: '/configFiles',
        method: 'GET',
      }),
    }),
    updateAllSystemConfigFiles: build.mutation({
      query: (body) => ({
        url: '/configFiles',
        method: 'POST',
        body,
      }),
    }),
    getDataArchiveConfig: build.query({
      query: () => ({
        url: '/dataArchiveConfig',
        method: 'GET',
      }),
    }),
    putDataArchiveConfig: build.mutation({
      query: (body) => ({
        url: '/dataArchiveConfig',
        method: 'PUT',
        body,
      }),
    }),
    getErrorQueue: build.query({
      query: () => '/errorQueue',
    }),
    commitErrorQueue: build.mutation({
      query: () => ({
        url: '/commitErrorQueue',
        method: 'POST',
      }),
    }),
    archiveAllData: build.mutation({
      query: () => ({
        url: '/dataArchive',
        method: 'POST',
      }),
    }),
    getInspectionCounts: build.query({
      query: () => ({
        url: '/inspectionCounts',
        method: 'GET',
      }),
    }),
    putInspectionCounts: build.mutation({
      query: (body) => ({
        url: '/inspectionCounts',
        method: 'PUT',
        body,
      }),
    }),
    getContinuousDefectThreshold: build.query({
      query: () => ({
        url: '/continuousDefectThreshold',
        method: 'GET',
      }),
    }),
    putContinuousDefectThreshold: build.mutation({
      query: (body) => ({
        url: '/continuousDefectThreshold',
        method: 'PUT',
        body,
      }),
    }),
  }),
});

export const {
  useGetSystemMetadataQuery,
  useLazyGetSystemMetadataQuery,
  useExportInspectionMutation,
  useExportSessionMutation,
  useLazyGetAllSystemConfigFilesQuery,
  useUpdateAllSystemConfigFilesMutation,
  useLazyGetDataArchiveConfigQuery,
  usePutDataArchiveConfigMutation,
  useLazyGetErrorQueueQuery,
  useCommitErrorQueueMutation,
  useArchiveAllDataMutation,
  useLazyGetInspectionCountsQuery,
  usePutInspectionCountsMutation,
  useLazyGetContinuousDefectThresholdQuery,
  usePutContinuousDefectThresholdMutation,
} = systemApi;