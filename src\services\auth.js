import { createApi } from '@reduxjs/toolkit/query/react';
import { baseQuery } from './api';


export const authApi = createApi({
  baseQuery,
  tagTypes: ['Auth'],
  endpoints: (build) => ({
    login: build.mutation({
      query: (data) => ({
        url: '/auth/login',
        method: 'POST',
        body: data,
      }),
    }),
    logout: build.mutation({
      query: () => ({
        url: '/auth/logout',
        method: 'POST',
      }),
    }),
    signup: build.mutation({
      query: (data) => ({
        url: '/auth/signup',
        method: 'POST',
        body: data,
      }),
    }),
  }),
});

export const {
  useLoginMutation,
  useLogoutMutation,
  useSignupMutation,
} = authApi;