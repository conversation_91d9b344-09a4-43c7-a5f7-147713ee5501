import { createSlice } from '@reduxjs/toolkit';


// NOTE: this is not rtk query, this is a normal slice
const initialState = {
  selectedCameraView: null,
  isCoordSystemSelectingPoints: false,
  cameraSettingCustomCoordSystemPoints: [],
  curCoordSystemSelectingPointIndex: null,
  curCoordSystemSelectedPointPosition: null,
};

const camera = createSlice({
  name: 'camera',
  initialState,
  reducers: {
    setSelectedCameraView(state, action) {
      state.selectedCameraView = action.payload;
    },
    setIsCoordSystemSelectingPoints(state, action) {
      state.isCoordSystemSelectingPoints = action.payload;
    },
    setCameraSettingCustomCoordSystemPoints(state, action) {
      state.cameraSettingCustomCoordSystemPoints = action.payload;
    },
    setCurCoordSystemSelectingPointIndex(state, action) {
      state.curCoordSystemSelectingPointIndex = action.payload;
    },
    setCurCoordSystemSelectedPointPosition(state, action) {
      state.curCoordSystemSelectedPointPosition = action.payload;
    },
  },
});

export const {
  setSelectedCameraView,
  setIsCoordSystemSelectingPoints,
  setCameraSettingCustomCoordSystemPoints,
  setCurCoordSystemSelectingPointIndex,
  setCurCoordSystemSelectedPointPosition,
} = camera.actions;
export default camera.reducer;