import { Button, InputN<PERSON>ber, Modal, Tooltip } from 'antd';
import React, { useEffect, useState } from 'react';
import {
  getTimeSinceMidnight, handleRequestFailed,
  toLocalISOString,
  translation,
} from '../../common/util';
import { useDispatch, useSelector } from 'react-redux';
import { setAllowedNGAmount, setDailyIpcCountResetTime, setProductPassRates, setTodayGoodManualChangeOffset, setTodayTotalManualChangeOffset } from '../../actions/setting';
import { PrimaryButtonConfigProvider } from '../../common/darkModeComponents';
import { useLazyGetAllInspectionsQuery } from '../../services/session';
import {
  useLazyGetContinuousDefectThresholdQuery,
  usePutContinuousDefectThresholdMutation,
  usePutInspectionCountsMutation,
} from '../../services/system';
import { useResetSessionMutation } from '../../services/product';
import { serverEndpoint } from '../../common/const';
import _ from 'lodash';
import { useLazyGetInferenceStatusQuery } from '../../services/product';


const AdjustPassRate = (props) => {
  const {
    isOpened,
    setIsOpened,
  } = props;

  const dispatch = useDispatch();

  const [newPassingRate, setNewPassingRate] = useState(0);
  const [newNgProductTolarance, setNewNgProductTolarance] = useState(0);
  const [isHiddenSettingsVis, setIsHiddenSettingsVis] = useState(false);

  const [curGoodCount, setCurGoodCount] = useState(0);
  const [curTotalCount, setCurTotalCount] = useState(0);
  const [newTotalIpcCount, setNewTotalIpcCount] = useState(0);
  const [newTotalGoodCount, setNewTotalGoodCount] = useState(0);
  const [selectedResetHour, setSelectedResetHour] = useState(0);
  const [continuousDefectThreshold, setContinuousDefectThreshold] = useState(0);

  const [realTotalCount, setRealTotalCount] = useState(0);
  const [realGoodCount, setRealGoodCount] = useState(0);

  const [getInspections] = useLazyGetAllInspectionsQuery();
  const [getContinuousThreshold] = useLazyGetContinuousDefectThresholdQuery();
  const [putContinuousThreshold] = usePutContinuousDefectThresholdMutation();
  const [putInspectionCounts] = usePutInspectionCountsMutation();
  const [getInferenceStatus] = useLazyGetInferenceStatusQuery();
  const [resetSession] = useResetSessionMutation();

  const productPassRates = useSelector((state) => state.setting.productPassRates);
  const allowedNGAmount = useSelector((state) => state.setting.allowedNGAmount);
  const dailyIpcCountResetTime = useSelector((state) => state.setting.dailyIpcCountResetTime);

  useEffect(() => {
    if (!isOpened) {
      setIsHiddenSettingsVis(false);
      return;
    }

    const init = async (resetTime) => {
      // init total/ good/ ng count
      // init time range filter
      // fetch all inspections
      const curResetTimeObj = new Date(resetTime);
      const currentDateTime = new Date();
      setSelectedResetHour(curResetTimeObj.getHours());

      // get time range filter's start and end datetime
      // start: should always be curResetTimeObj's time
      // if curTimeInMs - resetTimeInMs < 0, then start: yesterday's date + curResetTimeObj's time
      // if curTimeInMs - resetTimeInMs > 0, then start: today's date + curResetTimeObj's time
      // end: current time + current date
      let start_datetime = null;
      let end_datetime = null;

      const curTimeInMs = getTimeSinceMidnight(currentDateTime);
      const resetTimeInMs = getTimeSinceMidnight(curResetTimeObj);

      if (0 < (curTimeInMs - resetTimeInMs)) {
        const today = new Date();
        start_datetime = today;
      } else if ((curTimeInMs - resetTimeInMs) < 0) {
        const yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 1);
        start_datetime = yesterday;
      }
      start_datetime.setHours(curResetTimeObj.getHours());
      start_datetime.setMinutes(curResetTimeObj.getMinutes());
      start_datetime.setSeconds(curResetTimeObj.getSeconds());
      start_datetime = toLocalISOString(start_datetime);
      end_datetime = toLocalISOString(currentDateTime);
      // start_datetime = start_datetime.toISOString();
      // end_datetime = currentDateTime.toISOString();

      // const totalRes = await getInspections({
      //   limit: 1,
      //   // page: 0,
      //   start_datetime: start_datetime,
      //   end_datetime: end_datetime,
      // });
 
      // if (totalRes.error) {
      //   console.error('Failed to get total count');
      //   return;
      // }

      // const defectiveRes = await getInspections({
      //   limit: 1,
      //   // page: 0,
      //   start_datetime: start_datetime,
      //   end_datetime: end_datetime,
      //   defect: 1,
      // });

      // if (defectiveRes.error) {
      //   console.error('Failed to get defective count');
      //   return;
      // }

      const inferenceRes = await getInferenceStatus();

      if (inferenceRes.error) {
        handleRequestFailed('getInferenceStatus', inferenceRes.error);
        console.error('Failed to get inference status');
        return;
      }

      const thresholdRes = await getContinuousThreshold();
      if (!thresholdRes.error) {
        setContinuousDefectThreshold(_.get(thresholdRes, 'data.threshold', 0));
      }

      setRealTotalCount(_.get(inferenceRes, 'data.inspected_count_real', 0));
      setRealGoodCount(_.get(inferenceRes, 'data.inspected_count_real', 0) - _.get(inferenceRes, 'data.defect_count_real', 0));

      // setNewTotalIpcCount(_.get(totalRes, 'data.pageCount', 0));
      // setCurGoodCount(_.get(totalRes, 'data.pageCount', 0) - _.get(defectiveRes, 'data.pageCount', 0));
      // setCurTotalCount(_.get(totalRes, 'data.pageCount', 0));
      // setNewTotalGoodCount(_.get(totalRes, 'data.pageCount', 0) - _.get(defectiveRes, 'data.pageCount', 0));
      setNewTotalIpcCount(_.get(inferenceRes, 'data.inspected_count', 0));
      setCurGoodCount(_.get(inferenceRes, 'data.inspected_count', 0) - _.get(inferenceRes, 'data.defect_count', 0));
      setCurTotalCount(_.get(inferenceRes, 'data.inspected_count', 0));
      setNewTotalGoodCount(_.get(inferenceRes, 'data.inspected_count', 0) - _.get(inferenceRes, 'data.defect_count', 0));
    };

    const initState = async () => {
      await init(dailyIpcCountResetTime);

      try {
        const rateRes = await fetch(`${serverEndpoint}/targetPassRate`);
        if (rateRes.ok) {
          const data = await rateRes.json();
          setNewPassingRate(_.get(data, 'pass_rate', productPassRates / 100) * 100);
        } else {
          setNewPassingRate(productPassRates);
        }
      } catch {
        setNewPassingRate(productPassRates);
      }

      try {
        const resetRes = await fetch(`${serverEndpoint}/counterResetHour`);
        if (resetRes.ok) {
          const data = await resetRes.json();
          setSelectedResetHour(_.get(data, 'hour', selectedResetHour));
        }
      } catch {
        // ignore
      }

      setNewNgProductTolarance(allowedNGAmount);
    };

    initState();
  }, [isOpened]);

  return (
    <Modal
      open={isOpened}
      onCancel={() => setIsOpened(false)}
      title={
        <span className='font-source text-[16px] font-semibold'>
          {translation('adjustPassingRate.adjustPassingRate')}
        </span>
      }
      footer={
        <div className='flex p-4 gap-2 slef-stretch items-start justify-center'>
          <Button
            style={{ width: '50%' }}
            onClick={() => setIsOpened(false)}
          >
            <span className='font-source text-[12px] font-normal'>
              {translation('common.cancel')}
            </span>
          </Button>
          <PrimaryButtonConfigProvider>
            <Button
              style={{ width: '50%' }}
              onClick={async () => {
                try {
                  await fetch(`${serverEndpoint}/targetPassRate`, {
                    method: 'PUT',
                    body: JSON.stringify({ pass_rate: newPassingRate / 100 }),
                  });
                } catch (e) {
                  console.error(e);
                }

                await fetch(`${serverEndpoint}/counterResetHour`, {
                  method: 'PUT',
                  headers: {
                    'Content-Type': 'application/json',
                  },
                  body: JSON.stringify({ hour: selectedResetHour }),
                });
                dispatch(setProductPassRates(newPassingRate));
                dispatch(setAllowedNGAmount(newNgProductTolarance));

                // update offset
                dispatch(setTodayGoodManualChangeOffset(newTotalGoodCount - curGoodCount));
                dispatch(setTodayTotalManualChangeOffset(newTotalIpcCount - curTotalCount));

                // update reset time
                dispatch(setDailyIpcCountResetTime(new Date().setHours(selectedResetHour, 0, 0, 0)));

                putInspectionCounts({
                  total_passed: newTotalGoodCount,
                  total_inspected: newTotalIpcCount,
                });

                putContinuousThreshold({ threshold: continuousDefectThreshold });

                setIsOpened(false);

                window.location.reload();
              }}
            >
              <span className='font-source text-[12px] font-normal'>
                {translation('common.save')}
              </span>
            </Button>
          </PrimaryButtonConfigProvider>
        </div>
      }
    >
      <div className='flex py-6 px-4 flex-col items-start gap-8 self-stretch h-[184px] overflow-y-auto'>
        <div className='flex flex-col items-start gap-2 self-stretch'>
          <div className='flex items-center gap-2 self-stretch'>
            <span className='font-source text-[12px] font-normal flex-1'>
              {translation('adjustPassingRate.newPassingRate')}
            </span>
            <InputNumber
              controls={false}
              min={0}
              max={100}
              value={newPassingRate}
              onChange={(value) => setNewPassingRate(value)}
              addonAfter='%'
              style={{ width: '50%' }}
            />
          </div>
          {/* <div className='flex items-center gap-2 self-stretch'>
            <span className='font-source text-[12px] font-normal flex-1'>
              {translation('adjustPassingRate.ngProductTolarance')}
            </span>
            <InputNumber
              controls={false}
              min={0}
              step={1}
              precision={0}
              value={newNgProductTolarance}
              onChange={(value) => setNewNgProductTolarance(value)}
              style={{ width: '50%' }}
            />
          </div> */}
          <div className='flex items-center gap-2 self-stretch'>
            <span className='font-source text-[12px] font-normal flex-1'>
              {translation('adjustPassingRate.continuousDefectThreshold')}
            </span>
            <InputNumber
              controls={false}
              min={0}
              step={1}
              precision={0}
              value={continuousDefectThreshold}
              onChange={(value) => setContinuousDefectThreshold(value)}
              style={{ width: '50%' }}
            />
          </div>
        </div>
        {isHiddenSettingsVis && (
          <div className='flex flex-col items-start gap-2 self-stretch'>
            <span className='font-source text-[14px] font-normal'>
              {translation('adjustPassingRate.adjustIpcCountTitle')}
            </span>
            <div className='flex items-center gap-2 self-stretch'>
              <span className='font-source text-[12px] font-normal flex-1'>
                {translation('adjustPassingRate.adjustTotalIpcCount')}
              </span>
              <InputNumber
                controls={false}
                min={0}
                step={1}
                precision={0}
                value={newTotalIpcCount}
                onChange={(value) => setNewTotalIpcCount(value)}
                style={{ width: '50%' }}
              />
            </div>
            <div className='flex items-center gap-2 self-stretch'>
              <span className='font-source text-[12px] font-normal flex-1'>
                {translation('adjustPassingRate.adjustTotalGoodCount')}
              </span>
              <InputNumber
                controls={false}
                min={0}
                step={1}
                precision={0}
                value={newTotalGoodCount}
                onChange={(value) => setNewTotalGoodCount(value)}
                style={{ width: '50%' }}
              />
            </div>
            <div className='flex items-center gap-2 self-stretch'>
              <span className='font-source text-[12px] font-normal flex-1'>
                {translation('adjustPassingRate.adjustCounterResetTime')}
              </span>
              <InputNumber
                controls={false}
                min={0}
                max={23}
                value={selectedResetHour}
                onChange={(value) => setSelectedResetHour(value)}
                style={{ width: '50%' }}
              />
            </div>
            <Button
              style={{ width: '100%' }}
              onClick={async () => {
                await resetSession();
                // set all offset to 0
                // dispatch(setTodayGoodManualChangeOffset(0));
                // dispatch(setTodayTotalManualChangeOffset(0));
                setNewTotalIpcCount(realTotalCount);
                setNewTotalGoodCount(realGoodCount);
              }}
            >
              <span className='font-source text-[12px] font-normal'>
                {translation('adjustPassingRate.useRealDateForDisplay')}
              </span>
            </Button>
          </div>
        )}
        <Button
          style={{ width: '100%' }}
          onClick={() => setIsHiddenSettingsVis(!isHiddenSettingsVis)}
        >
          <img src='/img/icn/icn_ellipsis_white.svg' className='w-4 h-4' alt='check-circle' />
        </Button>
      </div>
    </Modal>
  );
};

export default AdjustPassRate;