import { createApi } from '@reduxjs/toolkit/query/react';
import { baseQuery } from './api';
import { serverEndpoint } from '../common/const';
import _ from 'lodash';


export const frameApi = createApi({
  reducerPath: 'frameApi',
  baseQuery,
  tagTypes: ['Frame'],
  endpoints: (build) => ({
    getFrameByProductIdAndStep: build.query({
      // query: ({ product_id, step, variant }) => ({
      //   url: '/frame',
      //   method: 'GET',
      //   params: { product_id, step, variant },
      // }),
      async queryFn(arg, queryApi, extraOptions, baseQuery) {
        let response;
        try {
          // space will be encoded to + for some reason in rtk query
          if (!_.isUndefined(arg.variant)) {
            const encodedVariantName = encodeURIComponent(arg.variant);
            response = await fetch(`${serverEndpoint}/frame?product_id=${arg.product_id}&step=${arg.step}&variant=${encodedVariantName}`, {
              method: 'GET',
            });
          } else {
            response = await fetch(`${serverEndpoint}/frame?product_id=${arg.product_id}&step=${arg.step}`, {
              method: 'GET',
            });
          }

          if (!response.ok) {
            return { error: { status: response.status, message: response.statusText } };
          }

          const data = await response.json();
          return { data };
        } catch (error) {
          return { error: { status: 'FETCH_ERROR', message: error.message } };
        }
      },
      transformResponse: (response, meta, arg) => {
        return { ...response, product_id: arg.product_id, step: arg.step };
      },
      providesTags: (result, error, arg) => [{ type: 'Frame', product_id: arg.product_id, step: arg.step }],
    }),
  }),
});

export const {
  useGetFrameByProductIdAndStepQuery,
  useLazyGetFrameByProductIdAndStepQuery,
} = frameApi;