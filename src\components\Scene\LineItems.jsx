import { Checkbox, InputNumber, Select, Switch, Tooltip } from 'antd';
import _ from 'lodash';
import React, { Fragment, useEffect, useState } from 'react';
import { useAddFeatureByProductIdAndStepMutation, useUpdateFeatureByProductIdAndStepMutation } from '../../services/feature';
import { useDispatch } from 'react-redux';
import { setContainerWindowLoadingLocked } from '../../actions/setting';
import { handleRequestFailed, translation } from '../../common/util';
import { ALERT_TYPES, aoiAlert } from '../../common/alert';


const LineItems = (props) => {
  const {
    systemMetadataData,
    lineItemParams: curDefaultLineItems,
    setLineItemParams,
  } = props;

  // const test = {
  //   "defect_detection": {
  //       "name": "defect_detection",
  //       "agent_name": "defect_detection",
  //       "enabled": true,
  //       "params": {
  //           "sensitivity": {
  //               "description": "Sensitivity of the defect detection algorithm",
  //               "param_int": {
  //                   "value": 5,
  //                   "max": 10,
  //                   "min": 0
  //               },
  //               "param_float": null,
  //               "param_enum": null,
  //               "param_bool": null
  //           },
  //           "enable_box_fitting": {
  //               "description": "Maximum rotation angle of the box in degrees",
  //               "param_int": null,
  //               "param_float": null,
  //               "param_enum": null,
  //               "param_bool": true
  //           }
  //       }
  //   },
  //   "height_diff": {
  //     "name": "height_diff",
  //     "agent_name": "height_diff",
  //     "enabled": false,
  //     "params": {
  //         "sensitivity": {
  //             "description": null,
  //             "param_int": {
  //                 "value": 5,
  //                 "max": 10,
  //                 "min": 0
  //             },
  //             "param_float": null,
  //             "param_enum": null,
  //             "param_bool": null
  //         }
  //     }
  //   }
  // };

  const handleEnableParam = async (agentName, enabled, curDefaultLineItems) => {
    if (_.isEmpty(curDefaultLineItems)) return;

    let lineItems = _.cloneDeep(curDefaultLineItems);
    lineItems = _.set(lineItems, `${agentName}.enabled`, enabled);

    setLineItemParams(lineItems);

    return;
  };

  useEffect(() => {
    if (_.isEmpty(systemMetadataData)) return;

    // reconstruct default lineItemParams
    setLineItemParams(_.get(systemMetadataData, 'default_line_items', {}));
  }, [systemMetadataData]);

  return (
    <div className='flex flex-col gap-4 w-full'>
      {_.map(_.keys(curDefaultLineItems), (agentName) =>
        <div className='flex gap-2 self-stretch w-full flex-col'>
          <div className='flex gap-2 items-center'>
            <Checkbox
              checked={_.get(curDefaultLineItems, `${agentName}.enabled`, false)}
              onChange={(e) => {
                handleEnableParam(agentName, e.target.checked, curDefaultLineItems);
              }}
            />
            <span className='font-source text-[12px] font-normal whitespace-nowrap'>
              {translation(`productAnnotation.agentName.${agentName}`)}
            </span>
          </div>
          <div className='flex flex-col gap-1 pl-6'>
            {_.map(_.keys(_.get(curDefaultLineItems, `${agentName}.params`, {})), (paramName) =>
              <SingleParam
                paramName={paramName}
                data={_.get(curDefaultLineItems, `${agentName}.params.${paramName}`, {})}
                agentName={agentName}
                lineItems={curDefaultLineItems}
                updateLineItems={setLineItemParams}
              />
            )}
          </div>
        </div>
      )}
    </div>
  );
};

const SingleParam = (props) => {
  const {
    paramName,
    data,
    agentName,
    lineItems,
    updateLineItems,
  } = props;

  const handleUpdateParam = async (lineItems, paramName, agentName, paramType, newValue) => {
    let curLineItems = _.cloneDeep(lineItems);
    curLineItems = _.set(curLineItems, `${agentName}.params.${paramName}.${paramType}`, newValue);
    updateLineItems(curLineItems);
  };

  return (
    <div className='flex items-center gap-2 justify-between'>
      <div className='flex items-center gap-1 self-stretch'>
        <span className='font-source text-[12px] font-normal whitespace-nowrap'>
          {translation(`productAnnotation.paramName.${agentName}.${paramName}.title`)}
        </span>
        <Tooltip title={
          <span className='font-source text-[12px] font-normal'>
            {translation(`productAnnotation.paramName.${agentName}.${paramName}.desc`)}
          </span>
        }>
          <img src='/img/icn/icn_info_white.svg' className='w-[12px] h-[12px]' />
        </Tooltip>
      </div>
      <div className='flex flex-1 justify-end self-stretch'>
        {!_.isNull(_.get(data, 'param_int', null)) &&
          <InputNumber
            style={{ width: '30px' }}
            controls={false}
            size='small'
            min={_.get(data, 'param_int.min', 0)}
            max={_.get(data, 'param_int.max', 0)}
            value={_.get(data, 'param_int.value', 0)}
            precision={0}
            onBlur={(e) => {
              // max and min works in onChange, not onBlur
              // manually validate here
              const value = Number(e.target.value);
              if (!_.isInteger(value)) {
                aoiAlert(translation('notification.error.pleaseEnterAValidInteger'), ALERT_TYPES.COMMON_ERROR);
                return
              }
              if (value < _.get(data, 'param_int.min', 0) || value > _.get(data, 'param_int.max', 0)) {
                aoiAlert(translation('notification.error.pleaseEnterANumberWithinTheRange'), ALERT_TYPES.COMMON_ERROR);
                return;
              }
              handleUpdateParam(
                lineItems,
                paramName,
                agentName,
                'param_int',
                {
                  ..._.get(data, 'param_int', {}),
                  value: Number(e.target.value),
                }
              );
            }}
          />
        }
        {!_.isNull(_.get(data, 'param_float', null)) &&
          <InputNumber
          style={{ width: '30px' }}
            controls={false}
            size='small'
            min={_.get(data, 'param_float.min', 0)}
            max={_.get(data, 'param_float.max', 0)}
            value={_.get(data, 'param_float.value', 0)}
            precision={6}
            onBlur={(e) => {
              // max and min works in onChange, not onBlur
              // manually validate here
              const value = Number(e.target.value);
              if (!_.isNumber(value)) {
                aoiAlert(translation('notification.error.pleaseEnterAValidNumber'), ALERT_TYPES.COMMON_ERROR);
                return
              }
              if (value < _.get(data, 'param_int.min', 0) || value > _.get(data, 'param_int.max', 0)) {
                aoiAlert(translation('notification.error.pleaseEnterANumberWithinTheRange'), ALERT_TYPES.COMMON_ERROR);
                return;
              }
              handleUpdateParam(
                lineItems,
                paramName,
                agentName,
                'param_float',
                {
                  ..._.get(data, 'param_float', {}),
                  value: Number(e.target.value),
                }
              );
            }}
          />
        }
        {!_.isNull(_.get(data, 'param_bool', null)) &&
          <Switch
            size='small'
            value={_.get(data, 'param_bool', false)}
            onChange={(v) => {
              handleUpdateParam(
                lineItems,
                paramName,
                agentName,
                'param_bool',
                v,
              );
            }}
          />
        }
        {!_.isNull(_.get(data, 'param_enum', null)) &&
          <Select
            size='small'
            value={_.get(data, 'param_enum.value', '')}
            options={_.map(_.get(data, 'param_enum.options', []), o => ({
              label: <span className='font-source text-[12px] font-normal'>{translation(`productAnnotation.paramName.${agentName}.${paramName}.options.${o}`)}</span>,
              value: o,
            }))}
            onChange={(v) => {
              handleUpdateParam(
                lineItems,
                paramName,
                agentName,
                'param_enum',
                v,
              );
            }}
          />
        }
      </div>
    </div>
  );
};

export default LineItems;